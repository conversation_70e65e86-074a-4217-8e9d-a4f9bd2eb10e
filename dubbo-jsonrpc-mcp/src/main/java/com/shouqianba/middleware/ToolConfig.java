package com.shouqianba.middleware;

import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;


@Component
public class ToolConfig {
    @Bean
    public ToolCallbackProvider myTools(WeatherService weatherService, MerchantAuditService merchantAuditService) {
        return MethodToolCallbackProvider.builder()
                .toolObjects(weatherService, merchantAuditService)
                .build();
    }
}