package com.shouqianba.middleware;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.shouqianba.middleware.jsonrpc4j.ng.rpc.JsonRpcClient;
import lombok.SneakyThrows;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
public class MerchantAuditService {
    private final JsonRpcClient merchantAuditService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public MerchantAuditService(@Qualifier("merchant-audit-service") JsonRpcClient merchantAuditService) {
        this.merchantAuditService = merchantAuditService;
    }

    @Tool(name = "getAuditDetail", description = "根据id查询商户审批详情")
    @SneakyThrows
    public String getAuditDetail(@ToolParam(description = "审批id") int id) {
        return objectMapper.writeValueAsString(this.merchantAuditService.invoke("getAuditById", new Object[]{id}));
    }
}
