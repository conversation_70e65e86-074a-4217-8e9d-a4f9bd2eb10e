<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<!-- $Id: pom.xml 642118 2008-03-28 08:04:16Z reinhard $ -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <artifactId>optional-spring-plugins</artifactId>
        <groupId>com.wosai.middleware</groupId>
        <version>1.14.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hera-springmvc-annotation-6.x-plugin</artifactId>
    <packaging>jar</packaging>

    <name>mvc-annotation-6.x-plugin</name>
    <url>http://maven.apache.org</url>

    <properties>
        <spring-core.version>6.0.0</spring-core.version>
        <spring-webmvc.version>6.0.0</spring-webmvc.version>
        <spring-webflux.version>6.0.0</spring-webflux.version>
        <jakarta-servlet-api.version>6.0.0</jakarta-servlet-api.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sdk.plugin.related.dir>/../..</sdk.plugin.related.dir>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${spring-core.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring-webmvc.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>${jakarta-servlet-api.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-springmvc-annotation-commons</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-springmvc-annotation-6.x-plugin</artifactId>
            <version>${apache.skywalking.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
            <version>${spring-webflux.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
