<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>optional-spring-plugins</artifactId>
        <groupId>com.wosai.middleware</groupId>
        <version>1.14.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hera-resttemplate-6.x-plugin</artifactId>
    <packaging>jar</packaging>

    <name>resttemplate-6.x-plugin</name>
    <url>http://maven.apache.org</url>

    <properties>
        <spring-web.version>6.0.0</spring-web.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sdk.plugin.related.dir>/../..</sdk.plugin.related.dir>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring-web.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-spring-commons</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>