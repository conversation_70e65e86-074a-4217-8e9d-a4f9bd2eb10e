package com.wosai.middleware.hera.plugin.spring.webflux.v5;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import com.wosai.middleware.hera.tracing.tag.Tags;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.HandlerMapping;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.ServerWebExchangeDecorator;
import org.springframework.web.server.adapter.DefaultServerWebExchange;
import com.wosai.middleware.hera.plugin.spring.webflux.v5.define.SpringHttpHeadersExtractAdapter;
import org.springframework.http.HttpHeaders;
import org.springframework.web.util.pattern.PathPattern;
import reactor.core.publisher.Mono;

import java.lang.reflect.Method;

import static org.apache.skywalking.apm.network.trace.component.ComponentsDefine.SPRING_WEBFLUX;

public class DispatcherHandlerHandleMethodInterceptor implements InstanceMethodsAroundInterceptor {

    private static final String SPAN_KEY_NAME = "HERA_SPAN";
    private static final String ACTIVE_SPAN_KEY_NAME = "HERA_ACTIVE_SPAN";

    public static EnhancedInstance getInstance(Object o) {
        EnhancedInstance instance = null;
        if (o instanceof DefaultServerWebExchange) {
            instance = (EnhancedInstance) o;
        } else if (o instanceof ServerWebExchangeDecorator) {
            ServerWebExchange delegate = ((ServerWebExchangeDecorator) o).getDelegate();
            return getInstance(delegate);
        }
        return instance;
    }

    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, MethodInterceptResult methodInterceptResult) throws Throwable {
        EnhancedInstance instance = getInstance(objects[0]);
        ServerWebExchange exchange = (ServerWebExchange) objects[0];

        HttpHeaders httpHeaders = exchange.getRequest().getHeaders();
        HeraSpanContext heraSpanContext = ContextManager.extract(new SpringHttpHeadersExtractAdapter(httpHeaders));
        AbstractHeraSpan heraSpan;

        if (instance != null && instance.getSkyWalkingDynamicField() != null) {
            heraSpan = (AbstractHeraSpan) instance.getSkyWalkingDynamicField();
        } else {
            heraSpan = ContextManager.createEntrySpan(exchange.getRequest().getURI().getPath(), heraSpanContext);
        }

        Tags.COMPONENT.set(heraSpan, SPRING_WEBFLUX.getName());
        Tags.HTTP_URL.set(heraSpan, exchange.getRequest().getURI().toString());
        Tags.HTTP_METHOD.set(heraSpan, exchange.getRequest().getMethodValue());
        instance.setSkyWalkingDynamicField(ContextManager.activeSpan());
        heraSpan.prepareForAsync();
        ContextManager.stopSpan();

        exchange.getAttributes().put(SPAN_KEY_NAME, heraSpan);
    }

    private void maybeSetPattern(AbstractHeraSpan heraSpan, ServerWebExchange exchange) {
        if (heraSpan != null) {
            PathPattern pathPattern = exchange.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);

            if (pathPattern != null && pathPattern.matches(exchange.getRequest().getPath().pathWithinApplication())) {
                heraSpan.setOperationName(pathPattern.getPatternString());
            }
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Object o) throws Throwable {
        ServerWebExchange exchange = (ServerWebExchange) objects[0];
        AbstractHeraSpan heraSpan = (AbstractHeraSpan) exchange.getAttributes().get(SPAN_KEY_NAME);
        Mono<Void> monoReturn = (Mono<Void>) o;

        EnhancedInstance instance = getInstance(objects[0]);
        if (instance != null && instance.getSkyWalkingDynamicField() != null) {
            monoReturn = monoReturn.subscriberContext(
                    c -> c.put(ACTIVE_SPAN_KEY_NAME, instance.getSkyWalkingDynamicField())
            );
        }

        return monoReturn.doFinally(s -> {
            if (heraSpan != null) {
                maybeSetPattern(heraSpan, exchange);
                try {
                    HttpStatus httpStatus = exchange.getResponse().getStatusCode();
                    if (httpStatus != null) {
                        Tags.HTTP_STATUS.set(heraSpan, httpStatus.value());
                        if (httpStatus.isError()) {
                            ContextManager.markError(heraSpan);
                        }
                    }
                } finally {
                    heraSpan.asyncFinish();
                }
            }
        });
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {

    }
}
