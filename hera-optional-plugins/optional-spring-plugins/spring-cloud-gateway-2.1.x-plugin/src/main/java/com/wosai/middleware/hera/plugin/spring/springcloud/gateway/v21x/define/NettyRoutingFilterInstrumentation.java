package com.wosai.middleware.hera.plugin.spring.springcloud.gateway.v21x.define;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.InstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;
import org.apache.skywalking.apm.plugin.spring.cloud.gateway.v21x.define.AbstractGateway210EnhancePluginDefine;

import static org.apache.skywalking.apm.agent.core.plugin.match.NameMatch.byName;
import static net.bytebuddy.matcher.ElementMatchers.named;
import static org.apache.skywalking.apm.agent.core.plugin.bytebuddy.ArgumentTypeNameMatch.takesArgumentWithType;

public class NettyRoutingFilterInstrumentation extends AbstractGateway210EnhancePluginDefine {
    @Override
    protected ClassMatch enhanceClass() {
        return byName(Constants.INTERCEPT_CLASS_NETTY_ROUTING_FILTER);
    }

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[0];
    }

    @Override
    public InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints() {
        return new InstanceMethodsInterceptPoint[] {
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("filter").and(takesArgumentWithType(0, "org.springframework.web.server.ServerWebExchange"));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return Constants.NETTY_ROUTING_FILTER_INTERCEPTOR;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return true;
                    }
                }
        };
    }
}
