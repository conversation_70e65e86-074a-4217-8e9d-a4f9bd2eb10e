package com.wosai.middleware.hera.plugin.spring.springcloud.gateway.v21x;

import com.wosai.middleware.hera.plugin.spring.springcloud.gateway.v21x.define.EnhanceObjectCache;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;

public class HttpClientFinalizerConstructorInterceptor implements InstanceConstructorInterceptor {
    @Override
    public void onConstruct(EnhancedInstance enhancedInstance, Object[] objects) throws Throwable {
        EnhancedInstance tcpClient = (EnhancedInstance) objects[0];
        if (tcpClient.getSkyWalkingDynamicField() == null) {
            tcpClient.setSkyWalkingDynamicField(new EnhanceObjectCache());
        }

        enhancedInstance.setSkyWalkingDynamicField(tcpClient.getSkyWalkingDynamicField());
    }
}
