package com.wosai.middleware.hera.plugin.spring.springcloud.gateway.v21x;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.plugin.spring.springcloud.gateway.v21x.define.EnhanceObjectCache;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.reactivestreams.Publisher;
import reactor.core.publisher.Flux;
import reactor.core.publisher.SignalType;
import reactor.netty.Connection;
import reactor.netty.http.client.HttpClientResponse;

import java.lang.reflect.Method;
import java.util.function.BiFunction;

public class HttpClientFinalizerResponseConnectionInterceptor implements InstanceMethodsAroundInterceptor {

    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, MethodInterceptResult methodInterceptResult) throws Throwable {
        BiFunction<? super HttpClientResponse, ? super Connection, ? extends Publisher> finalReceiver = (BiFunction<? super HttpClientResponse, ? super Connection, ? extends Publisher>) objects[0];
        EnhanceObjectCache enhanceObjectCache = (EnhanceObjectCache) enhancedInstance.getSkyWalkingDynamicField();
        objects[0] = (BiFunction<HttpClientResponse, Connection, Publisher>) (response, connection) -> {
            Publisher publisher = finalReceiver.apply(response, connection);
            if (enhanceObjectCache.getSpan2() != null) {
                int statusCode = response.status().code();
                AbstractHeraSpan heraSpan = enhanceObjectCache.getSpan2();
                if (statusCode >= 400) {
                    ContextManager.markError(heraSpan);
                }
                Tags.HTTP_STATUS.set(heraSpan, statusCode);
            }
            return publisher;
        };
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Object o) throws Throwable {
        Flux<?> responseFlux = (Flux<?>) o;

        responseFlux = responseFlux
                .doOnError(e -> {
                    EnhanceObjectCache enhanceObjectCache = (EnhanceObjectCache) enhancedInstance.getSkyWalkingDynamicField();
                    if (enhanceObjectCache == null) {
                        return;
                    }
                    if (enhanceObjectCache.getSpan1() != null) {
                        AbstractHeraSpan heraSpan = enhanceObjectCache.getSpan1();
                        ContextManager.markError(heraSpan);
                        ContextManager.logError(heraSpan, e);
                    }
                }).doFinally(singleType -> {
                    EnhanceObjectCache enhanceObjectCache = (EnhanceObjectCache) enhancedInstance.getSkyWalkingDynamicField();
                    if (enhanceObjectCache == null) {
                        return;
                    }
                    if (enhanceObjectCache.getSpan1() != null) {
                        if (singleType == SignalType.CANCEL) {
                            ContextManager.markError(enhanceObjectCache.getSpan1());
                        }
                        enhanceObjectCache.getSpan1().asyncFinish();
                    }
                    if (enhanceObjectCache.getSpan2() != null) {
                        enhanceObjectCache.getSpan2().asyncFinish();
                    }
                });

        return responseFlux;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {
    }
}
