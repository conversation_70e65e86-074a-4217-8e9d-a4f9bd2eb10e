package com.wosai.middleware.hera.plugin.spring.springcloud.gateway.v21x.define;

public class DispatcherHandlerInstrumentation extends com.wosai.middleware.hera.plugin.spring.webflux.v5.define.DispatcherHandlerInstrumentation {
    @Override
    protected String[] witnessClasses() {
        return new String[] {
                "org.springframework.cloud.gateway.filter.LoadBalancerClientFilter",
                "org.springframework.cloud.gateway.config.GatewayEnvironmentPostProcessor"
        };
    }
}
