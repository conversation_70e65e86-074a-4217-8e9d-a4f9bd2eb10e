package com.wosai.middleware.hera.plugin.netty.http.utils;

import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.netty.handler.codec.http.HttpHeaders;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import com.wosai.middleware.hera.util.ExtraTags;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.plugin.netty.http.config.NettyHttpPluginConfig.Plugin.NettyHttp;
import org.apache.skywalking.apm.util.StringUtil;

public class HttpDataCollectUtils {
    private static final ILog LOGGER = LogManager.getLogger(org.apache.skywalking.apm.plugin.netty.http.utils.HttpDataCollectUtils.class);

    public HttpDataCollectUtils() {
    }

    public static void collectHttpRequestBody(HttpHeaders headers, ByteBuf content, AbstractHeraSpan heraSpan) {
        try {
            if (headers == null || content == null || heraSpan == null) {
                return;
            }

            if (Unpooled.EMPTY_BUFFER.equals(content)) {
                return;
            }

            String contentTypeValue = headers.get(HttpHeaderNames.CONTENT_TYPE);
            boolean needCollectHttpBody = false;
            String[] contentTypes = NettyHttp.SUPPORTED_CONTENT_TYPES_PREFIX.split(",");

            for (String contentType : contentTypes) {
                if (contentTypeValue.startsWith(contentType)) {
                    needCollectHttpBody = true;
                    break;
                }
            }

            if (needCollectHttpBody) {
                String bodyStr = content.toString(getCharsetFromContentType(headers.get(HttpHeaderNames.CONTENT_TYPE)));
                bodyStr = NettyHttp.FILTER_LENGTH_LIMIT > 0 ? StringUtil.cut(bodyStr, NettyHttp.FILTER_LENGTH_LIMIT) : bodyStr;
                heraSpan.setTag(ExtraTags.HTTP_BODY, bodyStr);
            }
        } catch (Exception e) {
            LOGGER.error("Fail to collect netty http request body", e);
        }

    }

    private static Charset getCharsetFromContentType(String contentType) {
        String[] parts = contentType.split(";");

        for (String s : parts) {
            String part = s;
            part = part.trim();
            if (part.startsWith("charset=")) {
                return Charset.forName(part.substring("charset=".length()));
            }
        }

        return StandardCharsets.UTF_8;
    }
}