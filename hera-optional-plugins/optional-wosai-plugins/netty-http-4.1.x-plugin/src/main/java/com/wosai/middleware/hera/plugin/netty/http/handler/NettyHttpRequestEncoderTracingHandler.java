/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.wosai.middleware.hera.plugin.netty.http.handler;

import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelOutboundHandlerAdapter;
import io.netty.channel.ChannelPromise;
import io.netty.handler.codec.http.HttpHeaders;
import io.netty.handler.codec.http.HttpRequest;
import io.netty.handler.codec.http.LastHttpContent;
import io.netty.handler.ssl.SslHandler;
import com.wosai.middleware.hera.tracing.tag.Tags;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import io.netty.util.AsciiString;
import com.wosai.middleware.hera.plugin.netty.http.common.AttributeKeys;
import com.wosai.middleware.hera.plugin.netty.http.utils.HttpDataCollectUtils;
import com.wosai.middleware.hera.agent.services.ContextManager;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.netty.http.config.NettyHttpPluginConfig;
import org.apache.skywalking.apm.plugin.netty.http.utils.TypeUtils;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.plugin.netty.http.common.NettyConstants;
import java.net.InetSocketAddress;

@ChannelHandler.Sharable
public class NettyHttpRequestEncoderTracingHandler extends ChannelOutboundHandlerAdapter {

    private static class SingletonHolder {
        private static final NettyHttpRequestEncoderTracingHandler INSTANCE = new NettyHttpRequestEncoderTracingHandler();
    }

    public static NettyHttpRequestEncoderTracingHandler getInstance() {
        return SingletonHolder.INSTANCE;
    }

    private NettyHttpRequestEncoderTracingHandler() {

    }

    private static final ILog LOGGER = LogManager.getLogger(NettyHttpRequestEncoderTracingHandler.class);

    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) {
        try {
            if (!TypeUtils.isHttpRequest(msg)) {
                return;
            }

            AbstractHeraSpan lastSpan = ctx.channel().attr(AttributeKeys.HTTP_CLIENT_SPAN).getAndSet(null);
            if (null != lastSpan) {
                ContextManager.stopSpan();
            }
            HttpRequest request = (HttpRequest) msg;
            HttpHeaders headers = request.headers();
            String uri = request.uri();
            InetSocketAddress address = (InetSocketAddress) ctx.channel().remoteAddress();
            String peer = address.getHostString() + ":" + address.getPort();
            String url = peer + uri;

            //create exit span
            AbstractHeraSpan parentSpan = ctx.channel().attr(AttributeKeys.HTTP_CHANNEL_HANDLER_ACTIVE_SPAN).get();
            AbstractHeraSpan heraSpan;
            if (parentSpan != null) {
                heraSpan = ContextManager.createExitSpan(NettyConstants.NETTY_HTTP_OPERATION_PREFIX + uri, peer, parentSpan.context());
            } else {
                heraSpan = ContextManager.createExitSpan(NettyConstants.NETTY_HTTP_OPERATION_PREFIX + uri, peer);
            }

            ContextManager.inject(heraSpan, (key, value) -> headers.add(AsciiString.of(key), value));
            heraSpan.prepareForAsync();

            boolean sslFlag = ctx.channel().pipeline().context(SslHandler.class) != null;
            Tags.HTTP_URL.set(heraSpan, sslFlag ? NettyConstants.HTTPS_PROTOCOL_PREFIX + url : NettyConstants.HTTP_PROTOCOL_PREFIX + url);
            Tags.HTTP_METHOD.set(heraSpan, request.method().name());
            Tags.COMPONENT.set(heraSpan, ComponentsDefine.NETTY_HTTP.getName());
            if (NettyHttpPluginConfig.Plugin.NettyHttp.COLLECT_REQUEST_BODY) {
                if (TypeUtils.isLastHttpContent(msg)) {
                    HttpDataCollectUtils.collectHttpRequestBody(request.headers(), ((LastHttpContent) msg).content(), heraSpan);
                }
            }

            ContextManager.stopSpan();

            ctx.channel().attr(AttributeKeys.HTTP_CLIENT_SPAN).set(heraSpan);
        } catch (Exception e) {
            LOGGER.error("Fail to trace netty http request", e);
        } finally {
            try {
                ctx.write(msg, promise);
            } catch (Throwable throwable) {
                AbstractHeraSpan heraSpan = ctx.channel().attr(AttributeKeys.HTTP_CLIENT_SPAN).getAndSet(null);
                if (heraSpan != null) {
                    ContextManager.logError(throwable);
                    ContextManager.markError(heraSpan);
                    Tags.HTTP_STATUS.set(heraSpan, 500);
                    heraSpan.asyncFinish();
                }
                throw throwable;
            }
        }
    }
}
