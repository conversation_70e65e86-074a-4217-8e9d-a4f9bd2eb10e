package com.wosai.middleware.hera.plugin.neo4jconfig.monitor.impl;

import com.wosai.middleware.hera.agent.metrics.api.Counter;
import org.neo4j.internal.kernel.api.IndexMonitor;
import org.neo4j.internal.kernel.api.InternalIndexState;
import org.neo4j.internal.schema.IndexDescriptor;
import org.neo4j.internal.schema.IndexType;
import org.neo4j.kernel.impl.api.index.IndexSamplingMode;

// Database index metrics
public class IndexMonitorImpl implements IndexMonitor {
    private static final String METRIC_NAME_PREFIX = "neo4j.index.";

    // index.fulltext.queried
    private final Counter fulltextQueried = Counter.builder(METRIC_NAME_PREFIX + "fulltext.queried")
            .description("The total number of times fulltext indexes have been queried")
            .build();
    // index.fulltext.populated
    private final Counter fulltextPopulated = Counter.builder(METRIC_NAME_PREFIX + "fulltext.populated")
            .description("The total number of fulltext index population jobs that have been completed")
            .build();
    // index.lookup.queried
    private final Counter lookupQueried = Counter.builder(METRIC_NAME_PREFIX + "lookup.queried")
            .description("The total number of times lookup indexes have been queried")
            .build();
    // index.lookup.populated
    private final Counter lookupPopulated = Counter.builder(METRIC_NAME_PREFIX + "lookup.populated")
            .description("The total number of lookup index population jobs that have been completed")
            .build();
    // index.text.queried
    private final Counter textQueried = Counter.builder(METRIC_NAME_PREFIX + "text.queried")
            .description("The total number of times text indexes have been queried")
            .build();
    // index.text.populated
    private final Counter textPopulated = Counter.builder(METRIC_NAME_PREFIX + "text.populated")
            .description("The total number of text index population jobs that have been completed")
            .build();
    // index.range.queried
    private final Counter rangeQueried = Counter.builder(METRIC_NAME_PREFIX + "range.queried")
            .description("The total number of times range indexes have been queried")
            .build();
    // index.range.populated
    private final Counter rangePopulated = Counter.builder(METRIC_NAME_PREFIX + "range.populated")
            .description("The total number of range index population jobs that have been completed")
            .build();
    // index.point.queried
    private final Counter pointQueried = Counter.builder(METRIC_NAME_PREFIX + "point.queried")
            .description("The total number of times point indexes have been queried")
            .build();
    // index.point.populated
    private final Counter pointPopulated = Counter.builder(METRIC_NAME_PREFIX + "point.populated")
            .description("The total number of point index population jobs that have been completed")
            .build();
    // index.vector.queried
    private final Counter vectorQueried = Counter.builder(METRIC_NAME_PREFIX + "vector.queried")
            .description("The total number of times vector indexes have been queried")
            .build();
    // index.vector.populated
    private final Counter vectorPopulated = Counter.builder(METRIC_NAME_PREFIX + "vector.populated")
            .description("The total number of vector index population jobs that have been completed")
            .build();

    @Override
    public void initialState(String s, IndexDescriptor indexDescriptor, InternalIndexState internalIndexState) {

    }

    @Override
    public void populationCompleteOn(IndexDescriptor indexDescriptor) {
        IndexType indexType = indexDescriptor.getIndexType();
        switch (indexType) {
            case FULLTEXT:
                fulltextPopulated.increment();
                break;
            case LOOKUP:
                lookupPopulated.increment();
                break;
            case TEXT:
                textPopulated.increment();
                break;
            case RANGE:
                rangePopulated.increment();
                break;
            case POINT:
                pointPopulated.increment();
                break;
            case VECTOR:
                vectorPopulated.increment();
                break;
        }
    }

    @Override
    public void indexPopulationScanStarting(IndexDescriptor[] indexDescriptors) {

    }

    @Override
    public void indexPopulationScanComplete() {

    }

    @Override
    public void awaitingPopulationOfRecoveredIndex(IndexDescriptor indexDescriptor) {

    }

    @Override
    public void indexSamplingTriggered(IndexSamplingMode indexSamplingMode) {

    }

    @Override
    public void populationCancelled(IndexDescriptor[] indexDescriptors, boolean b) {

    }

    @Override
    public void populationJobCompleted(long l) {

    }

    @Override
    public void queried(IndexDescriptor indexDescriptor) {
        IndexType indexType = indexDescriptor.getIndexType();
        switch (indexType) {
            case FULLTEXT:
                fulltextQueried.increment();
                break;
            case LOOKUP:
                lookupQueried.increment();
                break;
            case TEXT:
                textQueried.increment();
                break;
            case RANGE:
                rangeQueried.increment();
                break;
            case POINT:
                pointQueried.increment();
                break;
            case VECTOR:
                vectorQueried.increment();
                break;
        }
    }

    @Override
    public void indexPopulationJobStarting(IndexDescriptor[] indexDescriptors) {

    }
}
