package com.wosai.middleware.hera.plugin.neo4jconfig.monitor.tracer;

import com.wosai.middleware.hera.plugin.neo4jconfig.monitor.MetricHelper;
import org.neo4j.io.pagecache.monitoring.PageCacheCounters;
import org.neo4j.io.pagecache.tracing.PageCacheTracer;

public class PageCacheTracerMetric {
    private static final String METRIC_NAME_PREFIX = "neo4j.page_cache.";

    public static void register(PageCacheTracer pageCacheTracer) {

        // counter
        // page_cache.eviction_exceptions
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "eviction_exceptions",
                pageCacheTracer, PageCacheCounters::evictionExceptions,
                "The total number of exceptions seen during the eviction process in the page cache");
        // page_cache.flushes
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "flushes",
                pageCacheTracer, PageCacheCounters::flushes,
                "The total number of page flushes executed by the page cache");
        // page_cache.merges
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "merges",
                pageCacheTracer, PageCacheCounters::merges,
                "The total number of page merges executed by the page cache");
        // page_cache.unpins
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "unpins",
                pageCacheTracer, PageCacheCounters::unpins,
                "The total number of page unpins executed by the page cache");
        // page_cache.pins
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "pins",
                pageCacheTracer, PageCacheCounters::pins,
                "The total number of page pins executed by the page cache");
        // page_cache.evictions
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "evictions",
                pageCacheTracer, PageCacheCounters::evictions,
                "The total number of page evictions executed by the page cache");
        // page_cache.evictions.cooperative
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "evictions.cooperative",
                pageCacheTracer, PageCacheCounters::cooperativeEvictions,
                "The total number of cooperative page evictions executed by the page cache due to low available pages");
        // page_cache.eviction.flushes
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "eviction.flushes",
                pageCacheTracer, PageCacheCounters::evictionFlushes,
                "The total number of pages flushed by page eviction");
        // page_cache.eviction.cooperative.flushes
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "cooperative.flushes",
                pageCacheTracer, PageCacheCounters::cooperativeEvictionFlushes,
                "The total number of pages flushed by cooperative page eviction");
        // page_cache.page_faults
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "page_faults",
                pageCacheTracer, PageCacheCounters::faults,
                "The total number of page faults in the page cache");
        // page_cache.page_fault_failures
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "page_fault_failures",
                pageCacheTracer, PageCacheCounters::failedFaults,
                "The total number of failed page faults happened in the page cache");
        // page_cache.page_vectored_faults
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "page_vectored_faults",
                pageCacheTracer, PageCacheCounters::vectoredFaults,
                "The total number of vectored page faults happened in the page cache");
        // page_cache.page_vectored_faults_failures
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "page_vectored_faults_failures",
                pageCacheTracer, PageCacheCounters::failedVectoredFaults,
                "The total number of failed vectored page faults happened in the page cache");
        // page_cache.page_no_pin_page_faults
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "page_no_pin_page_faults",
                pageCacheTracer, PageCacheCounters::noPinFaults,
                "The total number of page faults that are not caused by the page pins happened in the page cache");
        // page_cache.hits
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "hits",
                pageCacheTracer, PageCacheCounters::hits,
                "The total number of page hits happened in the page cache");
        // page_cache.bytes_read
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "bytes_read",
                pageCacheTracer, PageCacheCounters::bytesRead,
                "The total number of bytes read by the page cache");
        // page_cache.bytes_written
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "bytes_written",
                pageCacheTracer, PageCacheCounters::bytesWritten,
                "The total number of bytes written by the page cache");
        // page_cache.throttled.times
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "throttled.times",
                pageCacheTracer, PageCacheCounters::ioLimitedTimes,
                "The total number of times page cache flush IO limiter was throttled during ongoing IO operations");
        // page_cache.throttled.millis
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "throttled.millis",
                pageCacheTracer, PageCacheCounters::ioLimitedMillis,
                "The total number of millis page cache flush IO limiter was throttled during ongoing IO operations");
        // page_cache.pages_copied
        MetricHelper.registerFunctionCounter(METRIC_NAME_PREFIX + "pages_copied",
                pageCacheTracer, PageCacheCounters::copiedPages,
                "The total number of page copies happened in the page cache");

        // gauge
        // page_cache.hit_ratio
        MetricHelper.registerGauge(METRIC_NAME_PREFIX + "hit_ratio",
                pageCacheTracer, PageCacheCounters::hitRatio,
                "The ratio of hits to the total number of lookups in the page cache");

        // TODO
        // page_cache.page_cancelled_faults
        // page_cache.iops
        // page_cache.usage_ratio
    }
}
