package com.wosai.middleware.hera.plugin.neo4jconfig;

import com.wosai.middleware.hera.plugin.neo4jconfig.monitor.tracer.PageCacheTracerMetric;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;
import org.neo4j.graphdb.factory.module.GlobalModule;
import org.neo4j.io.pagecache.tracing.PageCacheTracer;
import org.neo4j.kernel.monitoring.tracing.Tracers;

public class Neo4jTracerInterceptor implements InstanceConstructorInterceptor {

    @Override
    public void onConstruct(EnhancedInstance enhancedInstance, Object[] objects) throws Throwable {
        GlobalModule globalModule = (GlobalModule) enhancedInstance;
        Tracers tracers = globalModule.getTracers();
        PageCacheTracer pageCacheTracer = tracers.getPageCacheTracer();
        PageCacheTracerMetric.register(pageCacheTracer);
    }
}
