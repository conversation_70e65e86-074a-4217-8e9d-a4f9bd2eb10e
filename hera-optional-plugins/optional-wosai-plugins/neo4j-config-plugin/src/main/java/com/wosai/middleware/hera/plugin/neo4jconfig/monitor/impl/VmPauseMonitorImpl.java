package com.wosai.middleware.hera.plugin.neo4jconfig.monitor.impl;

import com.wosai.middleware.hera.agent.metrics.api.Counter;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.neo4j.monitoring.VmPauseMonitor;

// GC metrics
public class VmPauseMonitorImpl implements VmPauseMonitor.Monitor {
    static final ILog LOGGER = LogManager.getLogger(VmPauseMonitorImpl.class);
    private static final String METRIC_NAME_PREFIX = "neo4j.vm.gc.";

    // vm.gc.time
    private final Counter gcTime = Counter.builder(METRIC_NAME_PREFIX + "time")
            .description("Accumulated garbage collection time in milliseconds")
            .build();
    // vm.gc.count
    private final Counter gcCount = Counter.builder(METRIC_NAME_PREFIX + "count")
            .description("Total number of garbage collections")
            .build();

    @Override
    public void started() {

    }

    @Override
    public void stopped() {

    }

    @Override
    public void interrupted() {

    }

    @Override
    public void failed(Exception e) {

    }

    @Override
    public void pauseDetected(VmPauseMonitor.VmPauseInfo vmPauseInfo) {
        String gcInfo = vmPauseInfo.toString();
        String[] gcData = gcInfo.split(",");
        String gcTimeStr = gcData[1].split("=")[1];
        String gcCountStr = gcData[2].split("=")[1];
        double time = Double.parseDouble(gcTimeStr);
        double count = Double.parseDouble(gcCountStr);
        gcTime.increment(time);
        gcCount.increment(count);
    }
}
