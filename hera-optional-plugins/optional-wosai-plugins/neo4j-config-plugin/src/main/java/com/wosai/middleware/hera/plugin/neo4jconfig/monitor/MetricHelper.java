package com.wosai.middleware.hera.plugin.neo4jconfig.monitor;

import com.wosai.middleware.hera.agent.metrics.api.FunctionCounter;
import com.wosai.middleware.hera.agent.metrics.api.Gauge;
import java.util.function.ToDoubleFunction;

public class MetricHelper {

    public static <T> void registerGauge(String metricName, T obj, ToDoubleFunction<T> f, String description, String... tags) {
        Gauge.builder(metricName, obj, f)
                .description(description)
                .tags(tags)
                .build();
    }

    public static <T> void registerFunctionCounter(String metricName, T obj, ToDoubleFunction<T> f, String description, String... tags) {
        FunctionCounter.builder(metricName, obj, f)
                .description(description)
                .tags(tags)
                .build();
    }
}
