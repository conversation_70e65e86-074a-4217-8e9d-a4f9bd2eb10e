package com.wosai.middleware.hera.plugin.neo4jconfig.monitor.impl;

import com.wosai.middleware.hera.agent.metrics.api.Counter;
import org.neo4j.kernel.impl.query.QueryRoutingMonitor;

// Query routing metrics
public class QueryRoutingMonitorImpl implements QueryRoutingMonitor {
    private static final String METRIC_NAME_PREFIX = "neo4j.dbms.routing.query.count.";

    // dbms.routing.query.count.local
    private final Counter local = Counter.builder(METRIC_NAME_PREFIX + "local")
            .description("The total number of queries executed locally")
            .build();
    // dbms.routing.query.count.remote_internal
    private final Counter remoteInternal = Counter.builder(METRIC_NAME_PREFIX + "remote_internal")
            .description("The total number of queries routed over to another member of the same cluster")
            .build();
    // dbms.routing.query.count.remote_external
    private final Counter remoteExternal = Counter.builder(METRIC_NAME_PREFIX + "remote_external")
            .description("The total number of queries routed over to a server outside the cluster")
            .build();

    @Override
    public void queryRoutedLocal() {
        local.increment();
    }

    @Override
    public void queryRoutedRemoteInternal() {
        remoteInternal.increment();
    }

    @Override
    public void queryRoutedRemoteExternal() {
        remoteExternal.increment();
    }
}
