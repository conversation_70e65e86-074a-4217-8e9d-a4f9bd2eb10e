package com.wosai.middleware.hera.plugin.neo4jconfig.monitor.impl;

import com.wosai.middleware.hera.agent.metrics.api.Counter;
import com.wosai.middleware.hera.plugin.neo4jconfig.monitor.MetricHelper;
import org.neo4j.internal.kernel.api.security.AccessMode;
import org.neo4j.kernel.api.KernelTransaction;
import org.neo4j.kernel.impl.query.TransactionExecutionMonitor;

import java.util.concurrent.atomic.AtomicLong;

// Database transaction metrics
public class TransactionMonitorImpl implements TransactionExecutionMonitor {
    private static final String METRIC_NAME_PREFIX = "neo4j.transaction.";

    // transaction.started
    private final Counter transactionStarted = Counter.builder(METRIC_NAME_PREFIX + "started")
            .description("The total number of started transactions")
            .build();
    // transaction.committed
    private final Counter transactionCommitted = Counter.builder(METRIC_NAME_PREFIX + "committed")
            .description("The total number of committed transactions")
            .build();
    // transaction.committed_read
    private final Counter transactionCommittedRead = Counter.builder(METRIC_NAME_PREFIX + "committed_read")
            .description("The total number of committed read transactions")
            .build();
    // transaction.committed_write
    private final Counter transactionCommittedWrite = Counter.builder(METRIC_NAME_PREFIX + "committed_write")
            .description("The total number of committed write transactions")
            .build();
    // transaction.rollbacks
    private final Counter transactionRollbacks = Counter.builder(METRIC_NAME_PREFIX + "rollbacks")
            .description("The total number of rolled back transactions")
            .build();
    // transaction.rollbacks_read
    private final Counter transactionRollbacksRead = Counter.builder(METRIC_NAME_PREFIX + "rollbacks_read")
            .description("The total number of rolled back read transactions")
            .build();
    // transaction.rollbacks_write
    private final Counter transactionRollbacksWrite = Counter.builder(METRIC_NAME_PREFIX + "rollbacks_write")
            .description("The total number of rolled back write transactions")
            .build();
    // transaction.terminated
    private final Counter transactionTerminated = Counter.builder(METRIC_NAME_PREFIX + "terminated")
            .description("The total number of terminated transactions")
            .build();
    // transaction.terminated_read
    private final Counter transactionTerminatedRead = Counter.builder(METRIC_NAME_PREFIX + "terminated_read")
            .description("The total number of terminated read transactions")
            .build();
    // transaction.terminated_write
    private final Counter transactionTerminatedWrite = Counter.builder(METRIC_NAME_PREFIX + "terminated_write")
            .description("The total number of terminated write transactions")
            .build();
    // transaction.last_committed_tx_id
    private final Counter transactionLastCommittedTxId = Counter.builder(METRIC_NAME_PREFIX + "last_committed_tx_id")
            .description("The ID of the last committed transaction")
            .build();
    // transaction.last_closed_tx_id
    private final Counter transactionLastClosedTxId = Counter.builder(METRIC_NAME_PREFIX + "last_closed_tx_id")
            .description("The ID of the last closed transaction")
            .build();

    // transaction.active
    private final AtomicLong transactionActive = new AtomicLong();
    // transaction.active_read
    private final AtomicLong transactionActiveRead = new AtomicLong();
    // transaction.active_write
    private final AtomicLong transactionActiveWrite = new AtomicLong();

    // register gauge
    {
        MetricHelper.registerGauge(METRIC_NAME_PREFIX + "active", transactionActive, AtomicLong::doubleValue,
                "The number of currently active transactions");
        MetricHelper.registerGauge(METRIC_NAME_PREFIX + "active_read", transactionActiveRead, AtomicLong::doubleValue,
                "The number of currently active read transactions");
        MetricHelper.registerGauge(METRIC_NAME_PREFIX + "active_write", transactionActiveWrite, AtomicLong::doubleValue,
                "The number of currently active write transactions");
    }

    // TODO transaction.peak_concurrent
    // TODO transaction_tx_size_heap
    // TODO transaction_tx_size_native

    @Override
    public void start(KernelTransaction tx) {
        transactionStarted.increment();
        transactionActive.incrementAndGet();
        if (isWrite(tx)) {
            transactionActiveWrite.incrementAndGet();
        } else {
            transactionActiveRead.incrementAndGet();
        }
    }

    @Override
    public void commit(KernelTransaction tx) {
        if (isTerminated(tx)) {
            // terminated
            transactionTerminated.increment();
            if (isWrite(tx)) {
                transactionTerminatedWrite.increment();
            } else {
                transactionTerminatedRead.increment();
            }
        } else {
            // committed
            transactionCommitted.increment();
            if (isWrite(tx)) {
                transactionCommittedWrite.increment();
            } else {
                transactionCommittedRead.increment();
            }
        }

        if (isClosed(tx)) {
            transactionLastClosedTxId.increment();
            if (!isTerminated(tx)) {
                transactionLastCommittedTxId.increment();
            }
        }

        transactionActive.decrementAndGet();
        if (isWrite(tx)) {
            transactionActiveWrite.decrementAndGet();
        } else {
            transactionActiveRead.decrementAndGet();
        }
    }

    @Override
    public void rollback(KernelTransaction tx, Throwable failure) {
        if (isTerminated(tx)) {
            // terminated
            transactionTerminated.increment();
            if (isWrite(tx)) {
                transactionTerminatedWrite.increment();
            } else {
                transactionTerminatedRead.increment();
            }
        } {
            // rollbacks
            transactionRollbacks.increment();
            if (isWrite(tx)) {
                transactionRollbacksWrite.increment();
            } else {
                transactionRollbacksRead.increment();
            }
        }

        if (isClosed(tx)) {
            transactionLastClosedTxId.increment();
        }

        transactionActive.decrementAndGet();
        if (isWrite(tx)) {
            transactionActiveWrite.decrementAndGet();
        } else {
            transactionActiveRead.decrementAndGet();
        }
    }

    private boolean isWrite(KernelTransaction tx) {
        AccessMode mode = tx.securityContext().mode();
        return mode.allowsWrites();
    }

    private boolean isTerminated(KernelTransaction tx) {
        return tx.isTerminated();
    }

    private boolean isClosed(KernelTransaction tx) {
        return !tx.isOpen();
    }
}
