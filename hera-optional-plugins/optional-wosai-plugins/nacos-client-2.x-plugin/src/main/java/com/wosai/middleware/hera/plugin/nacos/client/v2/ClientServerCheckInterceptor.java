package com.wosai.middleware.hera.plugin.nacos.client.v2;

import com.alibaba.nacos.api.remote.response.Response;
import com.alibaba.nacos.common.remote.client.RpcClient;
import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;

import java.lang.reflect.Method;

public class ClientServerCheckInterceptor implements InstanceMethodsAroundInterceptor {
    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, MethodInterceptResult methodInterceptResult) throws Throwable {
        String ip = (String) objects[0];
        int port = (Integer) objects[1];
        enhancedInstance.setSkyWalkingDynamicField(new RpcClient.ServerInfo(ip, port));
        AbstractHeraSpan heraSpan = ContextManager.createExitSpan("Nacos/serverCheck", ip + ":" + port);
        Tags.COMPONENT.set(heraSpan, ComponentsDefine.NACOS.getName());
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Object o) throws Throwable {
        if (ContextManager.isActive()) {
            if (o == null || !((Response) o).isSuccess()) {
                AbstractHeraSpan heraSpan = ContextManager.activeSpan();
                ContextManager.markError(heraSpan);
            }
            ContextManager.stopSpan();
        }
        return o;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {
        if (ContextManager.isActive()) {
            AbstractHeraSpan heraSpan = ContextManager.activeSpan();
            ContextManager.markError(heraSpan);
        }
    }
}
