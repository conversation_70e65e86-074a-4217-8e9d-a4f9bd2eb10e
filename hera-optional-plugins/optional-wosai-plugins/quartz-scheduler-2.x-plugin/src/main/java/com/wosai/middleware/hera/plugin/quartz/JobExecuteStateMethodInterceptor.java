package com.wosai.middleware.hera.plugin.quartz;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.quartz.JobExecutionException;

import java.lang.reflect.Method;

public class JobExecuteStateMethodInterceptor implements InstanceMethodsAroundInterceptor {
    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] allArguments, Class<?>[] classes, MethodInterceptResult methodInterceptResult) throws Throwable {
        if (ContextManager.isActive()) {
            JobExecutionException exception = (JobExecutionException) allArguments[1];
            if (exception != null) {
                AbstractHeraSpan heraSpan = ContextManager.activeSpan();
                heraSpan.error(exception);
            }
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Object o) throws Throwable {
        return o;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {
    }
}
