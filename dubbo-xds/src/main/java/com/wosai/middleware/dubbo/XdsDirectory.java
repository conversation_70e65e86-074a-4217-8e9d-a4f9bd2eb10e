/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.wosai.middleware.dubbo;

import com.google.common.collect.Maps;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.logger.ErrorTypeAwareLogger;
import org.apache.dubbo.common.logger.LoggerFactory;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Protocol;
import org.apache.dubbo.rpc.cluster.Directory;
import org.apache.dubbo.rpc.cluster.SingleRouterChain;
import org.apache.dubbo.rpc.cluster.directory.AbstractDirectory;
import org.apache.dubbo.rpc.cluster.router.state.BitList;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.apache.dubbo.common.constants.CommonConstants.REMOTE_APPLICATION_KEY;

public class XdsDirectory<T> extends AbstractDirectory<T> {

    private final URL oriUrl;

    private final Class<T> serviceType;

    private final String[] applicationNames;

    private final String protocolName;

    private Protocol protocol;

    private final int port;

    private static final ErrorTypeAwareLogger logger = LoggerFactory.getErrorTypeAwareLogger(XdsDirectory.class);

    public XdsDirectory(Directory<T> directory) {
        super(directory.getUrl().putAttribute("refer",
                        Maps.immutableEntry("CONSUMER_URL", directory.getConsumerUrl().toString())),
                null, true);
        this.serviceType = directory.getInterface();
        this.oriUrl = directory.getConsumerUrl();
        this.applicationNames = oriUrl.getParameter("provided-by").split(",");
        this.protocolName = oriUrl.getParameter("protocol", "tri");
        this.protocol = oriUrl.getOrDefaultApplicationModel()
                .getExtensionLoader(Protocol.class)
                .getExtension(protocolName);
        if (Objects.equals(protocolName, "http")) {
            this.port = 80;
        } else {
            this.port = 50051;
        }
    }

    public Protocol getProtocol() {
        return protocol;
    }

    public void setProtocol(Protocol protocol) {
        this.protocol = protocol;
    }

    @Override
    public Class<T> getInterface() {
        return serviceType;
    }

    public List<Invoker<T>> doList(
            SingleRouterChain<T> singleRouterChain, BitList<Invoker<T>> invokers, Invocation invocation) {
        return BitList.emptyList();
    }

    @Override
    public List<Invoker<T>> getAllInvokers() {
        return super.getInvokers();
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public void destroy() {
        super.destroy();
    }

    @Override
    public void setInvokers(BitList<Invoker<T>> invokers) {
        super.setInvokers(invokers);
    }

    public Invoker<T> initInvoker(String addr, int port, String appName) {
        Map<String, String> params = new HashMap<>(oriUrl.getParameters());
        params.put(REMOTE_APPLICATION_KEY, appName);
        URL url = new URL(protocolName, addr, port, serviceType.getName(), params);
        return protocol.refer(serviceType, url);
    }

    public int getPort() {
        return port;
    }

    public String getProtocolName() {
        return protocolName;
    }

    public String[] getApplicationNames() {
        return applicationNames;
    }

    public Class<T> getServiceType() {
        return serviceType;
    }

    public URL getOriUrl() {
        return oriUrl;
    }
}
