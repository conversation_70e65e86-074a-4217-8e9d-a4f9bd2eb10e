ARG BASE_IMAGE
FROM ${BASE_IMAGE}

ARG VCS_REF
ARG BUILD_DATE
ARG VERSION

LABEL MAINTAINER=<EMAIL>
LABEL wosai.hera.version=$VERSION \
      wosai.hera.release-date=$BUILD_DATE \
      wosai.hera.vcs-url="https://git.wosai-inc.com/middleware/hera" \
      wosai.hera.vcs-ref=$VCS_REF

SHELL ["/bin/bash", "-c"]

USER root

# replace and generate a backup
RUN sed -i.bak -e 's/archive.ubuntu.com/mirrors.aliyun.com/g' -e 's/ports.ubuntu.com/mirrors.aliyun.com/g' -e 's/security.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list

# set noninteractive installation
ENV DEBIAN_FRONTEND noninteractive
# install tzdata package
RUN apt-get update && apt-get install -y \
    tzdata \
    curl \
    jq \
    unzip \
    && rm -rf /var/lib/apt/lists/*
# set your timezone
RUN ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN dpkg-reconfigure --frontend noninteractive tzdata

USER jetty
WORKDIR /app/hera-agent
COPY ./ci/docker-entrypoint.sh .
COPY --chown=jetty:jetty ./target/hera-agent ./agent

WORKDIR $JETTY_BASE

ENTRYPOINT ["/app/hera-agent/docker-entrypoint.sh"]
CMD ["java","-jar","/usr/local/jetty/start.jar"]
