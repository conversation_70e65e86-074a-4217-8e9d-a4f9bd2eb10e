<?xml version="1.0"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<!DOCTYPE import-control PUBLIC
        "-//Puppy Crawl//DTD Import Control 1.4//EN"
        "https://checkstyle.org/dtds/import_control_1_4.dtd">

<import-control pkg="com.wosai.middleware.hera.(toolkit|plugin)" regex="true">
    <allow pkg="java"/>
    <allow pkg="org.apache.skywalking"/>
    <allow pkg="com.wosai.middleware.hera"/>
    <allow pkg="net.bytebuddy"/>
    <allow pkg="javax"/>
    <allow pkg="lombok"/>

    <subpackage name=".*" strategyOnMismatch="allowed" regex="true">
        <allow class="com.google.common.annotations.VisibleForTesting"/>
        <disallow pkg="com.google"/>
    </subpackage>

    <subpackage name="(jdbc.)?tomcat.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="org.apache.catalina"/>
        <allow pkg="org.apache.tomcat"/>
    </subpackage>

    <subpackage name="jsonrpc.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="com.fasterxml.jackson"/>
        <allow pkg="org.apache.http"/>
    </subpackage>

    <subpackage name="(httpclient|httpasyncclient).*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="org.apache.http"/>
    </subpackage>

    <subpackage name="jedis.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="redis.clients"/>
    </subpackage>

    <subpackage name="lettuce.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="io.lettuce.core"/>
    </subpackage>

    <subpackage name="jetty.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="org.eclipse.jetty"/>
    </subpackage>

    <subpackage name="spring.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="org.springframework"/>
    </subpackage>

    <subpackage name="kafka.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="org.apache.kafka"/>
    </subpackage>

    <subpackage name="mongodb.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="com.mongodb"/>
    </subpackage>

    <subpackage name="feign.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="feign"/>
    </subpackage>

    <subpackage name="shardingsphere.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="org.apache.shardingsphere"/>
    </subpackage>

    <subpackage name="datasource.hikaricp.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="com.zaxxer.hikari"/>
    </subpackage>

    <subpackage name="datasource.druid.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="com.alibaba.druid"/>
    </subpackage>

    <subpackage name="okhttp.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="okhttp3"/>
    </subpackage>

    <subpackage name="cache.ehcache.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="net.sf.ehcache"/>
    </subpackage>

    <subpackage name="cache.caffeine.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="com.github.benmanes.caffeine"/>
    </subpackage>

    <subpackage name="elasticsearch.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="org.elasticsearch"/>
    </subpackage>

    <subpackage name="redisson.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="io.netty.buffer"/>
        <allow pkg="org.redisson"/>
    </subpackage>

    <subpackage name="dbcp.*" strategyOnMismatch="allowed" regex="true">
        <allow pkg="org.apache.commons.dbcp2"/>
    </subpackage>
</import-control>