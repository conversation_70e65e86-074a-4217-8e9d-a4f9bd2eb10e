# HERA

> Java Agent for Observability

## How to compile

You need,

- `JD<PERSON>` (=17)

### Build Agent Jar

```shell
$ ./mvnw clean package
```

use maven wrapper is always recommended.

### Build docker image

```shell
# to build for jdk8 runtime
$ make docker.jdk8
```

Some common build options,

 - If one want to disable apt mirror, use `BUILD_ARGS="--build-arg APT_MIRROR="` as a prefix

## How to release

### Git SCM

First, checkout a new release branch if not exist, e.g. `release/1.10.x`,

```shell
$ git checkout release/1.10.x
```

Then, bump the Maven pom version by,

```shell
$ mvn versions:set -DnewVersion=<VERSION>
$ mvn versions:commit
```

And create a tag on that commit.

### Artifact

After a successful pipeline, a `build-tags:arthive` will be generated by the CI and thus can be downloaded.

Extract and re-compress the `hera-agent` to `hera-agent.<VERSION>.RELEASE.zip` with some hacks,

```shell
$ zip -x "*.DS_Store" -x "__MACOSX" -r hera-agent.<VERSION>.RELEASE.zip ./hera-agent
```

in order to exclude unnecessary directories. Upload the zip to the OSS bucket `oss://sqb-ft/hera-agent`.

Besides, artifacts published to the JFrog MUST be promoted.

### Init Container

Bump the version in the configuration for `init0-go`.
