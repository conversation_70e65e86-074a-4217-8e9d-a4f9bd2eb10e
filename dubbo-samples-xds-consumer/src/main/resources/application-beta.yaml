dubbo:
  application:
    name: dubbo-samples-xds-consumer
    qosEnable: true
    qosAcceptForeignIp: true
    metadataServicePort: 20885
    metadataServiceProtocol: dubbo
    metadata-type: local
  registry:
    address: xds://***************:30100?security=plaintext
  consumer:
    timeout: 3000
    check: false
    cluster: xds
  protocol:
    name: tri
    port: 50051
  reference:
    jsonrpc4j-wosai-envoy:
      url: http://jsonrpc4j-wosai-envoy.beta.iwosai.com/rpc/example
server:
  port: 8080
