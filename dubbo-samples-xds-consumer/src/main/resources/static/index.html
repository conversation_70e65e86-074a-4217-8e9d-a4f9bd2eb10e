
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>Dubbo & JSON-RPC 接口测试</title>
    <style>    body {
        font-family: Arial, sans-serif;
        padding: 20px;
        margin: 0;
        display: flex;
        min-height: 100vh;
    }

    .container {
        display: flex;
        max-width: 1200px;
        margin: auto;
        gap: 20px;
    }

    .left-panel, .right-panel {
        flex: 1;
        min-width: 500px;
    }

    .left-panel h2 {
        margin-top: 0;
    }

    input, button {
        width: 100%;
        padding: 8px;
        margin-top: 5px;
        margin-bottom: 15px;
        box-sizing: border-box;
    }

    p {
        background-color: #f0f0f0;
        padding: 10px;
        white-space: pre-wrap;
        word-break: break-word;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
    }

    th, td {
        border: 1px solid #ccc;
        padding: 8px;
        text-align: left;
        font-size: 14px;
    }

    th {
        background-color: #f9f9f9;
    }

    .header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    </style>
</head>
<body>

<div class="container">

    <!-- 左边：接口调用区域 -->
    <div class="left-panel">
        <h2>Dubbo Triple 协议调用</h2>
        <input type="text" id="tripleName" placeholder="输入名称（如：Alice）">
        <input type="text" id="envFlagTriple" placeholder="输入环境标识（如：test 或 prod）">
        <button onclick="callTriple()">调用 Dubbo Triple 接口</button>
        <p id="tripleResult"></p>

        <h2>JSON-RPC (HTTP) 协议调用</h2>
        <input type="text" id="jsonrpcName" placeholder="输入名称（如：Bob）">
        <input type="text" id="envFlagJsonRpc" placeholder="输入环境标识（如：test 或 prod）">
        <button onclick="callJsonRpc()">调用 JSON-RPC 接口</button>
        <p id="jsonrpcResult"></p>
    </div>

    <!-- 右边：请求历史记录 -->
    <div class="right-panel">
        <div class="header-row">
            <h2>请求历史记录</h2>
            <button onclick="clearHistory()" style="padding: 5px 10px; font-size: 14px;">清除历史</button>
        </div>

        <table id="historyTable">
            <thead>
            <tr>
                <th>时间</th>
                <th>接口类型</th>
                <th>请求参数</th>
                <th>环境标识</th>
                <th>返回值</th>
            </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

</div>

<script>  // 页面加载时恢复历史记录
window.onload = function () {
    const history = JSON.parse(localStorage.getItem('requestHistory') || '[]');
    renderHistory(history);
};

// 调用 Dubbo Triple 接口
function callTriple() {
    const name = document.getElementById('tripleName').value;
    const envFlag = document.getElementById('envFlagTriple').value;

    const headers = {};
    if (envFlag) {
        headers['x-env-flag'] = envFlag.trim();
    }

    fetch(`/api/test/triple/${name}`, {
        method: 'GET',
        headers: headers,
    })
        .then(res => res.text())
        .then(data => {
            document.getElementById('tripleResult').innerText = 'Dubbo Triple 返回结果:\n' + data;
            saveToHistory('Dubbo Triple', name, envFlag, data);
        })
        .catch(err => {
            document.getElementById('tripleResult').innerText = '调用失败:\n' + err.message;
            saveToHistory('Dubbo Triple', name, envFlag, '调用失败: ' + err.message);
        });
}

// 调用 JSON-RPC 接口
function callJsonRpc() {
    const name = document.getElementById('jsonrpcName').value;
    const envFlag = document.getElementById('envFlagJsonRpc').value;

    const headers = {};
    if (envFlag) {
        headers['x-env-flag'] = envFlag.trim();
    }

    fetch(`/api/test/jsonrpc/${name}`, {
        method: 'GET',
        headers: headers,
    })
        .then(res => res.text())
        .then(data => {
            document.getElementById('jsonrpcResult').innerText = 'JSON-RPC 返回结果:\n' + data;
            saveToHistory('JSON-RPC', name, envFlag, data);
        })
        .catch(err => {
            document.getElementById('jsonrpcResult').innerText = '调用失败:\n' + err.message;
            saveToHistory('JSON-RPC', name, envFlag, '调用失败: ' + err.message);
        });
}

// 保存请求记录到 localStorage
function saveToHistory(type, name, envFlag, result) {
    const history = JSON.parse(localStorage.getItem('requestHistory') || '[]');
    const record = {
        time: new Date().toLocaleString(),
        type: type,
        name: name,
        envFlag: envFlag || '-',
        result: result
    };
    history.unshift(record); // 插入到最前面
    localStorage.setItem('requestHistory', JSON.stringify(history));
    renderHistory(history);
}

// 渲染表格
function renderHistory(history) {
    const tbody = document.querySelector('#historyTable tbody');
    tbody.innerHTML = '';
    history.forEach(item => {
        const tr = document.createElement('tr');
        tr.innerHTML = `        <td>${item.time}</td>
        <td>${item.type}</td>
        <td>${item.name}</td>
        <td>${item.envFlag}</td>
        <td>${item.result}</td>
      `;
        tbody.appendChild(tr);
    });
}

// 清除历史记录
function clearHistory() {
    localStorage.removeItem('requestHistory');
    renderHistory([]);
}
</script>

</body>
</html>