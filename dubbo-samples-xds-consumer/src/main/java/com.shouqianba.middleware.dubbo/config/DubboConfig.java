package com.shouqianba.middleware.dubbo.config;

import com.shouqianba.middleware.service.ExampleService;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.bootstrap.DubboBootstrap;
import org.apache.dubbo.config.utils.SimpleReferenceCache;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DubboConfig {
    @Value("${dubbo.reference.jsonrpc4j-wosai-envoy.url}")
    private String exampleServiceUrl;
    @Bean
    public ExampleService exampleService() {
        ReferenceConfig<ExampleService> reference = new ReferenceConfig<>();
        reference.setInterface(ExampleService.class);
        reference.setProtocol("http");
        reference.setTimeout(300);
        reference.setUrl(exampleServiceUrl);
        reference.setProvidedBy("jsonrpc4j-wosai-envoy");
        DubboBootstrap.getInstance().reference(reference);
        SimpleReferenceCache cache = SimpleReferenceCache.getCache();
        return cache.get(reference);
    }
}
