/*
 *
 *   Licensed to the Apache Software Foundation (ASF) under one or more
 *   contributor license agreements.  See the NOTICE file distributed with
 *   this work for additional information regarding copyright ownership.
 *   The ASF licenses this file to You under the Apache License, Version 2.0
 *   (the "License"); you may not use this file except in compliance with
 *   the License.  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 *
 */

package com.shouqianba.middleware.dubbo;

import com.shouqianba.middleware.dubbo.api.GreetingService;
import com.shouqianba.middleware.service.ExampleService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

@RestController
@RequestMapping("/api/dubbo")
public class GreetingServiceController {

    /**
     * 订阅dubbo xds服务端
     */
    @DubboReference(version = "1.0.0", providedBy = "dubbo-samples-xds-provider", timeout = 2800)
    private GreetingService greetingService;

    @RequestMapping("/echo/{name}")
    public String dubboecho(@PathVariable String name) {
        return greetingService.sayHello(name);
    }

    /**
     * 订阅dubbo nacos注册中心服务场景 （该场景元数据依赖字段为空）
     */
//    @DubboReference(group = "ft", providedBy = "service-governance-demo", protocol = "tri")
//    private HostService hostService;
//
//    @RequestMapping("/triecho/{name}")
//    public String doSayHello(String name) {
//        return hostService.getHost();
//    }

    /**
     * 订阅jsonrpc4j项目
      */
//    @DubboReference(providedBy = "jsonrpc4j-wosai-envoy", protocol = "http")
    @Autowired
    private ExampleService exampleService;

    @RequestMapping("/jsecho/{name}")
    public String jsonrpcecho(@PathVariable String name) {
        return exampleService.sayHello(name, Collections.singletonMap("message", "hello"));
    }
}
