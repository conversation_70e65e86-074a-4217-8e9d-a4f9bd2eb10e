package com.shouqianba.middleware.dubbo;

import com.shouqianba.middleware.dubbo.api.GreetingService;
import com.shouqianba.middleware.service.ExampleService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;

@RestController
@RequestMapping("/api/test")
public class ServiceTestController {

//
//
//    @DubboReference(version = "1.0.0", providedBy = "dubbo-samples-xds-provider", timeout = 2800)
//    private GreetingService greetingService;
//
//    /**
//     * HTTP JSON-RPC 服务
//     */
//    @DubboReference(providedBy = {"jsonrpc4j-wosai-envoy"}, protocol = "http", timeout = 300)
//    private ExampleService exampleService;
//
//    /**
//     * 调用 Dubbo Triple 接口
//     */
//    @GetMapping("/triple/{name}")
//    public String callTriple(@PathVariable String name, HttpServletRequest request) {
//        // 将所有请求头透传给 Dubbo Triple 协议
//        return greetingService.sayHello(name);
//    }
//
//    /**
//     * 调用 HTTP JSON-RPC 接口
//     */
//    @GetMapping("/jsonrpc/{name}")
//    public String callJsonRpc(@PathVariable String name, HttpServletRequest request) {
//        // 构造一个包含 headers 的 metadata
//        return exampleService.sayHello(name, Collections.singletonMap("message", "hello"));
//    }
}