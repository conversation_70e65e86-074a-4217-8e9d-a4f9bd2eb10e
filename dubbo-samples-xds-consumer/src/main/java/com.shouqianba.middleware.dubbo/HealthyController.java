package com.shouqianba.middleware.dubbo;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by <PERSON><PERSON><PERSON>quan on 2018/11/22.
 * <p>
 * 注意: 不要、不要、千万不要删除，这个是作为接入bingo的必要条件
 */
@RestController
@RequestMapping("")
@Slf4j
public class HealthyController {
    /**
     * 健康检查接口
     */
    @RequestMapping(value = "/check", method = RequestMethod.GET)
    public
    @ResponseBody
    String healthy() {
        return "group: success"; //可以自定义逻辑来验证系统已经启动成功
    }


}