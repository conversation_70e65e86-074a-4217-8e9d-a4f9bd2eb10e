<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hera</artifactId>
        <groupId>com.wosai.middleware</groupId>
        <version>1.14.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hera-agent</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <premain.class>com.wosai.middleware.hera.agent.Agent</premain.class>
        <can.redefine.classes>true</can.redefine.classes>
        <can.retransform.classes>true</can.retransform.classes>
        <shade.net.bytebuddy.source>net.bytebuddy</shade.net.bytebuddy.source>
        <shade.net.bytebuddy.target>${shade.package}.${shade.net.bytebuddy.source}</shade.net.bytebuddy.target>
    </properties>

    <dependencies>
        <!-- plugin -->
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-agent-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>bytebuddy-patch</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.skywalking</groupId>
                    <artifactId>apm-agent-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.bytebuddy</groupId>
                    <artifactId>byte-buddy</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <finalName>hera-agent</finalName>
        <plugins>
            <plugin>
                <artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <shadedArtifactAttached>false</shadedArtifactAttached>
                            <createDependencyReducedPom>true</createDependencyReducedPom>
                            <createSourcesJar>true</createSourcesJar>
                            <shadeSourcesContent>true</shadeSourcesContent>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <manifestEntries>
                                        <Premain-Class>${premain.class}</Premain-Class>
                                        <Can-Redefine-Classes>${can.redefine.classes}</Can-Redefine-Classes>
                                        <Can-Retransform-Classes>${can.retransform.classes}</Can-Retransform-Classes>
                                    </manifestEntries>
                                </transformer>
                            </transformers>
                            <artifactSet>
                                <excludes>
                                    <!-- skywalking dependencies -->
                                    <exclude>*:gson</exclude>
                                    <exclude>io.grpc:*</exclude>
                                    <exclude>io.netty:*</exclude>
                                    <exclude>io.opencensus:*</exclude>
                                    <exclude>com.google.*:*</exclude>
                                    <exclude>com.google.guava:guava</exclude>
                                    <exclude>org.checkerframework:checker-compat-qual</exclude>
                                    <exclude>org.checkerframework:checker-qual</exclude>
                                    <exclude>org.codehaus.mojo:animal-sniffer-annotations</exclude>
                                    <exclude>com.google.api.grpc:proto-google-common-protos:jar:</exclude>
                                    <exclude>org.jetbrains:annotations:jar:</exclude>
                                    <exclude>io.perfmark:*</exclude>
                                    <exclude>org.slf4j:*</exclude>
                                    <!-- hera dependencies -->
                                    <exclude>io.zipkin.brave:*</exclude>
                                    <exclude>com.fasterxml.jackson.core:*</exclude>
                                    <exclude>com.fasterxml.jackson.datatype:*</exclude>
                                    <exclude>com.squareup.okhttp3:*</exclude>
                                    <exclude>com.squareup.okio:*</exclude>
                                    <exclude>io.micrometer:*</exclude>
                                    <exclude>io.prometheus:*</exclude>
                                    <exclude>io.jaegertracing:*</exclude>
                                    <exclude>io.github.mweirauch:micrometer-jvm-extras</exclude>
                                    <exclude>org.jetbrains.kotlin:*</exclude>
                                    <exclude>org.hdrhistogram:*</exclude>
                                    <exclude>org.latencyutils:*</exclude>
                                    <exclude>io.zipkin.brave:*</exclude>
                                    <exclude>io.zipkin.reporter2:*</exclude>
                                    <exclude>io.zipkin.zipkin2:*</exclude>
                                    <exclude>org.apache.thrift:*</exclude>
                                    <exclude>io.opentracing:*</exclude>
                                    <!-- sentinel -->
                                    <exclude>com.alibaba.csp:*</exclude>
                                    <exclude>com.alibaba:fastjson</exclude>
                                    <exclude>com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru
                                    </exclude>
                                    <exclude>com.wosai.middleware:hera-xds:*</exclude>
                                    <exclude>com.shouqianba.middleware:hera-xds-api:*</exclude>
                                    <exclude>com.salesforce.*:*</exclude>
                                    <exclude>io.reactivex.rxjava2:*</exclude>
                                    <exclude>org.reactivestreams:*</exclude>
                                    <exclude>com.auth0:java-jwt</exclude>
                                    <exclude>com.wosai.middleware:hera-network:*</exclude>
                                    <exclude>com.alibaba:bytekit-core:*</exclude>
                                    <exclude>com.alibaba:bytekit-instrument-api:*</exclude>
                                    <exclude>com.alibaba:repackage-asm:*</exclude>
                                    <exclude>ognl:*</exclude>
                                    <exclude>org.javassist:javassist:*</exclude>
                                    <!-- pyroscope agent -->
                                    <exclude>io.pyroscope:*</exclude>
                                </excludes>
                            </artifactSet>
                            <relocations>
                                <relocation>
                                    <pattern>${shade.net.bytebuddy.source}</pattern>
                                    <shadedPattern>${shade.net.bytebuddy.target}</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>${shade.org.slf4j.source}</pattern>
                                    <shadedPattern>${shade.org.slf4j.target}</shadedPattern>
                                </relocation>
                            </relocations>
                            <filters>
                                <filter>
                                    <artifact>net.bytebuddy:byte-buddy</artifact>
                                    <excludes>
                                        <exclude>META-INF/versions/9/module-info.class</exclude>
                                    </excludes>
                                </filter>
                                <filter>
                                    <artifact>org.apache.skywalking:apm-agent-core</artifact>
                                    <excludes>
                                        <exclude>
                                            META-INF/services/org.apache.skywalking.apm.agent.core.boot.BootService
                                        </exclude>
                                        <exclude>
                                            META-INF/services/org.apache.skywalking.apm.dependencies.io.grpc.*
                                        </exclude>
                                        <!-- exclude protos -->
                                        <exclude>asyncprofiler/**</exclude>
                                        <exclude>browser/**</exclude>
                                        <exclude>common/**</exclude>
                                        <exclude>ebpf/**</exclude>
                                        <exclude>event/**</exclude>
                                        <exclude>language-agent/**</exclude>
                                        <exclude>logging/**</exclude>
                                        <exclude>management/**</exclude>
                                        <exclude>profile/**</exclude>
                                        <exclude>service-mesh-probe/**</exclude>
                                        <exclude>linux-arm64/*</exclude>
                                        <exclude>linux-x64/*</exclude>
                                        <exclude>macos/*</exclude>
                                        <exclude>META-INF/native/*</exclude>
                                        <exclude>org/apache/skywalking/apm/dependencies/io/grpc/**</exclude>
                                        <exclude>org/apache/skywalking/apm/dependencies/io/netty/**</exclude>
                                        <exclude>org/apache/skywalking/apm/dependencies/com/google/common/**</exclude>
                                        <exclude>org/apache/skywalking/apm/dependencies/com/google/protobuf/**</exclude>
                                        <exclude>org/apache/skywalking/apm/dependencies/one/profiler/**</exclude>
                                        <exclude>org/checkerframework/**</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>package</id>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <mkdir dir="${project.basedir}/../target/hera-agent"/>
                                <copy file="${project.build.directory}/hera-agent.jar"
                                      tofile="${project.basedir}/../target/hera-agent/hera-agent.jar"
                                      overwrite="true"/>
                                <mkdir dir="${project.basedir}/../target/hera-agent/config"/>
                                <mkdir dir="${project.basedir}/../target/hera-agent/logs"/>
                                <copydir src="${project.basedir}/../config"
                                         dest="${project.basedir}/../target/hera-agent/config" forceoverwrite="true"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>