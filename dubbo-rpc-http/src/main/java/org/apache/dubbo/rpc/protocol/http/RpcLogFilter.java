package org.apache.dubbo.rpc.protocol.http;

import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.Filter;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.support.RpcUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;

import static org.apache.dubbo.common.constants.CommonConstants.DEFAULT_TIMEOUT;

@Activate(group = {CommonConstants.PROVIDER, CommonConstants.CONSUMER})
public class RpcLogFilter implements Filter {
    public static final Logger log = LoggerFactory.getLogger(RpcLogFilter.class);

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        long start = System.currentTimeMillis();
        final boolean isProvider = RpcContext.getServerContext().getUrl() == null || RpcContext.getServerContext().isProviderSide();

        try {
            // 调用前记录
            logRequest(invocation, isProvider);

            Result result = invoker.invoke(invocation);
            long duration = System.currentTimeMillis() - start;

            // 调用后记录
            logResponse(invocation, result, duration, isProvider);
            return result;
        } catch (RpcException e) {
            long duration = System.currentTimeMillis() - start;
            logError(invocation, e, duration, isProvider);
            throw e;
        }
    }
    private void logRequest(Invocation invocation, boolean isProvider) {
        String role = isProvider ? "[Provider]" : "[Consumer]";
        log.info("{} RPC Request - method={} arguments={}",
                role,
                invocation.getMethodName(),
                Arrays.toString(invocation.getArguments()));
    }
    private void logResponse(Invocation invocation, Result result, long duration, boolean isProvider) {
        String role = isProvider ? "[Provider]" : "[Consumer]";
        log.info("{} RPC Response - method={} arguments={} result={} duration={}",
                role,
                invocation.getMethodName(),
                Arrays.toString(invocation.getArguments()),
                result.getValue(),
                duration);
    }

    private void logError(Invocation invocation, RuntimeException e, long duration, boolean isProvider) {
        String role = isProvider ? "[Provider]" : "[Consumer]";
        log.error("{} RPC Error - method=\"{}\" duration={}, error=\"{}\"",
                role,
                invocation.getMethodName(),
                duration,
                e.getMessage(),
                e); // 最后一个参数会自动作为异常堆栈打印
    }
}