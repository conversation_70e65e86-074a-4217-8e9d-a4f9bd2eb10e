package org.apache.dubbo.rpc.protocol.http;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.PenetrateAttachmentSelector;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.dubbo.rpc.RpcContextAttachment;

import java.util.Map;

@Activate(group = {CommonConstants.PROVIDER, CommonConstants.CONSUMER})
public class DefaultPenetrateAttachmentSelector implements PenetrateAttachmentSelector {

    private static final String X_ENV_FLAG = "x-env-flag";
    /**
     * Select some attachments to pass to next hop.
     * These attachments can fetch from {@link RpcContext#getServerAttachment()} or user defined.
     * 该方法会在该服务作为客户端时进行调用
     * 相当于调用Invocation.addObjectAttachments方法
     */
    @Override
    public Map<String, Object> select(Invocation invocation, RpcContextAttachment clientAttachment, RpcContextAttachment serverAttachment) {
        if (StringUtils.isNotBlank(serverAttachment.getAttachment(X_ENV_FLAG))) {
            /**
             * A -> B -> C
             * 当B收到环境标识后重新传入clientAttachment，透传至C
             */
            clientAttachment.setAttachment(X_ENV_FLAG, serverAttachment.getAttachment(X_ENV_FLAG));
        }
        return null;
    }

    @Override
    public Map<String, Object> selectReverse(Invocation invocation, RpcContextAttachment clientResponseContext, RpcContextAttachment serverResponseContext) {
        return null;
    }
}
