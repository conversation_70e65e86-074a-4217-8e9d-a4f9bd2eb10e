package org.apache.dubbo.rpc.protocol.http;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.googlecode.jsonrpc4j.JsonRpcClient;
import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static net.logstash.logback.argument.StructuredArguments.value;


public class LogRequestListener implements JsonRpcClient.RequestListener {
    public static final Logger log = LoggerFactory.getLogger(LogRequestListener.class);

    private ThreadLocal<Long> startTime = new ThreadLocal<>();
    private ThreadLocal<ObjectNode> req = new ThreadLocal<>();

    @Override
    public void onBeforeRequestSent(JsonRpcClient client, ObjectNode request) {
        startTime.set(System.currentTimeMillis());
        req.set(request);
    }

    @Override
    public void onBeforeResponseProcessed(JsonRpcClient client, ObjectNode response) {
        String url = (client instanceof JsonRpcHttpClient)
                ? ((JsonRpcHttpClient) client).getServiceUrl().toString() : null;
        String header = (client instanceof JsonRpcHttpClient)
                ? ((JsonRpcHttpClient) client).getHeaders().get("x-env-flag") : null;
        log.info("dubbo-jsonrpc-client",
                value("url", url),
                value("request", req.get()),
                value("headers", header),
                value("response", response),
                value("duration", System.currentTimeMillis() - startTime.get()));
    }
}
