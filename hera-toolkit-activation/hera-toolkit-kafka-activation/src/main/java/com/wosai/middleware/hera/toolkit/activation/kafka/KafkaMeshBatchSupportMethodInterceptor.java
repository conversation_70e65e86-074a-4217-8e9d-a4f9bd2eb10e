package com.wosai.middleware.hera.toolkit.activation.kafka;

import com.wosai.middleware.hera.agent.conf.HeraConfig;
import com.wosai.middleware.hera.agent.services.ContextManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.header.Headers;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.StaticMethodsAroundInterceptor;
import org.apache.kafka.common.header.Header;

import java.lang.reflect.Method;
import java.util.Optional;

import static com.wosai.middleware.hera.agent.mesh.Constants.ENV_BASE;
import static com.wosai.middleware.hera.agent.mesh.Constants.TAG_ROUTING;

@Slf4j
public class KafkaMeshBatchSupportMethodInterceptor implements StaticMethodsAroundInterceptor {
    @Override
    public void beforeMethod(Class aClass, Method method, Object[] objects, Class<?>[] classes, MethodInterceptResult methodInterceptResult) {
        if (HeraConfig.Kubernetes.Pod.VERSION.equals(ENV_BASE) && HeraConfig.Mesh.Kafka.ENABLE_SWIMLANE) {
            // extract envFlag from consumer record
            Headers headers = (Headers) objects[0];
            Header envFlagHeader = headers.lastHeader(TAG_ROUTING);
            String envFlag = ENV_BASE;
            if (envFlagHeader != null) {
                byte[] payload = envFlagHeader.value();
                if (payload != null) {
                    envFlag = new String(payload);
                }
            }
            // set env flag into additional baggage
            Optional<String> result = ContextManager.setAdditionalBaggage(TAG_ROUTING, envFlag);
            log.debug("setAdditionBaggage result:{}", result);
        }
    }

    @Override
    public Object afterMethod(Class aClass, Method method, Object[] objects, Class<?>[] classes, Object o) {
        return null;
    }

    @Override
    public void handleMethodException(Class aClass, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {

    }
}
