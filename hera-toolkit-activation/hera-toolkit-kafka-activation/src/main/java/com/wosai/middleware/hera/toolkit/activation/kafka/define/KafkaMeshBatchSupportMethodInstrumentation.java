package com.wosai.middleware.hera.toolkit.activation.kafka.define;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.StaticMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.ClassStaticMethodsEnhancePluginDefine;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;

import static net.bytebuddy.matcher.ElementMatchers.named;
import static org.apache.skywalking.apm.agent.core.plugin.match.NameMatch.byName;

public class KafkaMeshBatchSupportMethodInstrumentation extends ClassStaticMethodsEnhancePluginDefine {

    private static final String ENHANCE_CLASS = "com.wosai.middleware.hera.toolkit.kafka.KafkaMeshBatchSupportManager";
    private static final String METHOD = "setEnvFlagFromKafkaRecord";
    private static final String KAFKA_MESH_BATCH_SUPPORT_INTERCEPTOR = "com.wosai.middleware.hera.toolkit.activation.kafka.KafkaMeshBatchSupportMethodInterceptor";

    @Override
    protected ClassMatch enhanceClass() {
        return byName(ENHANCE_CLASS);
    }

    @Override
    public StaticMethodsInterceptPoint[] getStaticMethodsInterceptPoints() {
        return new StaticMethodsInterceptPoint[] {
                new StaticMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(METHOD);
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return KAFKA_MESH_BATCH_SUPPORT_INTERCEPTOR;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }
}
