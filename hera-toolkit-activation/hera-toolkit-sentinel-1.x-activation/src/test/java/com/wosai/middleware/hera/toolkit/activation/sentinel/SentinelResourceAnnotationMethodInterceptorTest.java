package com.wosai.middleware.hera.toolkit.activation.sentinel;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.wosai.middleware.hera.agent.sentinel.FlowRule;
import com.wosai.middleware.hera.infra.HeraAgentServiceRule;
import com.wosai.middleware.hera.test.HeraSentinelInMemoryServer;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.lang.reflect.Method;
import java.util.concurrent.atomic.AtomicReference;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.catchRuntimeException;

public class SentinelResourceAnnotationMethodInterceptorTest {
    @Rule
    public MockitoRule rule = MockitoJUnit.rule();

    private SentinelResourceAnnotationMethodInterceptor sentinelResourceAnnotationMethodInterceptor;

    private Method method;

    private MethodInvocationContext context;

    private SampleService enhanced;

    @Rule
    public HeraSentinelInMemoryServer heraInMemoryServer = new HeraSentinelInMemoryServer("sentinelResource-annotation");

    @Rule
    public HeraAgentServiceRule agentServiceRule = new HeraAgentServiceRule();

    @Before
    public void setup() throws NoSuchMethodException {
        heraInMemoryServer.loadRules(FlowRule.initFlowRule("getParams", null, 0));

        sentinelResourceAnnotationMethodInterceptor = new SentinelResourceAnnotationMethodInterceptor();

        method = SampleService.class.getDeclaredMethod("getParams", Integer.class);

        context = new MethodInvocationContext();

        enhanced = new SampleService();
    }

    @After
    public void after() throws Throwable {
        sentinelResourceAnnotationMethodInterceptor.afterMethod(enhanced, method, new Object[]{2}, null, null, context);
    }

    @Test
    public void test_getParamsByBlockHandler() throws Throwable {
        sentinelResourceAnnotationMethodInterceptor.beforeMethod(enhanced, method, new Object[]{2}, null, context);
        assertThat(context).isNotNull();
        assertThat(context._ret()).isNotNull().hasToString("blockHandler age:2");
    }

    @Test
    public void test_handleMethodExceptionWithIgnore() {
        Exception throwException = new IllegalStateException("age must greater than 1");
        Exception exception = catchRuntimeException(
                () -> sentinelResourceAnnotationMethodInterceptor.handleMethodException(enhanced, method, new Object[]{1}, null,
                        throwException, context));
        assertThat(exception).isNotNull().isSameAs(throwException);
    }

    @Test
    public void test_test_handleMethodExceptionByFallbackHandler() {
        SampleService enhanced = new SampleService();
        Exception throwException = new IllegalArgumentException("invalid age");
        AtomicReference<Object> result = new AtomicReference<>();
        Exception exception = catchRuntimeException(
                () -> result.set(sentinelResourceAnnotationMethodInterceptor.handleMethodException(enhanced, method, new Object[]{-1}, null,
                        throwException, context)));
        assertThat(exception).isNull();
        assertThat(result.get()).isEqualTo("fallbackHandler age:-1");
    }

    public static class SampleService implements EnhancedInstance {

        @SentinelResource(value = "getParams", blockHandler = "blockHandler", exceptionsToIgnore = {
                IllegalStateException.class}, fallback = "fallbackHandler")
        public String getParams(Integer age) throws Throwable {
            // throw ArithmeticException when age = 0
            age = age * (age / age);
            if (age < 0) throw new IllegalArgumentException("invalid age");
            if (age == 1) throw new IllegalStateException("age must greater than 1");
            if (age == 2) Thread.sleep(2000);
            return "age:" + age;
        }

        public String blockHandler(Integer age, BlockException ex) throws Throwable {
            if (age == -3) {
                RuntimeException runtimeException = ex.toRuntimeException();
                throw runtimeException;
            }
            return "blockHandler age:" + age;
        }

        public String fallbackHandler(Integer age) throws Throwable {
            return "fallbackHandler age:" + age;
        }

        private Object obj;

        @Override
        public Object getSkyWalkingDynamicField() {
            return obj;
        }

        @Override
        public void setSkyWalkingDynamicField(Object o) {
            this.obj = o;
        }
    }
}
