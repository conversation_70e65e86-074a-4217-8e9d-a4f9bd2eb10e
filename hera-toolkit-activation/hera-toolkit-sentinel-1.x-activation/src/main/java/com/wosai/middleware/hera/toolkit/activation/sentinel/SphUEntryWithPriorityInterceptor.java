package com.wosai.middleware.hera.toolkit.activation.sentinel;

import com.alibaba.csp.sentinel.EntryType;
import com.wosai.middleware.hera.agent.core.plugin.interceptor.enhance.v3.StaticMethodsAroundInterceptorV3;
import com.wosai.middleware.hera.agent.sentinel.BlockException;
import com.wosai.middleware.hera.agent.services.SentinelContextManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;

import java.lang.reflect.Method;

import static com.wosai.middleware.hera.toolkit.activation.sentinel.AbstractSentinelInterceptorSupport.parseEntryType;

public class SphUEntryWithPriorityInterceptor implements StaticMethodsAroundInterceptorV3 {
    @Override
    public void beforeMethod(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes, MethodInvocationContext context) throws Throwable {
        SentinelContextManager.entryWithPriority(String.valueOf(allArguments[0]), parseEntryType((EntryType) allArguments[1]));
    }

    @Override
    public Object afterMethod(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes, Object ret, MethodInvocationContext context) throws Throwable {
        return null;
    }

    @Override
    public Object handleMethodException(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes, Throwable t, MethodInvocationContext context) throws Throwable {
        if (t instanceof BlockException) {
            com.alibaba.csp.sentinel.slots.block.BlockException exception = BlockExceptionHelper.parseFromCore((BlockException) t);
            throw exception;
        }
        throw t;
    }
}
