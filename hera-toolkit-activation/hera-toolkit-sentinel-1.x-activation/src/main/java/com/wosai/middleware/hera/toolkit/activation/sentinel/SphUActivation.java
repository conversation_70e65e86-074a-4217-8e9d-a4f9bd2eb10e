package com.wosai.middleware.hera.toolkit.activation.sentinel;

import com.wosai.middleware.hera.agent.core.plugin.interceptor.enhance.v3.ClassEnhancePluginDefineV3;
import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.StaticMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.v2.InstanceMethodsInterceptV2Point;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.v2.StaticMethodsInterceptV2Point;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;
import org.apache.skywalking.apm.agent.core.plugin.match.NameMatch;

import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.returns;
import static net.bytebuddy.matcher.ElementMatchers.takesArguments;

public class SphUActivation extends ClassEnhancePluginDefineV3 {
    private static final String ENHANCE_CLASS = "com.alibaba.csp.sentinel.SphU";
    private static final String ENTRY_INTERCEPTOR_CLASS = "com.wosai.middleware.hera.toolkit.activation.sentinel.SphUEntryInterceptor";
    private static final String ENTRY_INTERCEPTOR_METHOD_NAME = "entry";
    private static final String ENTRY_END_INTERCEPTOR_CLASS = "com.wosai.middleware.hera.toolkit.activation.sentinel.SphUEntryEndInterceptor";
    private static final String ENTRY_END_INTERCEPTOR_METHOD_NAME = "entryEnd";
    private static final String ENTRY_WITH_PRIORITY_INTERCEPTOR_CLASS = "com.wosai.middleware.hera.toolkit.activation.sentinel.SphUEntryWithPriorityInterceptor";
    private static final String ENTRY_WITH_PRIORITY_METHOD_NAME = "entryWithPriority";

    @Override
    protected ClassMatch enhanceClass() {
        return NameMatch.byName(ENHANCE_CLASS);
    }

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[0];
    }

    @Override
    public InstanceMethodsInterceptV2Point[] getInstanceMethodsInterceptV2Points() {
        return new InstanceMethodsInterceptV2Point[0];
    }

    @Override
    public StaticMethodsInterceptPoint[] getStaticMethodsInterceptPoints() {
        return new StaticMethodsInterceptPoint[]{
                new StaticMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(ENTRY_INTERCEPTOR_METHOD_NAME).and(takesArguments(4)).and(returns(void.class));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return ENTRY_INTERCEPTOR_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new StaticMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(ENTRY_END_INTERCEPTOR_METHOD_NAME);
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return ENTRY_END_INTERCEPTOR_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new StaticMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(ENTRY_WITH_PRIORITY_METHOD_NAME).and(takesArguments(2));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return ENTRY_WITH_PRIORITY_INTERCEPTOR_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }

    @Override
    public StaticMethodsInterceptV2Point[] getStaticMethodsInterceptV2Points() {
        return new StaticMethodsInterceptV2Point[]{
                new StaticMethodsInterceptV2Point() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(ENTRY_INTERCEPTOR_METHOD_NAME).and(takesArguments(4)).and(returns(void.class));
                    }

                    @Override
                    public String getMethodsInterceptorV2() {
                        return ENTRY_INTERCEPTOR_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new StaticMethodsInterceptV2Point() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(ENTRY_END_INTERCEPTOR_METHOD_NAME);
                    }

                    @Override
                    public String getMethodsInterceptorV2() {
                        return ENTRY_END_INTERCEPTOR_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new StaticMethodsInterceptV2Point() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(ENTRY_WITH_PRIORITY_METHOD_NAME).and(takesArguments(2));
                    }

                    @Override
                    public String getMethodsInterceptorV2() {
                        return ENTRY_WITH_PRIORITY_INTERCEPTOR_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }
}
