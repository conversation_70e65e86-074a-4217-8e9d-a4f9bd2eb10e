package com.wosai.middleware.hera.toolkit.activation.sentinel;

import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.flow.ClusterFlowConfig;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowClusterConfig;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowException;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowItem;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowRule;
import com.alibaba.csp.sentinel.slots.system.SystemBlockException;
import com.wosai.middleware.hera.agent.sentinel.BlockException;
import org.apache.skywalking.apm.agent.core.util.CollectionUtil;

import java.util.ArrayList;
import java.util.List;

public class BlockExceptionHelper {
    public static com.alibaba.csp.sentinel.slots.block.BlockException parseFromCore(BlockException wrappedBlockEx) {
        com.alibaba.csp.sentinel.slots.block.BlockException exception = null;

        if (wrappedBlockEx.isFlowException()) {
            FlowRule flowRule = new FlowRule();
            com.wosai.middleware.hera.agent.sentinel.FlowRule wrapRule = (com.wosai.middleware.hera.agent.sentinel.FlowRule) wrappedBlockEx.getRule();
            flowRule.setStrategy(wrapRule.getStrategy());
            flowRule.setClusterMode(wrapRule.isClusterMode());
            flowRule.setCount(wrapRule.getCount());
            flowRule.setGrade(wrapRule.getGrade());
            flowRule.setControlBehavior(wrapRule.getControlBehavior());
            flowRule.setMaxQueueingTimeMs(wrapRule.getMaxQueueingTimeMs());
            flowRule.setWarmUpPeriodSec(wrapRule.getWarmUpPeriodSec());
            flowRule.setLimitApp(wrapRule.getLimitApp());
            flowRule.setResource(wrapRule.getResource());
            flowRule.setControlBehavior(wrapRule.getControlBehavior());
            flowRule.setRefResource(wrapRule.getRefResource());
            if (wrapRule.getClusterConfig() != null) {
                ClusterFlowConfig clusterFlowConfig = new ClusterFlowConfig();
                clusterFlowConfig.setFlowId(wrapRule.getClusterConfig().getFlowId());
                clusterFlowConfig.setStrategy(wrapRule.getClusterConfig().getStrategy());
                clusterFlowConfig.setFallbackToLocalWhenFail(wrapRule.getClusterConfig().isFallbackToLocalWhenFail());
                clusterFlowConfig.setResourceTimeout(wrapRule.getClusterConfig().getResourceTimeout());
                clusterFlowConfig.setSampleCount(wrapRule.getClusterConfig().getSampleCount());
                clusterFlowConfig.setAcquireRefuseStrategy(wrapRule.getClusterConfig().getAcquireRefuseStrategy());
                clusterFlowConfig.setClientOfflineTime(wrapRule.getClusterConfig().getClientOfflineTime());
                clusterFlowConfig.setResourceTimeoutStrategy(wrapRule.getClusterConfig().getResourceTimeoutStrategy());
                clusterFlowConfig.setThresholdType(wrapRule.getClusterConfig().getThresholdType());
                clusterFlowConfig.setWindowIntervalMs(wrapRule.getClusterConfig().getWindowIntervalMs());
                flowRule.setClusterConfig(clusterFlowConfig);
            }
            exception = new FlowException(wrappedBlockEx.getRuleLimitApp(), flowRule);
        } else if (wrappedBlockEx.isParamFlowException()) {
            ParamFlowRule paramFlowRule = new ParamFlowRule();
            com.wosai.middleware.hera.agent.sentinel.ParamFlowRule wrapRule = (com.wosai.middleware.hera.agent.sentinel.ParamFlowRule) wrappedBlockEx.getRule();

            if (wrapRule.getClusterConfig() != null) {
                ParamFlowClusterConfig paramFlowClusterConfig = new ParamFlowClusterConfig();
                paramFlowClusterConfig.setFlowId(wrapRule.getClusterConfig().getFlowId());
                paramFlowClusterConfig.setFallbackToLocalWhenFail(wrapRule.getClusterConfig().isFallbackToLocalWhenFail());
                paramFlowClusterConfig.setSampleCount(wrapRule.getClusterConfig().getSampleCount());
                paramFlowClusterConfig.setThresholdType(wrapRule.getClusterConfig().getThresholdType());
                paramFlowClusterConfig.setWindowIntervalMs(wrapRule.getClusterConfig().getWindowIntervalMs());
                paramFlowRule.setClusterConfig(paramFlowClusterConfig);
            }
            if (!CollectionUtil.isEmpty(wrapRule.getParamFlowItemList())) {
                List<ParamFlowItem> list = new ArrayList<>();
                wrapRule.getParamFlowItemList().forEach(it -> {
                    list.add(new ParamFlowItem(it.getObject(), it.getCount(), it.getClassType()));
                });
                paramFlowRule.setParamFlowItemList(list);
            }
            paramFlowRule.setCount(wrapRule.getCount());
            paramFlowRule.setGrade(wrapRule.getGrade());
            paramFlowRule.setLimitApp(wrapRule.getLimitApp());
            paramFlowRule.setParamIdx(wrapRule.getParamIdx());
            paramFlowRule.setResource(wrapRule.getResource());
            paramFlowRule.setBurstCount(wrapRule.getBurstCount());
            paramFlowRule.setClusterMode(wrapRule.isClusterMode());
            paramFlowRule.setControlBehavior(wrapRule.getControlBehavior());
            paramFlowRule.setDurationInSec(wrapRule.getDurationInSec());
            paramFlowRule.setMaxQueueingTimeMs(wrapRule.getMaxQueueingTimeMs());

            exception = new ParamFlowException(wrappedBlockEx.getResourceName(), wrappedBlockEx.getRuleLimitApp(), paramFlowRule);
        } else if (wrappedBlockEx.isDegradeException()) {
            DegradeRule degradeRule = new DegradeRule();
            com.wosai.middleware.hera.agent.sentinel.DegradeRule wrapRule = (com.wosai.middleware.hera.agent.sentinel.DegradeRule) wrappedBlockEx.getRule();
            degradeRule.setCount(wrapRule.getCount());
            degradeRule.setGrade(wrapRule.getGrade());
            degradeRule.setResource(wrapRule.getResource());
            degradeRule.setLimitApp(wrapRule.getLimitApp());
            degradeRule.setTimeWindow(wrapRule.getTimeWindow());
            degradeRule.setMinRequestAmount(wrapRule.getMinRequestAmount());
            degradeRule.setSlowRatioThreshold(wrapRule.getSlowRatioThreshold());
            degradeRule.setStatIntervalMs(wrapRule.getStatIntervalMs());
            exception = new DegradeException(wrappedBlockEx.getRuleLimitApp(), degradeRule);
        } else if (wrappedBlockEx.isSystemBlockException()) {
            exception = new SystemBlockException(wrappedBlockEx.getResourceName(), wrappedBlockEx.getRuleLimitApp());
        }
        return exception;
    }
}
