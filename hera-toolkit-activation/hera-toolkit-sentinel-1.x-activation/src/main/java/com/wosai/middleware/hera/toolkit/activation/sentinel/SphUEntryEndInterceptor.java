package com.wosai.middleware.hera.toolkit.activation.sentinel;

import com.wosai.middleware.hera.agent.core.plugin.interceptor.enhance.v3.StaticMethodsAroundInterceptorV3;
import com.wosai.middleware.hera.agent.services.SentinelContextManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;

import java.lang.reflect.Method;

public class SphUEntryEndInterceptor implements StaticMethodsAroundInterceptorV3 {

    @Override
    public void beforeMethod(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes, MethodInvocationContext context) throws Throwable {
        SentinelContextManager.entryEnd((Integer) allArguments[0], (Object[]) allArguments[1]);
    }

    @Override
    public Object afterMethod(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes, Object ret, MethodInvocationContext context) throws Throwable {
        return ret;
    }

    @Override
    public Object handleMethodException(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes, Throwable t, MethodInvocationContext context) throws Throwable {
        return null;
    }
}
