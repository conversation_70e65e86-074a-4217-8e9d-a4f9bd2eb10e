package com.wosai.middleware.hera.toolkit.activation.log.log4j.v2.async;

import com.wosai.middleware.hera.agent.services.ContextManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;

/**
 * Log4jLogEvent implements LogEvent, which is a message in the Disruptor Array of the AsyncLoggerConfigDisruptor.class,
 * appended by AsyncLoggerConfig.class. The Log4jLogEvent is enhanced to carry the tid in the synchronization thread
 * using `dynamicField`.
 */

public class Log4jLogEventConstructorInterceptor implements InstanceConstructorInterceptor {

    @Override
    public void onConstruct(EnhancedInstance objInst, Object[] allArguments) {
        objInst.setSkyWalkingDynamicField(ContextManager.getTraceId());
    }
}