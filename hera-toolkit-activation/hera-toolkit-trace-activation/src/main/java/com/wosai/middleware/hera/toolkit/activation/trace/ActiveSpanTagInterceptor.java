package com.wosai.middleware.hera.toolkit.activation.trace;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.StaticMethodsAroundInterceptor;

import java.lang.reflect.Method;

public class ActiveSpanTagInterceptor implements StaticMethodsAroundInterceptor {
    @Override
    public void beforeMethod(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes,
                             MethodInterceptResult result) {
        try {
            AbstractHeraSpan activeSpan = ContextManager.activeSpan();
            if (activeSpan == null) return;
            activeSpan.setTag(String.valueOf(allArguments[0]), String.valueOf(allArguments[1]));
        } catch (NullPointerException ignored) {
        }
    }

    @Override
    public Object afterMethod(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes,
                              Object ret) {
        return ret;
    }

    @Override
    public void handleMethodException(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes,
                                      Throwable t) {

    }
}
