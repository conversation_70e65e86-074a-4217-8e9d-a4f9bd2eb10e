package com.wosai.middleware.hera.toolkit.activation.metrics;

import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.StaticMethodsAroundInterceptor;

import java.lang.reflect.Method;

public class ActiveMetricsErrorThrowableInterceptor implements StaticMethodsAroundInterceptor {
    @Override
    public void beforeMethod(Class aClass, Method method, Object[] allArguments, Class<?>[] classes, MethodInterceptResult methodInterceptResult) {
        Throwable t = (Throwable) allArguments[0];
        if (t != null) {
            TimedAspectImpl.ThrowableHolder holder = TimedAspectImpl.CONTEXT_THROWABLE_HOLDER.get().peek();
            if (holder != null) {
                holder.t = t;
            }
        }
    }

    @Override
    public Object afterMethod(Class aClass, Method method, Object[] objects, Class<?>[] classes, Object o) {
        return o;
    }

    @Override
    public void handleMethodException(Class aClass, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {

    }
}
