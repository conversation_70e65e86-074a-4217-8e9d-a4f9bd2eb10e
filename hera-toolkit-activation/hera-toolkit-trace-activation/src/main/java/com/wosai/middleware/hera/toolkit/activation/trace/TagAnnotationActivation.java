package com.wosai.middleware.hera.toolkit.activation.trace;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.DeclaredInstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.InstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.StaticMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.ClassEnhancePluginDefine;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;

import static net.bytebuddy.matcher.ElementMatchers.isAnnotatedWith;
import static net.bytebuddy.matcher.ElementMatchers.named;
import static org.apache.skywalking.apm.agent.core.plugin.match.MethodAnnotationMatch.byMethodAnnotationMatch;
import static org.apache.skywalking.apm.agent.core.plugin.match.logical.LogicalMatchOperation.and;
import static org.apache.skywalking.apm.agent.core.plugin.match.logical.LogicalMatchOperation.not;
import static org.apache.skywalking.apm.agent.core.plugin.match.logical.LogicalMatchOperation.or;

/**
 * Intercepts all methods annotated with {@link com.wosai.middleware.hera.toolkit.trace.Tag}
 */
public class TagAnnotationActivation extends ClassEnhancePluginDefine {
    public static final String TAG_ANNOTATION_METHOD_INTERCEPTOR = "com.wosai.middleware.hera.toolkit.activation.trace.TagAnnotationMethodInterceptor";
    public static final String TAG_ANNOTATION_STATIC_METHOD_INTERCEPTOR = "com.wosai.middleware.hera.toolkit.activation.trace.TagAnnotationStaticMethodInterceptor";
    public static final String TAG_ANNOTATION = "com.wosai.middleware.hera.toolkit.trace.Tag";
    public static final String TAGS_ANNOTATION = "com.wosai.middleware.hera.toolkit.trace.Tags";
    public static final String TRACE_ANNOTATION = "com.wosai.middleware.hera.toolkit.trace.Trace";

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[0];
    }

    @Override
    public InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints() {
        return new InstanceMethodsInterceptPoint[]{
                new DeclaredInstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return isAnnotatedWith(named(TAG_ANNOTATION));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return TAG_ANNOTATION_METHOD_INTERCEPTOR;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }

    @Override
    protected ClassMatch enhanceClass() {
        return and(not(byMethodAnnotationMatch(TRACE_ANNOTATION)),
                or(byMethodAnnotationMatch(TAGS_ANNOTATION), byMethodAnnotationMatch(TAG_ANNOTATION)));
    }

    @Override
    public StaticMethodsInterceptPoint[] getStaticMethodsInterceptPoints() {
        return new StaticMethodsInterceptPoint[]{
                new StaticMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return isAnnotatedWith(named(TAG_ANNOTATION));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return TAG_ANNOTATION_STATIC_METHOD_INTERCEPTOR;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }
}
