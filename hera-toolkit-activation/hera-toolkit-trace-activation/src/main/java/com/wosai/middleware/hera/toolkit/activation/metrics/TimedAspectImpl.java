package com.wosai.middleware.hera.toolkit.activation.metrics;

import com.wosai.middleware.hera.agent.metrics.api.Counter;
import com.wosai.middleware.hera.agent.metrics.api.LongTaskTimer;
import com.wosai.middleware.hera.agent.metrics.api.Tags;
import com.wosai.middleware.hera.agent.metrics.api.Timer;
import com.wosai.middleware.hera.agent.metrics.handlers.AbstractComponentHandler;
import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.apache.skywalking.apm.agent.core.util.CustomizeExpression;

import java.lang.reflect.Method;
import java.util.ArrayDeque;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletionStage;

import static com.wosai.middleware.hera.toolkit.activation.metrics.TimedAnnotationActivation.DEFAULT_METRIC_NAME;

@Slf4j
public class TimedAspectImpl {
    private static final String CLASS_TAG = "class";
    private static final String METHOD_TAG = "method";
    private static final String ALIAS_TAG = "alias";

    static final ThreadLocal<ArrayDeque<ThrowableHolder>> CONTEXT_THROWABLE_HOLDER = ThreadLocal.withInitial(ArrayDeque::new);

    /**
     * This method shall be called before the enhanced method is executed.
     *
     * @param method                  the enhanced method
     * @param allArguments            all arguments of the enhanced method
     * @param methodInvocationContext the method invocation context exists per method call
     */
    protected void beforeTimedMethod(Method method, Object[] allArguments, MethodInvocationContext methodInvocationContext) {
        Timed timed = method.getAnnotation(Timed.class);
        if (timed == null) return;

        // push a holder for throwable
        CONTEXT_THROWABLE_HOLDER.get().push(new ThrowableHolder());

        final Map<String, Object> context = CustomizeExpression.evaluationContext(allArguments);

        String alias = timed.value();
        if (timed.value().isEmpty()) {
            alias = method.getDeclaringClass().getName() + "#" + method.getName();
        } else if (timed.value().startsWith("arg[")) {
            // only parse express if the value starts with `arg[`
            alias = CustomizeExpression.parseExpression(timed.value(), context);
        }

        if (!timed.longTask()) {
            methodInvocationContext.setContext(beforeProcessWithTimer(method, alias));
        } else {
            methodInvocationContext.setContext(beforeProcessWithLongTaskTimer(timed, method, alias));
        }
    }

    private TimerContext beforeProcessWithTimer(Method method, String alias) {
        Timer.Sample sample = Timer.start();
        return new TimerContext(sample,
                Tags.of(CLASS_TAG, method.getDeclaringClass().getName(),
                        METHOD_TAG, method.getName(),
                        ALIAS_TAG, alias), alias
        );
    }

    private LongTaskTimerContext beforeProcessWithLongTaskTimer(Timed timed, Method method, String alias) {
        Optional<LongTaskTimer.Sample> sample = buildLongTaskTimer(timed, method, alias)
                .map(LongTaskTimer::start);

        return new LongTaskTimerContext(sample.orElse(null));
    }

    private Optional<LongTaskTimer> buildLongTaskTimer(Timed timed, Method method, String alias) {
        try {
            return Optional.of(LongTaskTimer.builder(DEFAULT_METRIC_NAME)
                    .tag(CLASS_TAG, method.getDeclaringClass().getName())
                    .tag(METHOD_TAG, method.getName())
                    .tag(ALIAS_TAG, alias)
                    .description(timed.description().isEmpty() ? null : timed.description())
                    .publishPercentileHistogram(timed.histogram())
                    .publishPercentiles(timed.percentiles().length == 0 ? null : timed.percentiles())
                    .build());
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    /**
     * This method should be called after the method is executed and the result has been returned.
     *
     * @param method                  the enhanced method
     * @param retVal                  the original return value of the enhance method
     * @param methodInvocationContext the method invocation context exists per method call
     * @return return value which may be decorated (if it implements {@link CompletionStage})
     */
    protected Object afterTimedMethod(Method method, Object retVal, MethodInvocationContext methodInvocationContext) {
        Timed timed = method.getAnnotation(Timed.class);
        if (timed == null) return retVal;

        // pop/recycle the holder
        ThrowableHolder holder = CONTEXT_THROWABLE_HOLDER.get().pop();

        final boolean stopWhenCompleted = CompletionStage.class.isAssignableFrom(method.getReturnType());

        if (!timed.longTask()) {
            // record exception with counter
            final boolean recordException = method.getAnnotation(Trace.class) == null;
            return afterProcessWithTimer(retVal, timed, methodInvocationContext, recordException, stopWhenCompleted, holder);
        } else {
            return afterProcessWithLongTaskTimer(retVal, methodInvocationContext, stopWhenCompleted);
        }
    }

    /**
     * This method should be called if and only if the @Timed annotated method is not measure in long task mode.
     *
     * @param retVal                  the return value of the enhanced method
     * @param timed                   the annotation
     * @param methodInvocationContext the per-method-call context
     * @param recordException         whether we have to record the exception. For @Trace annotated method, we must not.
     * @param stopWhenCompleted       a flag which depends on the return type
     * @param throwableHolder         a thread-local holder for runtime "injected" throwable
     * @return a (maybe wrapped) return value
     */
    private Object afterProcessWithTimer(Object retVal, Timed timed, MethodInvocationContext methodInvocationContext,
                                         boolean recordException, boolean stopWhenCompleted, ThrowableHolder throwableHolder) {
        if (stopWhenCompleted) {
            return ((CompletionStage<?>) retVal).whenComplete(
                    (result, throwable) -> record((TimerContext) methodInvocationContext.getContext(), timed, recordException, getExceptionTag(throwable)));
        }

        TimerContext context = (TimerContext) methodInvocationContext.getContext();
        // overwrite the throwable if possible
        if (throwableHolder.t != null) {
            context.exceptionClass = getExceptionTag(throwableHolder.t);
        }
        record(context, timed, recordException, context.exceptionClass);
        return retVal;
    }

    private void record(TimerContext context, Timed timed, boolean recordException, String exceptionClass) {
        try {
            context.sample.stop(
                    Timer.builder(DEFAULT_METRIC_NAME)
                            .description(timed.description().isEmpty() ? null : timed.description())
                            .publishPercentileHistogram(timed.histogram())
                            .publishPercentiles(timed.percentiles().length == 0 ? null : timed.percentiles())
                            .serviceLevelObjectives(timed.serviceLevelObjectives())
                            .minimumExpectedValue(timed.minimumExpectedValue())
                            .maximumExpectedValue(timed.maximumExpectedValue())
                            .tags(context.tags)
                            .build());
            if (recordException) {
                Counter.builder("error_spans").description("Error Counter for each span")
                        .tag("operation", context.alias)
                        .tag("exception", exceptionClass)
                        .build().increment();
            }
        } catch (Exception e) {
            // ignoring on purpose
        }
    }

    private Object afterProcessWithLongTaskTimer(Object retVal, MethodInvocationContext methodInvocationContext, boolean stopWhenCompleted) {
        LongTaskTimerContext context = (LongTaskTimerContext) methodInvocationContext.getContext();
        Optional<LongTaskTimer.Sample> sample = Optional.ofNullable(context.sample);

        if (stopWhenCompleted) {
            try {
                return ((CompletionStage<?>) retVal)
                        .whenComplete((result, throwable) -> sample.ifPresent(this::stopTimer));
            } catch (Exception ex) {
                sample.ifPresent(this::stopTimer);
                throw ex;
            }
        }

        sample.ifPresent(this::stopTimer);
        return retVal;
    }

    private void stopTimer(LongTaskTimer.Sample sample) {
        try {
            sample.stop();
        } catch (Exception e) {
            // ignoring on purpose
        }
    }

    protected void handleTimedException(MethodInvocationContext context, Throwable t) {
        if (t != null && context.getContext() instanceof TimerContext) {
            ((TimerContext) context.getContext()).exceptionClass = getExceptionTag(t);
        }
    }

    private String getExceptionTag(Throwable throwable) {
        if (throwable == null) {
            return AbstractComponentHandler.EXCEPTION_NONE_VALUE;
        }

        if (throwable.getCause() == null) {
            return throwable.getClass().getSimpleName();
        }

        return throwable.getCause().getClass().getSimpleName();
    }

    static final class TimerContext {
        private String exceptionClass = AbstractComponentHandler.EXCEPTION_NONE_VALUE;
        private final Timer.Sample sample;
        private final Tags tags;
        private final String alias;

        public TimerContext(Timer.Sample sample, Tags tags, String alias) {
            this.sample = sample;
            this.tags = tags;
            this.alias = alias;
        }
    }

    static final class LongTaskTimerContext {
        private final LongTaskTimer.Sample sample;

        public LongTaskTimerContext(LongTaskTimer.Sample sample) {
            this.sample = sample;
        }
    }

    static final class ThrowableHolder {
        Throwable t;
    }
}
