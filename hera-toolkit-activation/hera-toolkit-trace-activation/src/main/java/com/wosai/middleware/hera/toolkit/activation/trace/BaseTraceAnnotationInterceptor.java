package com.wosai.middleware.hera.toolkit.activation.trace;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.toolkit.activation.metrics.TimedAspectImpl;
import com.wosai.middleware.hera.toolkit.activation.util.TagUtil;
import com.wosai.middleware.hera.toolkit.trace.Tag;
import com.wosai.middleware.hera.toolkit.trace.Tags;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.apache.skywalking.apm.agent.core.util.CustomizeExpression;
import org.apache.skywalking.apm.agent.core.util.MethodUtil;
import org.apache.skywalking.apm.toolkit.activation.ToolkitPluginConfig;

import java.lang.reflect.Method;
import java.util.Map;

public class BaseTraceAnnotationInterceptor extends TimedAspectImpl {
    protected void beforeMethod(Method method, Object[] allArguments, MethodInvocationContext methodInvocationContext) {
        Trace trace = method.getAnnotation(Trace.class);
        String operationName = trace.operationName();
        if (operationName.length() == 0 || ToolkitPluginConfig.Plugin.Toolkit.USE_QUALIFIED_NAME_AS_OPERATION_NAME) {
            operationName = MethodUtil.generateOperationName(method);
        }

        final AbstractHeraSpan localSpan = ContextManager.createLocalSpan(operationName);

        final Map<String, Object> context = CustomizeExpression.evaluationContext(allArguments);

        final Tags tags = method.getAnnotation(Tags.class);
        if (tags != null && tags.value().length > 0) {
            for (final Tag tag : tags.value()) {
                if (!TagUtil.isReturnTag(tag.value())) {
                    TagUtil.tagSpan(localSpan, context, tag);
                }
            }
        }
        final Tag tag = method.getAnnotation(Tag.class);
        if (tag != null && !TagUtil.isReturnTag(tag.value())) {
            TagUtil.tagSpan(localSpan, context, tag);
        }

        beforeTimedMethod(method, allArguments, methodInvocationContext);
    }

    protected void afterMethod(Method method, Object ret, MethodInvocationContext methodInvocationContext) {
        try {
            if (ret == null) {
                return;
            }
            final AbstractHeraSpan localSpan = ContextManager.activeSpan();
            final Map<String, Object> context = CustomizeExpression.evaluationReturnContext(ret);
            final Tags tags = method.getAnnotation(Tags.class);
            if (tags != null && tags.value().length > 0) {
                for (final Tag tag : tags.value()) {
                    if (TagUtil.isReturnTag(tag.value())) {
                        TagUtil.tagSpan(localSpan, context, tag);
                    }
                }
            }
            final Tag tag = method.getAnnotation(Tag.class);
            if (tag != null && TagUtil.isReturnTag(tag.value())) {
                TagUtil.tagSpan(localSpan, context, tag);
            }
        } finally {
            ContextManager.stopSpan();
            afterTimedMethod(method, ret, methodInvocationContext);
        }
    }

    protected void handleMethodException(Throwable t) {
        if (ContextManager.isActive()) {
            ContextManager.logError(t);
        }
    }
}
