package com.wosai.middleware.hera.toolkit.activation.metrics;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.ClassEnhancePluginDefineV2;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.v2.DeclaredInstanceMethodsInterceptV2Point;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.v2.InstanceMethodsInterceptV2Point;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.v2.StaticMethodsInterceptV2Point;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;

import static net.bytebuddy.matcher.ElementMatchers.isAnnotatedWith;
import static net.bytebuddy.matcher.ElementMatchers.named;
import static org.apache.skywalking.apm.agent.core.plugin.match.MethodAnnotationMatch.byMethodAnnotationMatch;
import static org.apache.skywalking.apm.agent.core.plugin.match.logical.LogicalMatchOperation.and;
import static org.apache.skywalking.apm.agent.core.plugin.match.logical.LogicalMatchOperation.not;

public class TimedAnnotationActivation extends ClassEnhancePluginDefineV2 {
    public static final String DEFAULT_METRIC_NAME = "hera_timed_method";
    public static final String TIMED_ANNOTATION_METHOD_INTERCEPTOR = "com.wosai.middleware.hera.toolkit.activation.metrics.TimedAnnotationMethodInterceptor";
    public static final String TIMED_ANNOTATION_STATIC_METHOD_INTERCEPTOR = "com.wosai.middleware.hera.toolkit.activation.metrics.TimedAnnotationStaticMethodInterceptor";
    public static final String TIMED_ANNOTATION = "com.wosai.middleware.hera.toolkit.metrics.Timed";
    public static final String TRACE_ANNOTATION = "com.wosai.middleware.hera.toolkit.trace.Trace";

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[0];
    }

    @Override
    public InstanceMethodsInterceptV2Point[] getInstanceMethodsInterceptV2Points() {
        return new InstanceMethodsInterceptV2Point[]{
                new DeclaredInstanceMethodsInterceptV2Point() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return isAnnotatedWith(named(TIMED_ANNOTATION));
                    }

                    @Override
                    public String getMethodsInterceptorV2() {
                        return TIMED_ANNOTATION_METHOD_INTERCEPTOR;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }

    @Override
    protected ClassMatch enhanceClass() {
        // only enhance method that is not annotated with @Trace
        return and(not(byMethodAnnotationMatch(TRACE_ANNOTATION)), byMethodAnnotationMatch(TIMED_ANNOTATION));
    }

    @Override
    public StaticMethodsInterceptV2Point[] getStaticMethodsInterceptV2Points() {
        return new StaticMethodsInterceptV2Point[]{
                new StaticMethodsInterceptV2Point() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return isAnnotatedWith(named(TIMED_ANNOTATION));
                    }

                    @Override
                    public String getMethodsInterceptorV2() {
                        return TIMED_ANNOTATION_STATIC_METHOD_INTERCEPTOR;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }
}
