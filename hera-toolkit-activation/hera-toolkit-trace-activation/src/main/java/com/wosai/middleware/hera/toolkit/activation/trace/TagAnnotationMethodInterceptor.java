package com.wosai.middleware.hera.toolkit.activation.trace;

import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;

import java.lang.reflect.Method;

public class TagAnnotationMethodInterceptor extends BaseTagAnnotationInterceptor implements InstanceMethodsAroundInterceptor {
    @Override
    public void beforeMethod(final EnhancedInstance objInst, final Method method, final Object[] allArguments,
                             final Class<?>[] argumentsTypes, final MethodInterceptResult result) {
        super.beforeMethod(method, allArguments);
    }

    @Override
    public Object afterMethod(
            final EnhancedInstance objInst,
            final Method method,
            final Object[] allArguments,
            final Class<?>[] argumentsTypes,
            final Object ret) {
        super.afterMethod(method, ret);
        return ret;
    }

    @Override
    public void handleMethodException(final EnhancedInstance objInst, final Method method, final Object[] allArguments,
                                      final Class<?>[] argumentsTypes, final Throwable t) {
        super.handleMethodException(t);
    }
}
