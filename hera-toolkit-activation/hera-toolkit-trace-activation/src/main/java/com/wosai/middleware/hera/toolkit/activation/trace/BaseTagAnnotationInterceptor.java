package com.wosai.middleware.hera.toolkit.activation.trace;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.toolkit.activation.util.TagUtil;
import com.wosai.middleware.hera.toolkit.trace.Tag;
import com.wosai.middleware.hera.toolkit.trace.Tags;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import org.apache.skywalking.apm.agent.core.util.CustomizeExpression;

import java.lang.reflect.Method;
import java.util.Map;

public class BaseTagAnnotationInterceptor {
    void beforeMethod(Method method, Object[] allArguments) {
        if (!ContextManager.isActive()) {
            return;
        }

        final AbstractHeraSpan activeSpan = ContextManager.activeSpan();
        final Map<String, Object> context = CustomizeExpression.evaluationContext(allArguments);

        final Tags tags = method.getAnnotation(Tags.class);
        if (tags != null && tags.value().length > 0) {
            for (final Tag tag : tags.value()) {
                if (!TagUtil.isReturnTag(tag.value())) {
                    TagUtil.tagSpan(activeSpan, context, tag);
                }
            }
        }

        final Tag tag = method.getAnnotation(Tag.class);
        if (tag != null && !TagUtil.isReturnTag(tag.value())) {
            TagUtil.tagSpan(activeSpan, context, tag);
        }
    }

    void afterMethod(Method method, Object ret) {
        if (ret == null || !ContextManager.isActive()) {
            return;
        }
        final AbstractHeraSpan localSpan = ContextManager.activeSpan();
        final Map<String, Object> context = CustomizeExpression.evaluationReturnContext(ret);
        final Tags tags = method.getAnnotation(Tags.class);
        if (tags != null && tags.value().length > 0) {
            for (final Tag tag : tags.value()) {
                if (TagUtil.isReturnTag(tag.value())) {
                    TagUtil.tagSpan(localSpan, context, tag);
                }
            }
        }
        final Tag tag = method.getAnnotation(Tag.class);
        if (tag != null && TagUtil.isReturnTag(tag.value())) {
            TagUtil.tagSpan(localSpan, context, tag);
        }
        return;
    }

    void handleMethodException(Throwable t) {
        if (ContextManager.isActive()) {
            ContextManager.logError(t);
        }
    }
}
