package com.wosai.middleware.hera.toolkit.activation.trace;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.util.ExtraTags;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;

import java.lang.reflect.Method;

public class CallableOrRunnableInvokeInterceptor implements InstanceMethodsAroundInterceptor {
    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] objects, Class<?>[] classes, MethodInterceptResult methodInterceptResult) throws Throwable {
        String operationName = "Thread/" + objInst.getClass().getName() + "/" + method.getName();
        AbstractHeraSpan parentSpan = (AbstractHeraSpan) objInst.getSkyWalkingDynamicField();
        // we always create a local span here
        AbstractHeraSpan span = ContextManager.createLocalSpan(operationName, parentSpan != null ? parentSpan.context() : null);
        span.setTag(ExtraTags.THREAD_NAME, Thread.currentThread().getName());
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Object ret) throws Throwable {
        ContextManager.stopSpan();
        enhancedInstance.setSkyWalkingDynamicField(null);
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {
        ContextManager.logError(throwable);
    }
}