package com.wosai.middleware.hera.toolkit.activation.trace;

import com.wosai.middleware.hera.agent.services.ContextManager;
import org.apache.skywalking.apm.agent.core.context.status.AnnotationMatchExceptionCheckStrategy;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;

public class IgnoredExceptionConstructInterceptor implements InstanceConstructorInterceptor {
    /**
     * Inject a tag field to mark the exception should be not thought error status.
     */
    @Override
    public void onConstruct(EnhancedInstance objInst, Object[] allArguments) {
        if (ContextManager.isActive()) {
            objInst.setSkyWalkingDynamicField(AnnotationMatchExceptionCheckStrategy.class.getSimpleName());
        }
    }
}
