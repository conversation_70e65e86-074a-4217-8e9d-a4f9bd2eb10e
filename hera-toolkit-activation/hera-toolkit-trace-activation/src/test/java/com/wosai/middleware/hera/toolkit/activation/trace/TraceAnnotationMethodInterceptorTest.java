package com.wosai.middleware.hera.toolkit.activation.trace;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.MeterId;
import com.wosai.middleware.hera.agent.metrics.api.Tags;
import com.wosai.middleware.hera.infra.HeraAgentServiceRule;
import com.wosai.middleware.hera.test.HeraInMemoryServer;
import com.wosai.middleware.hera.toolkit.activation.metrics.TimedAnnotationActivation;
import com.wosai.middleware.hera.toolkit.activation.metrics.TimedAspectImpl;
import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.middleware.hera.toolkit.trace.Tag;
import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.middleware.hera.tracing.network.NetworkSpan;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayDeque;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.entry;

public class TraceAnnotationMethodInterceptorTest {
    @Rule
    public MockitoRule rule = MockitoJUnit.rule();
    private TraceAnnotationMethodInterceptor interceptor;
    private Method tracedMethodRun;
    private Method tracedMethodRunAndReturnInt;
    private Method tracedAndTimedMethodRun;

    @Rule
    public HeraInMemoryServer heraInMemoryServer = new HeraInMemoryServer("trace-annotation");

    @Rule
    public HeraAgentServiceRule agentServiceRule = new HeraAgentServiceRule();

    @Before
    public void setup() throws NoSuchMethodException {
        interceptor = new TraceAnnotationMethodInterceptor();

        // methods
        tracedMethodRun = TracedClass.class.getDeclaredMethod("run");
        tracedMethodRunAndReturnInt = TracedClass.class.getDeclaredMethod("runAndReturnInt");
        tracedAndTimedMethodRun = TracedClass.class.getDeclaredMethod("bothTraceAndTimed");
    }

    @Test
    public void testWithTrace() throws Throwable {
        MethodInvocationContext context = new MethodInvocationContext();
        interceptor.beforeMethod(null, tracedMethodRun, new Object[]{}, new Class<?>[]{}, context);
        interceptor.afterMethod(null, tracedMethodRun, new Object[]{}, new Class<?>[]{}, null, context);

        assertThat(heraInMemoryServer.getSpans()).hasSize(1);
    }

    @Test
    public void testWithMethod_withTraceAndReturnInt() throws Throwable {
        MethodInvocationContext context = new MethodInvocationContext();
        interceptor.beforeMethod(null, tracedMethodRunAndReturnInt, new Object[]{}, new Class<?>[]{}, context);
        interceptor.afterMethod(null, tracedMethodRunAndReturnInt, new Object[]{}, new Class<?>[]{}, new TracedClass().runAndReturnInt(), context);

        List<NetworkSpan> spanList = heraInMemoryServer.getSpans();
        assertThat(spanList).hasSize(1);
        assertThat(spanList.get(0).getTags()).contains(entry("result", "2"));
    }

    @Test
    public void testWithMethod_withTraceAndTimedAnnotations() throws Throwable {
        MethodInvocationContext context = new MethodInvocationContext();
        interceptor.beforeMethod(null, tracedAndTimedMethodRun, new Object[]{}, new Class<?>[]{}, context);
        interceptor.afterMethod(null, tracedAndTimedMethodRun, new Object[]{}, new Class<?>[]{}, null, context);

        List<NetworkSpan> spanList = heraInMemoryServer.getSpans();
        assertThat(spanList).hasSize(1);
        Field field = TimedAspectImpl.class.getDeclaredField("CONTEXT_THROWABLE_HOLDER");
        field.setAccessible(true);
        ThreadLocal<ArrayDeque<?>> throwableHolderStack = (ThreadLocal<ArrayDeque<?>>) field.get(null);
        assertThat(throwableHolderStack.get()).isEmpty();
        assertThat(MetricsHandler.getMeters()).isNotEmpty();
        assertThat(MetricsHandler.find(TimedAnnotationActivation.DEFAULT_METRIC_NAME).timer().getId()).isEqualTo(
                MeterId.dummy(TimedAnnotationActivation.DEFAULT_METRIC_NAME,
                        Tags.of("alias", TracedClass.class.getName() + "#" + tracedAndTimedMethodRun.getName(),
                                "class", TracedClass.class.getName(),
                                "method", tracedAndTimedMethodRun.getName()))
        );
    }

    public static class TracedClass {

        @Trace
        public void run() {
        }

        @Trace
        @Tag(key = "result", value = "returnedObj")
        public int runAndReturnInt() {
            return 1 + 1;
        }

        @Trace
        @Timed
        public void bothTraceAndTimed() {

        }
    }
}
