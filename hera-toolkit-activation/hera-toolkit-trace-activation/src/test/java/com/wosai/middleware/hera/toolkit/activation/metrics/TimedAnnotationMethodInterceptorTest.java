package com.wosai.middleware.hera.toolkit.activation.metrics;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.BaseMeter;
import com.wosai.middleware.hera.agent.metrics.api.MeterId;
import com.wosai.middleware.hera.agent.metrics.api.Tags;
import com.wosai.middleware.hera.test.HeraInMemoryMetrics;
import com.wosai.middleware.hera.toolkit.metrics.Timed;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.apache.skywalking.apm.agent.core.util.MethodUtil;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.awaitility.Awaitility.await;

public class TimedAnnotationMethodInterceptorTest {
    @Rule
    public MockitoRule rule = MockitoJUnit.rule();

    private TimedAnnotationMethodInterceptor interceptor;

    @Rule
    public HeraInMemoryMetrics heraInMemoryMetrics = new HeraInMemoryMetrics();

    @Before
    public void setup() {
        interceptor = new TimedAnnotationMethodInterceptor();
    }

    @Test
    public void testTimedMethod_whenPercentilesAreSpecified() {
        assertThat(MethodUtil.isMethodExist(TimedAnnotationMethodInterceptorTest.class.getClassLoader(),
                FakeTimedWithPercentiles.class.getName(),
                "percentiles")).isTrue();
    }

    @Test
    public void testTimedMethod_whenNoPercentileIsSpecified() {
        assertThat(MethodUtil.isMethodExist(TimedAnnotationMethodInterceptorTest.class.getClassLoader(),
                FakeTimedWithoutPercentiles.class.getName(),
                "percentiles")).isFalse();
    }

    @Test
    public void testTimedMethod_smoke() throws Throwable {
        Class<?> cache = Class.forName("com.wosai.middleware.hera.toolkit.activation.metrics." +
                "TimedAnnotationMethodInterceptorTest$TimedClass");
        Method timedMethodRun = cache.getDeclaredMethod("run");

        MethodInvocationContext context = new MethodInvocationContext();
        interceptor.beforeMethod(null, timedMethodRun, null, null, context);
        interceptor.afterMethod(null, timedMethodRun, null, null, null, context);
        assertThat(TimedAnnotationMethodInterceptor.CONTEXT_THROWABLE_HOLDER.get()).isEmpty();
        assertThat(MetricsHandler.getMeters()).hasSize(2);
        assertThat(MetricsHandler.find(TimedAnnotationActivation.DEFAULT_METRIC_NAME).timer().getId()).isEqualTo(
                MeterId.dummy(TimedAnnotationActivation.DEFAULT_METRIC_NAME,
                        Tags.of("alias", TimedClass.class.getName() + "#" + timedMethodRun.getName(),
                                "class", TimedClass.class.getName(),
                                "method", timedMethodRun.getName()))
        );
    }

    @Test
    public void testTimedMethod_whenExceptionThrown() throws Throwable {
        Class<?> cache = Class.forName("com.wosai.middleware.hera.toolkit.activation.metrics." +
                "TimedAnnotationMethodInterceptorTest$TimedClass");
        Method timedMethodRun = cache.getDeclaredMethod("run");

        MethodInvocationContext context = new MethodInvocationContext();
        interceptor.beforeMethod(null, timedMethodRun, null, null, context);
        interceptor.handleMethodException(null, timedMethodRun, null, null, new RuntimeException("NPE"), context);
        interceptor.afterMethod(null, timedMethodRun, null, null, null, context);
        assertThat(TimedAnnotationMethodInterceptor.CONTEXT_THROWABLE_HOLDER.get()).isEmpty();
        assertThat(MetricsHandler.getMeters()).hasSize(2);
    }

    @Test
    public void testTimedMethod_whenDynamicAliasIsUsed() throws Throwable {
        MethodInvocationContext context = new MethodInvocationContext();
        Class<?> cache = Class.forName("com.wosai.middleware.hera.toolkit.activation.metrics." +
                "TimedAnnotationMethodInterceptorTest$TimedClass");
        Method timedMethodRunWithArg = cache.getDeclaredMethod("runWithName", String.class);

        interceptor.beforeMethod(null, timedMethodRunWithArg, new Object[]{"customName"}, new Class[]{String.class}, context);
        interceptor.handleMethodException(null, timedMethodRunWithArg, new Object[]{"customName"}, new Class[]{String.class}, new RuntimeException("NPE"), context);
        interceptor.afterMethod(null, timedMethodRunWithArg, new Object[]{"customName"}, new Class[]{String.class}, null, context);
        assertThat(TimedAnnotationMethodInterceptor.CONTEXT_THROWABLE_HOLDER.get()).isEmpty();
        assertThat(MetricsHandler.getMeters()).hasSize(2);
        assertThat(MetricsHandler.getMeterRegistry().getMeters().get(0).getId().getTag("alias")).isEqualTo("customName");
    }

    @Test
    public void testTimedMethod_whenAliasIsStatic() throws Throwable {
        MethodInvocationContext context = new MethodInvocationContext();
        Class<?> cache = Class.forName("com.wosai.middleware.hera.toolkit.activation.metrics." +
                "TimedAnnotationMethodInterceptorTest$TimedClass");
        Method timedMethodRunWithArg = cache.getDeclaredMethod("runWithStaticValue", String.class);

        interceptor.beforeMethod(null, timedMethodRunWithArg, new Object[]{"customName"}, new Class[]{String.class}, context);
        interceptor.handleMethodException(null, timedMethodRunWithArg, new Object[]{"customName"}, new Class[]{String.class}, new RuntimeException("NPE"), context);
        interceptor.afterMethod(null, timedMethodRunWithArg, new Object[]{"customName"}, new Class[]{String.class}, null, context);
        assertThat(TimedAnnotationMethodInterceptor.CONTEXT_THROWABLE_HOLDER.get()).isEmpty();
        assertThat(MetricsHandler.getMeters()).hasSize(2);
        assertThat(MetricsHandler.getMeterRegistry().getMeters().get(0).getId().getTag("alias")).isEqualTo("anyway");
    }

    @Test
    public void testTimedMethod_whenArgIsOutOfBound() throws Throwable {
        MethodInvocationContext context = new MethodInvocationContext();
        Class<?> cache = Class.forName("com.wosai.middleware.hera.toolkit.activation.metrics." +
                "TimedAnnotationMethodInterceptorTest$TimedClass");
        final Method timedMethodRunWithArg = cache.getDeclaredMethod("runWithArgOutOfBound", String.class);

        interceptor.beforeMethod(null, timedMethodRunWithArg, new Object[]{"customName"}, new Class[]{String.class}, context);
        interceptor.handleMethodException(null, timedMethodRunWithArg, new Object[]{"customName"}, new Class[]{String.class}, new RuntimeException("NPE"), context);
        interceptor.afterMethod(null, timedMethodRunWithArg, new Object[]{"customName"}, new Class[]{String.class}, null, context);
        assertThat(TimedAnnotationMethodInterceptor.CONTEXT_THROWABLE_HOLDER.get()).isEmpty();
        assertThat(MetricsHandler.getMeters()).hasSize(2);
        assertThat(MetricsHandler.find(TimedAnnotationActivation.DEFAULT_METRIC_NAME).timer().getId()).isEqualTo(
                MeterId.dummy(TimedAnnotationActivation.DEFAULT_METRIC_NAME,
                        Tags.of("alias", "null",
                                "class", TimedClass.class.getName(),
                                "method", timedMethodRunWithArg.getName()))
        );
    }

    @Test
    public void testTimedMethod_whenAsyncMethodIsMeasured() throws Throwable {
        MethodInvocationContext context = new MethodInvocationContext();
        Class<?> cache = Class.forName("com.wosai.middleware.hera.toolkit.activation.metrics." +
                "TimedAnnotationMethodInterceptorTest$TimedClass");
        final Method asyncTimedMethod = cache.getDeclaredMethod("runWithAsync");

        CompletableFuture<String> f = new TimedClass().runWithAsync();
        interceptor.beforeMethod(null, asyncTimedMethod, new Object[0], new Class[0], context);
        interceptor.handleMethodException(null, asyncTimedMethod, new Object[0], new Class[0], null, context);
        interceptor.afterMethod(null, asyncTimedMethod, new Object[0], new Class[0], f, context);
        String asyncResult = f.get();
        assertThat(asyncResult).isEqualTo("HelloWorld");
        assertThat(TimedAnnotationMethodInterceptor.CONTEXT_THROWABLE_HOLDER.get()).isEmpty();
        // wait for ready
        await().atMost(Duration.ofSeconds(5)).untilAsserted(() ->
                assertThat(MetricsHandler.getMeters()).hasSize(2));
        assertThat(MetricsHandler.find(TimedAnnotationActivation.DEFAULT_METRIC_NAME).timer()).isNotNull()
                .satisfies(t -> {
                    assertThat(t.getId()).isEqualTo(MeterId.dummy(TimedAnnotationActivation.DEFAULT_METRIC_NAME,
                            Tags.of("alias", TimedClass.class.getName() + "#" + asyncTimedMethod.getName(),
                                    "class", TimedClass.class.getName(),
                                    "method", asyncTimedMethod.getName())));
                    assertThat(t.totalTime(TimeUnit.MILLISECONDS)).isGreaterThan(500);
                });
    }

    @Test
    public void testTimedMethod_whenAsyncMethodThrowsUncheckedException() throws Throwable {
        MethodInvocationContext context = new MethodInvocationContext();
        Class<?> cache = Class.forName("com.wosai.middleware.hera.toolkit.activation.metrics." +
                "TimedAnnotationMethodInterceptorTest$TimedClass");
        final Method asyncTimedMethod = cache.getDeclaredMethod("runWithAsyncAndThrowException");

        CompletableFuture<String> f = new TimedClass().runWithAsyncAndThrowException();
        interceptor.beforeMethod(null, asyncTimedMethod, new Object[0], new Class[0], context);
        interceptor.handleMethodException(null, asyncTimedMethod, new Object[0], new Class[0], null, context);
        interceptor.afterMethod(null, asyncTimedMethod, new Object[0], new Class[0], f, context);
        assertThatThrownBy(f::get)
                .hasCauseInstanceOf(IllegalStateException.class);
        assertThat(TimedAnnotationMethodInterceptor.CONTEXT_THROWABLE_HOLDER.get()).isEmpty();
        // wait for ready
        await().atMost(Duration.ofSeconds(5)).untilAsserted(() ->
                assertThat(MetricsHandler.getMeters()).hasSize(2));
        assertThat(MetricsHandler.find(TimedAnnotationActivation.DEFAULT_METRIC_NAME).timer())
                .isNotNull().extracting(BaseMeter::getId).isEqualTo(
                        MeterId.dummy(TimedAnnotationActivation.DEFAULT_METRIC_NAME,
                                Tags.of("alias", TimedClass.class.getName() + "#" + asyncTimedMethod.getName(),
                                        "class", TimedClass.class.getName(),
                                        "method", asyncTimedMethod.getName()))
                );
        assertThat(MetricsHandler.find("error_spans").counter())
                .isNotNull().satisfies(c -> {
                    assertThat(c.getId().getTag("exception")).isNotEmpty().isEqualTo("IllegalStateException");
                    assertThat(c.count()).isEqualTo(1);
                });
    }

    public @interface FakeTimedWithPercentiles {
        String value() default "";

        double[] percentiles() default {};
    }

    public @interface FakeTimedWithoutPercentiles {
        String value() default "";
    }

    public static class TimedClass {
        @Timed
        public void run() {
        }

        @Timed(value = "arg[0]")
        public void runWithName(String name) {
        }

        @Timed(value = "anyway")
        public void runWithStaticValue(String name) {

        }

        @Timed
        public CompletableFuture<String> runWithAsync() {
            return CompletableFuture.supplyAsync(() -> {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException ignored) {
                }
                return "HelloWorld";
            });
        }

        @Timed
        public CompletableFuture<String> runWithAsyncAndThrowException() {
            return CompletableFuture.supplyAsync(() -> {
                throw new IllegalStateException("invalid state");
            });
        }

        @Timed(value = "arg[1]")
        public void runWithArgOutOfBound(String name) {

        }
    }
}
