package com.wosai.middleware.hera.toolkit.activation.metrics;

public abstract class ServletSupport {
    private static final String SERVLET_REQUEST_CLASS = "javax.servlet.http.HttpServletRequest";
    static boolean IN_SERVLET_CONTAINER;
    static final String REQUEST_KEY_IN_RUNTIME_CONTEXT = "SW_REQUEST";

    static {
        try {
            Class.forName(SERVLET_REQUEST_CLASS, true, ServletSupport.class.getClassLoader());
            IN_SERVLET_CONTAINER = true;
        } catch (Exception ignore) {
            IN_SERVLET_CONTAINER = false;
        }
    }
}
