package com.wosai.middleware.hera.toolkit.activation.metrics;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.StaticMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.ClassStaticMethodsEnhancePluginDefine;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;
import org.apache.skywalking.apm.agent.core.plugin.match.NameMatch;

import java.util.List;

import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.returns;
import static net.bytebuddy.matcher.ElementMatchers.takesArgument;
import static net.bytebuddy.matcher.ElementMatchers.takesArguments;

public class MetricsManagerActivation extends ClassStaticMethodsEnhancePluginDefine {
    private static final String ENHANCE_CLASS = "com.wosai.middleware.hera.toolkit.metrics.MetricsManager";
    private static final String SCRAPE_METHOD_NAME = "scrape";
    private static final String GET_METERS_METHOD_NAME = "getMeters";
    private static final String SCRAPE_INTERCEPTOR_NAME = "com.wosai.middleware.hera.toolkit.activation.metrics.ScrapeInterceptor";
    private static final String SCRAPE_WRITER_INTERCEPTOR_NAME = "com.wosai.middleware.hera.toolkit.activation.metrics.ScrapeWriterInterceptor";
    private static final String GET_METERS_INTERCEPTOR_NAME = "com.wosai.middleware.hera.toolkit.activation.metrics.MeterDebugInterceptor";

    @Override
    protected ClassMatch enhanceClass() {
        return NameMatch.byName(ENHANCE_CLASS);
    }

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[0];
    }

    @Override
    public StaticMethodsInterceptPoint[] getStaticMethodsInterceptPoints() {
        return new StaticMethodsInterceptPoint[]{
                new StaticMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(SCRAPE_METHOD_NAME).and(takesArguments(0))
                                .or(named(SCRAPE_METHOD_NAME).and(takesArgument(0, String.class)));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return SCRAPE_INTERCEPTOR_NAME;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new StaticMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(SCRAPE_METHOD_NAME).and(takesArgument(0, named("java.io.Writer")));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return SCRAPE_WRITER_INTERCEPTOR_NAME;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new StaticMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(GET_METERS_METHOD_NAME).and(returns(List.class));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return GET_METERS_INTERCEPTOR_NAME;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }
}
