package com.wosai.middleware.hera.toolkit.activation.metrics;

import com.wosai.middleware.hera.agent.conf.bootstrap.KubernetesContext;
import com.wosai.middleware.hera.agent.conf.bootstrap.KubernetesRunLevel;
import com.wosai.middleware.hera.agent.conf.bootstrap.RunLevelManager;
import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.services.ContextManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.StaticMethodsAroundInterceptor;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.Writer;
import java.lang.reflect.Method;
import java.util.Optional;

public class ScrapeWriterInterceptor extends ServletSupport implements StaticMethodsAroundInterceptor {
    @Override
    public void beforeMethod(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes,
                             MethodInterceptResult result) {
        Writer writer = (Writer) allArguments[0];
        if (allArguments.length == 1) {
            Optional<KubernetesContext> opt = RunLevelManager.INSTANCE.isRunningWithLevel(KubernetesRunLevel.NAME);
            if (opt.isPresent() && IN_SERVLET_CONTAINER) { // if it is running in Kubernetes
                // NOTICE: this is a hack to read SW_REQUEST set in Spring plugin
                Object maybeRequest = ContextManager.getRuntimeContext().get(REQUEST_KEY_IN_RUNTIME_CONTEXT);
                if (maybeRequest != null && HttpServletRequest.class.isAssignableFrom(maybeRequest.getClass())) {
                    final String authz = ((HttpServletRequest) maybeRequest).getHeader("Authorization");
                    if (MetricsHandler.checkToken(authz)) {
                        MetricsHandler.scrape(writer);
                        return; // only successful leaf in this branch
                    }
                }
                safeExitWithInvalidRequest(writer);
            } else {
                MetricsHandler.scrape(writer);
            }
        } else if (allArguments.length == 2) {
            String token = (String) allArguments[1];
            if (MetricsHandler.checkToken(token)) {
                MetricsHandler.scrape(writer);
            } else {
                safeExitWithInvalidRequest(writer);
            }
        }
    }

    private void safeExitWithInvalidRequest(Writer writer) {
        try {
            writer.write("invalid request");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Object afterMethod(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes,
                              Object ret) {
        return ret;
    }

    @Override
    public void handleMethodException(Class aClass, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {

    }
}
