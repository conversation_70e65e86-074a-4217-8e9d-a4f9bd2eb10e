package com.wosai.middleware.hera.toolkit.activation.metrics;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.Tag;
import com.wosai.middleware.hera.toolkit.metrics.MeterMeasurement;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.StaticMethodsAroundInterceptor;
import org.apache.skywalking.apm.dependencies.com.google.common.collect.Iterables;
import org.apache.skywalking.apm.dependencies.com.google.common.collect.Streams;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MeterDebugInterceptor implements StaticMethodsAroundInterceptor {
    @Override
    public void beforeMethod(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes,
                             MethodInterceptResult result) {

    }

    @Override
    public Object afterMethod(Class clazz, Method method, Object[] allArguments, Class<?>[] parameterTypes,
                              Object ret) {
        final String metricName = (String) allArguments[0];
        return MetricsHandler.getMeters(m -> m.getId().getName().equals(metricName)).stream().flatMap(baseMeter -> {
            final Map<String, String> tagMap = transformTags(baseMeter.getId().getTags());
            return Streams.stream(Iterables.transform(baseMeter.measure(), measurement ->
                    new MeterMeasurement(baseMeter.getId().getName(), tagMap,
                            measurement.getStatistic().getTagValueRepresentation(), measurement.getValue())));
        }).collect(Collectors.toList());
    }

    @Override
    public void handleMethodException(Class aClass, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {

    }

    static Map<String, String> transformTags(List<Tag> tags) {
        if (tags == null || tags.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, String> tagMap = new HashMap<>();
        for (final Tag tag : tags) {
            tagMap.put(tag.getKey(), tag.getValue());
        }
        return tagMap;
    }
}
