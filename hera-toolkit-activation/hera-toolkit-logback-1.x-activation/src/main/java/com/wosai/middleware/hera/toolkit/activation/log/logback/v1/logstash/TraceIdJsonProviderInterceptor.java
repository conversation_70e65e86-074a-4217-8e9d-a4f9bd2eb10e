package com.wosai.middleware.hera.toolkit.activation.log.logback.v1.logstash;

import com.wosai.middleware.hera.agent.services.ContextManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;

import java.lang.reflect.Method;

public class TraceIdJsonProviderInterceptor implements InstanceMethodsAroundInterceptor {
    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Throwable {

    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) throws Throwable {
        if (ret != null && !"N/A".equals(ret)) {
            return ret;
        }
        if (!ContextManager.isActive() && allArguments[0] instanceof EnhancedInstance) {
            String tid = (String) ((EnhancedInstance) allArguments[0]).getSkyWalkingDynamicField();
            if (tid != null) {
                return tid;
            }
        }
        return ContextManager.getTraceId();
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {

    }
}
