package com.wosai.middleware.hera.toolkit.activation.log.logback.v1.async;

import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;

/**
 * LoggingEvent implements ILoggingEvent, which is a message in the blockingQueue of the AsyncAppenderBase.class. The
 * LoggingEvent is enhanced to carry the tid in the synchronization thread using `dynamicField`.
 */

public class LoggingEventConstructorInterceptor implements InstanceConstructorInterceptor {

    @Override
    public void onConstruct(EnhancedInstance objInst, Object[] allArguments) {
    }
}
