package com.wosai.middleware.hera.toolkit.activation.threadpool;

import org.junit.Test;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.SynchronousQueue;

import static org.assertj.core.api.Assertions.assertThat;

public class BlockingQueueCapacityExtractorTest {
    BlockingQueue<?> blockingQueue;

    @Test
    public void testArrayBlockingQueue() throws Exception {
        blockingQueue = new ArrayBlockingQueue<Runnable>(10);
        assertThat(BlockingQueueCapacityExtractor.Extractor.ARRAY_BLOCKING_QUEUE.extract(blockingQueue)).isEqualTo(10);
    }

    @Test
    public void testLinkedBlockingQueue() throws Exception {
        blockingQueue = new LinkedBlockingQueue<>(20);
        assertThat(BlockingQueueCapacityExtractor.Extractor.LINKED_BLOCKING_QUEUE.extract(blockingQueue)).isEqualTo(20);
    }

    @Test
    public void testSynchronousQueue() throws Exception {
        blockingQueue = new SynchronousQueue<>();
        assertThat(BlockingQueueCapacityExtractor.Extractor.SYNCHRONOUS_QUEUE.extract(blockingQueue)).isEqualTo(0);
    }

    @Test
    public void testDelayedQueue() throws Exception {
        ScheduledThreadPoolExecutor executorService = (ScheduledThreadPoolExecutor) Executors.newScheduledThreadPool(10);
        assertThat(BlockingQueueCapacityExtractor.Extractor.UNBOUNDED_QUEUE.extract(executorService.getQueue())).isEqualTo(Integer.MAX_VALUE);
    }
}
