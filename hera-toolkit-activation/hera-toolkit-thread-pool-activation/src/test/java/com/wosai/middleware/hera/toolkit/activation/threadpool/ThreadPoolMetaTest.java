package com.wosai.middleware.hera.toolkit.activation.threadpool;

import org.junit.Test;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;

public class ThreadPoolMetaTest {
    @Test
    public void testCachedExecutorService() {
        ExecutorService executorService = Executors.newCachedThreadPool();
        ThreadPoolMeta meta = ThreadPoolMeta.parse(executorService, "pool");
        assertThat(meta).extracting("workQueueClass").isEqualTo(SynchronousQueue.class);
        assertThat(meta).extracting("name").isEqualTo("pool");
        assertThat(meta).extracting("corePoolSize").isEqualTo(0);
        assertThat(meta).extracting("maxPoolSize").isEqualTo(Integer.MAX_VALUE);
        assertThat(meta).extracting("workQueueCapacity").isEqualTo(0);
        assertThat(meta).extracting("keepAliveTimeMilli").isEqualTo(60000L);
    }

    @Test
    public void testFixedExecutorService() {
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        ThreadPoolMeta meta = ThreadPoolMeta.parse(executorService, "pool");
        assertThat(meta).extracting("workQueueClass").isEqualTo(LinkedBlockingQueue.class);
        assertThat(meta).extracting("name").isEqualTo("pool");
        assertThat(meta).extracting("corePoolSize").isEqualTo(10);
        assertThat(meta).extracting("maxPoolSize").isEqualTo(10);
        assertThat(meta).extracting("workQueueCapacity").isEqualTo(Integer.MAX_VALUE);
        assertThat(meta).extracting("keepAliveTimeMilli").isEqualTo(0L);
        assertThat(meta).extracting("rejectedExecutionPolicy").isEqualTo(ThreadPoolExecutor.AbortPolicy.class);
    }

    @Test
    public void testSingleThreadScheduledExecutor() {
        ExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
        ThreadPoolMeta meta = ThreadPoolMeta.parse(executorService, "pool");
        assertThat(meta.getWorkQueueClass().getName()).isEqualTo("java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue");
        assertThat(meta).extracting("name").isEqualTo("pool");
        assertThat(meta).extracting("corePoolSize").isEqualTo(1);
        assertThat(meta).extracting("maxPoolSize").isEqualTo(Integer.MAX_VALUE);
        assertThat(meta).extracting("workQueueCapacity").isEqualTo(Integer.MAX_VALUE);
        assertThat(meta).extracting("keepAliveTimeMilli").isEqualTo(10L);
        assertThat(meta).extracting("rejectedExecutionPolicy").isEqualTo(ThreadPoolExecutor.AbortPolicy.class);
    }

    @Test
    public void testScheduledThreadPool() {
        ExecutorService executorService = Executors.newScheduledThreadPool(10);
        ThreadPoolMeta meta = ThreadPoolMeta.parse(executorService, "pool");
        assertThat(meta.getWorkQueueClass().getName()).isEqualTo("java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue");
        assertThat(meta).extracting("name").isEqualTo("pool");
        assertThat(meta).extracting("corePoolSize").isEqualTo(10);
        assertThat(meta).extracting("maxPoolSize").isEqualTo(Integer.MAX_VALUE);
        assertThat(meta).extracting("workQueueCapacity").isEqualTo(Integer.MAX_VALUE);
        assertThat(meta).extracting("keepAliveTimeMilli").isEqualTo(10L);
        assertThat(meta).extracting("rejectedExecutionPolicy").isEqualTo(ThreadPoolExecutor.AbortPolicy.class);
    }

    @Test
    public void testCustomThreadPool_givenArrayBlockingQueue() {
        ExecutorService executorService = new ThreadPoolExecutor(5, 10, 53, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(11), new ThreadPoolExecutor.CallerRunsPolicy());
        ThreadPoolMeta meta = ThreadPoolMeta.parse(executorService, "pool");
        assertThat(meta.getWorkQueueClass()).isEqualTo(ArrayBlockingQueue.class);
        assertThat(meta).extracting("name").isEqualTo("pool");
        assertThat(meta).extracting("corePoolSize").isEqualTo(5);
        assertThat(meta).extracting("maxPoolSize").isEqualTo(10);
        assertThat(meta).extracting("workQueueCapacity").isEqualTo(11);
        assertThat(meta).extracting("keepAliveTimeMilli").isEqualTo(53L);
        assertThat(meta).extracting("rejectedExecutionPolicy").isEqualTo(ThreadPoolExecutor.CallerRunsPolicy.class);
    }

    @Test
    public void testCustomThreadPool_givenLinkedBlockingQueue() {
        ExecutorService executorService = new ThreadPoolExecutor(5, 10, 53, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(11), new ThreadPoolExecutor.DiscardPolicy());
        ThreadPoolMeta meta = ThreadPoolMeta.parse(executorService, "pool");
        assertThat(meta.getWorkQueueClass()).isEqualTo(LinkedBlockingQueue.class);
        assertThat(meta).extracting("name").isEqualTo("pool");
        assertThat(meta).extracting("corePoolSize").isEqualTo(5);
        assertThat(meta).extracting("maxPoolSize").isEqualTo(10);
        assertThat(meta).extracting("workQueueCapacity").isEqualTo(11);
        assertThat(meta).extracting("keepAliveTimeMilli").isEqualTo(53L);
        assertThat(meta).extracting("rejectedExecutionPolicy").isEqualTo(ThreadPoolExecutor.DiscardPolicy.class);
    }
}
