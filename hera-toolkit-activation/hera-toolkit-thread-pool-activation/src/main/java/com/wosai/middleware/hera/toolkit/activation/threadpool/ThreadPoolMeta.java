package com.wosai.middleware.hera.toolkit.activation.threadpool;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import java.lang.reflect.Field;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.LinkedTransferQueue;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Getter
@Builder
@Slf4j
@ToString
class ThreadPoolMeta {
    private final String name;
    private final Class<?> workQueueClass;
    private final Integer workQueueCapacity;
    private final Integer corePoolSize;
    private final Integer maxPoolSize;
    private final Long keepAliveTimeMilli;
    private final Class<?> rejectedExecutionPolicy;

    public static ThreadPoolMeta parse(final ExecutorService executorService, final String name) {
        ThreadPoolMeta.ThreadPoolMetaBuilder builder = ThreadPoolMeta.builder()
                .name(name);
        if (executorService == null) {
            return builder.build();
        }

        if (executorService instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor executor = (ThreadPoolExecutor) executorService;
            Class<?> clazz = executor.getQueue().getClass();
            builder.maxPoolSize(executor.getMaximumPoolSize())
                    .corePoolSize(executor.getCorePoolSize())
                    .keepAliveTimeMilli(executor.getKeepAliveTime(TimeUnit.MILLISECONDS))
                    .workQueueClass(clazz)
                    .rejectedExecutionPolicy(executor.getRejectedExecutionHandler().getClass());
            try {
                if (clazz.isAssignableFrom(ArrayBlockingQueue.class)) {
                    builder.workQueueCapacity(BlockingQueueCapacityExtractor.Extractor.ARRAY_BLOCKING_QUEUE.extract(executor.getQueue()));
                } else if (clazz.isAssignableFrom(LinkedBlockingQueue.class)) {
                    builder.workQueueCapacity(BlockingQueueCapacityExtractor.Extractor.LINKED_BLOCKING_QUEUE.extract(executor.getQueue()));
                } else if (clazz.isAssignableFrom(SynchronousQueue.class)) {
                    builder.workQueueCapacity(BlockingQueueCapacityExtractor.Extractor.SYNCHRONOUS_QUEUE.extract(executor.getQueue()));
                } else if (clazz.isAssignableFrom(PriorityBlockingQueue.class)
                        || clazz.getName().equals("java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue")
                        || clazz.isAssignableFrom(LinkedTransferQueue.class)) {
                    builder.workQueueCapacity(BlockingQueueCapacityExtractor.Extractor.UNBOUNDED_QUEUE.extract(executor.getQueue()));
                } else {
                    log.warn("unable to extract for {}", clazz);
                }
            } catch (Exception ex) {
                log.error("unable to extract queue size", ex);
            }
            return builder.build();
        } else if (executorService.getClass().getName().equals("java.util.concurrent.Executors$DelegatedScheduledExecutorService")) {
            return parse(unwrapThreadPoolExecutor(executorService, executorService.getClass()), name);
        } else if (executorService.getClass().getName().equals("java.util.concurrent.Executors$FinalizableDelegatedExecutorService")) {
            return parse(unwrapThreadPoolExecutor(executorService, executorService.getClass().getSuperclass()), name);
        } else {
            return builder.build();
        }
    }

    @Nullable
    private static ThreadPoolExecutor unwrapThreadPoolExecutor(ExecutorService executor, Class<?> wrapper) {
        try {
            Field e = wrapper.getDeclaredField("e");
            e.setAccessible(true);
            return (ThreadPoolExecutor) e.get(executor);
        } catch (NoSuchFieldException | IllegalAccessException | RuntimeException e) {
            // Cannot use InaccessibleObjectException since it was introduced in Java 9, so catch all RuntimeExceptions instead
            // Do nothing. We simply can't get to the underlying ThreadPoolExecutor.
            log.info("Cannot unwrap ThreadPoolExecutor for monitoring from {} due to {}: {}", wrapper.getName(), e.getClass().getName(), e.getMessage());
        }
        return null;
    }
}
