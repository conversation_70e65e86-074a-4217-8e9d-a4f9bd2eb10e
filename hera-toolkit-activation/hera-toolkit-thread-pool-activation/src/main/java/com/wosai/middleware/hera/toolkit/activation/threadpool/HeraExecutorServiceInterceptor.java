package com.wosai.middleware.hera.toolkit.activation.threadpool;

import com.wosai.middleware.hera.agent.metrics.api.Timer;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

public class HeraExecutorServiceInterceptor implements InstanceMethodsAroundInterceptor {
    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] allArguments, Class<?>[] classes, MethodInterceptResult methodInterceptResult) throws Throwable {
        Object firstArg = allArguments[0];
        if (firstArg == null || !(enhancedInstance.getSkyWalkingDynamicField() instanceof ThreadPoolMeta)) {
            return;
        }

        if (firstArg instanceof Runnable) {
            allArguments[0] = wrap((Runnable) firstArg, (ThreadPoolMeta) enhancedInstance.getSkyWalkingDynamicField());
        } else if (firstArg instanceof Callable) {
            allArguments[0] = wrap((Callable<?>) firstArg, (ThreadPoolMeta) enhancedInstance.getSkyWalkingDynamicField());
        } else if (firstArg instanceof Collection) {
            allArguments[0] = ((Collection<Callable<?>>) firstArg).stream()
                    .map(it -> wrap(it, (ThreadPoolMeta) enhancedInstance.getSkyWalkingDynamicField()))
                    .collect(Collectors.toList());
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Object ret) throws Throwable {
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {
    }

    private static <V> Callable<V> wrap(Callable<V> callable, ThreadPoolMeta meta) {
        return Timer.builder("executor.tasks").tag("name", meta.getName()).build().wrap(callable);
    }

    private static Runnable wrap(Runnable runnable, ThreadPoolMeta meta) {
        return Timer.builder("executor.tasks").tag("name", meta.getName()).build().wrap(runnable);
    }
}
