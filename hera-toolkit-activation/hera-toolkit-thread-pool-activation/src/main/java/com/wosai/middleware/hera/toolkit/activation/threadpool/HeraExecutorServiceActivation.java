package com.wosai.middleware.hera.toolkit.activation.threadpool;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.InstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.ClassInstanceMethodsEnhancePluginDefine;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;
import org.apache.skywalking.apm.agent.core.plugin.match.NameMatch;

import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.takesArguments;
import static org.apache.skywalking.apm.agent.core.plugin.bytebuddy.ArgumentTypeNameMatch.takesArgumentWithType;

public class HeraExecutorServiceActivation extends ClassInstanceMethodsEnhancePluginDefine {
    private static final String ENHANCE_CLASS = "com.wosai.middleware.hera.toolkit.threadpool.HeraExecutorService";

    private static final String CONSTRUCTOR_INTERCEPTOR_FLAG = "java.util.concurrent.ExecutorService";
    private static final String CONSTRUCTOR_INTERCEPTOR = "com.wosai.middleware.hera.toolkit.activation.threadpool.HeraExecutorServiceConstructorInterceptor";

    private static final String METHOD_INTERCEPTOR = "com.wosai.middleware.hera.toolkit.activation.threadpool.HeraExecutorServiceInterceptor";

    private static final String METHOD_SUBMIT = "submit";
    private static final String METHOD_INVOKE_ALL = "invokeAll";
    private static final String METHOD_INVOKE_ANY = "invokeAny";
    private static final String METHOD_EXECUTE = "execute";

    @Override
    protected ClassMatch enhanceClass() {
        return NameMatch.byName(ENHANCE_CLASS);
    }

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[]{
                new ConstructorInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getConstructorMatcher() {
                        return takesArguments(2).and(takesArgumentWithType(0, CONSTRUCTOR_INTERCEPTOR_FLAG));
                    }

                    @Override
                    public String getConstructorInterceptor() {
                        return CONSTRUCTOR_INTERCEPTOR;
                    }
                }
        };
    }

    @Override
    public InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints() {
        return new InstanceMethodsInterceptPoint[]{
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(METHOD_EXECUTE).or(named(METHOD_INVOKE_ANY)).or(named(METHOD_SUBMIT)).or(named(METHOD_INVOKE_ALL));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return METHOD_INTERCEPTOR;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return true;
                    }
                }
        };
    }
}
