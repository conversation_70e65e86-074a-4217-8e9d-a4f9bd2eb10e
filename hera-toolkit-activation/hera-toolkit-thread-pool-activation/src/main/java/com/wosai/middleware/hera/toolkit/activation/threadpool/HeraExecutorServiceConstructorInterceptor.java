package com.wosai.middleware.hera.toolkit.activation.threadpool;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.Counter;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;
import org.apache.skywalking.apm.util.StringUtil;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
public class HeraExecutorServiceConstructorInterceptor implements InstanceConstructorInterceptor {
    @Override
    public void onConstruct(EnhancedInstance enhancedInstance, Object[] allArguments) throws Throwable {
        ExecutorService executorService = (ExecutorService) allArguments[0];
        String executorServiceName = (String) allArguments[1];
        if (executorService != null && StringUtil.isNotEmpty(executorServiceName)) {
            ThreadPoolMeta meta = ThreadPoolMeta.parse(executorService, executorServiceName);
            log.info("An executorService creation is intercepted with meta {}", meta);
            enhancedInstance.setSkyWalkingDynamicField(meta);
            MetricsHandler.bind(new ExecutorServiceMetrics(executorService, executorServiceName));
            if (executorService instanceof ThreadPoolExecutor) {
                // once the rejected handler is called, we need to record it
                RejectedExecutionHandler handler = new RejectedExecutionRecorder(((ThreadPoolExecutor) executorService).getRejectedExecutionHandler(), meta);
                ((ThreadPoolExecutor) executorService).setRejectedExecutionHandler(handler);
                // TODO(megrez): also bind ExecutorService to the agent if this executorService is an instance of ThreadPoolExecutor
            }
        }
    }

    static class RejectedExecutionRecorder implements RejectedExecutionHandler {
        private final RejectedExecutionHandler delegate;
        private final ThreadPoolMeta meta;

        public RejectedExecutionRecorder(RejectedExecutionHandler delegate, ThreadPoolMeta meta) {
            this.delegate = delegate;
            this.meta = meta;
        }

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            Counter.builder("executor.rejected.tasks.total")
                    .tag("name", this.meta.getName())
                    .description("An estimate of the number of tasked that have been rejected regardless of the handler type")
                    .build()
                    .increment();
            this.delegate.rejectedExecution(r, executor);
        }
    }
}
