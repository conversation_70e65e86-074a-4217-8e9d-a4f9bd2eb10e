package com.wosai.middleware.hera.toolkit.activation.env.define;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.StaticMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.ClassStaticMethodsEnhancePluginDefine;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;

import static org.apache.skywalking.apm.agent.core.plugin.match.NameMatch.byName;
import static net.bytebuddy.matcher.ElementMatchers.named;

public class EnvironmentInfoServiceActivation extends ClassStaticMethodsEnhancePluginDefine {

    public static final String ENV_TOOLKIT_INTERCEPTOR = "com.wosai.middleware.hera.toolkit.activation.env.EnvironmentInfoServiceInterceptor";
    public static final String ENV_TOOLKIT_METHOD = "getEnvInformation";
    public static final String ENV_TOOLKIT_CLASS = "com.wosai.middleware.hera.toolkit.env.EnvironmentInformationService";

    @Override
    protected ClassMatch enhanceClass() {
        return byName(ENV_TOOLKIT_CLASS);
    }

    @Override
    public StaticMethodsInterceptPoint[] getStaticMethodsInterceptPoints() {
        return new StaticMethodsInterceptPoint[] {
                new StaticMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(ENV_TOOLKIT_METHOD);
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return ENV_TOOLKIT_INTERCEPTOR;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return true;
                    }
                }
        };
    }
}
