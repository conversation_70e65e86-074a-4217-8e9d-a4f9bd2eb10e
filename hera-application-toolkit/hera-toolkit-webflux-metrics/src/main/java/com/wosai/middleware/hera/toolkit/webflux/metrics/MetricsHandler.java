package com.wosai.middleware.hera.toolkit.webflux.metrics;

import com.wosai.middleware.hera.toolkit.metrics.MetricsManager;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

public class MetricsHandler {

    public Mono<ServerResponse> handleMetricsRequest(ServerRequest request) {
        return ServerResponse.ok().body(Mono.just(MetricsManager.scrape()), String.class);
    }
}