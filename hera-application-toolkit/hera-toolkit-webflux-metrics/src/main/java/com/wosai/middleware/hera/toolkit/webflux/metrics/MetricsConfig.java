package com.wosai.middleware.hera.toolkit.webflux.metrics;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerResponse;

import static org.springframework.web.reactive.function.server.RequestPredicates.GET;

@Configuration
public class MetricsConfig {

    @Bean
    public RouterFunction<ServerResponse> route(MetricsHandler metricsHandler) {
        return RouterFunctions.route(GET("/metrics"), metricsHandler::handleMetricsRequest);
    }

    @Bean
    public MetricsHandler metricsHandler() {
        return new MetricsHandler();
    }
}