package com.alibaba.csp.sentinel.slots.system;

import com.alibaba.csp.sentinel.slots.block.BlockException;

/**
 * <AUTHOR>
 */
public class SystemBlockException extends BlockException {

    private final String resourceName;

    public SystemBlockException(String resourceName, String message, Throwable cause) {
        super(message, cause);
        this.resourceName = resourceName;
    }

    public SystemBlockException(String resourceName, String limitType) {
        super(limitType);
        this.resourceName = resourceName;
    }

    public String getResourceName() {
        return resourceName;
    }

    @Override
    public Throwable fillInStackTrace() {
        return this;
    }

    /**
     * Return the limit type of system rule.
     *
     * @return the limit type
     * @since 1.4.2
     */
    public String getLimitType() {
        return getRuleLimitApp();
    }
}
