package com.alibaba.csp.sentinel.slots.block.degrade;

import com.alibaba.csp.sentinel.slots.block.BlockException;

/***
 * <AUTHOR>
 */
public class DegradeException extends BlockException {

    public DegradeException(String ruleLimitApp) {
        super(ruleLimitApp);
    }

    public DegradeException(String ruleLimitApp, DegradeRule rule) {
        super(ruleLimitApp, rule);
    }

    public DegradeException(String message, Throwable cause) {
        super(message, cause);
    }

    public DegradeException(String ruleLimitApp, String message) {
        super(ruleLimitApp, message);
    }

    @Override
    public Throwable fillInStackTrace() {
        return this;
    }

    /**
     * Get triggered rule.
     * Note: the rule result is a reference to rule map and SHOULD NOT be modified.
     *
     * @return triggered rule
     * @since 1.4.2
     */
    @Override
    public DegradeRule getRule() {
        return rule.as(DegradeRule.class);
    }
}
