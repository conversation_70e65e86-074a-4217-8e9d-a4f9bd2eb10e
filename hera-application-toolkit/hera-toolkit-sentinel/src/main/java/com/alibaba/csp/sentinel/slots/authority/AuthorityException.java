package com.alibaba.csp.sentinel.slots.authority;

import com.alibaba.csp.sentinel.slots.block.BlockException;

/**
 * Block exception for request origin access (authority) control.
 *
 * <AUTHOR>
 * <AUTHOR>
 */
public class AuthorityException extends BlockException {

    public AuthorityException(String ruleLimitApp) {
        super(ruleLimitApp);
    }

    public AuthorityException(String ruleLimitApp, AuthorityRule rule) {
        super(ruleLimitApp, rule);
    }

    public AuthorityException(String message, Throwable cause) {
        super(message, cause);
    }

    public AuthorityException(String ruleLimitApp, String message) {
        super(ruleLimitApp, message);
    }

    @Override
    public Throwable fillInStackTrace() {
        return this;
    }

    /**
     * Get triggered rule.
     * Note: the rule result is a reference to rule map and SHOULD NOT be modified.
     *
     * @return triggered rule
     * @since 1.4.2
     */
    @Override
    public AuthorityRule getRule() {
        return rule.as(AuthorityRule.class);
    }
}
