package com.alibaba.csp.sentinel.slots.block;

/**
 * <AUTHOR>
 * @since 1.4.0
 */
public final class ClusterRuleConstant {

    public static final int FLOW_CLUSTER_STRATEGY_NORMAL = 0;
    public static final int FLOW_CLUSTER_STRATEGY_BORROW_REF = 1;

    public static final int FLOW_THRESHOLD_AVG_LOCAL = 0;
    public static final int FLOW_THRESHOLD_GLOBAL = 1;

    public static final int DEFAULT_CLUSTER_SAMPLE_COUNT = 10;

    private ClusterRuleConstant() {
    }
}
