package com.wosai.middleware.hera.toolkit.jsonrpc4j.match;

import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * An element matcher that matches a method's parameters.
 *
 * @param <T> The type of the matched entity.
 */
@EqualsAndHashCode(callSuper = false)
public class MethodParametersMatcher<T extends NamedElement.MethodElement> extends ElementMatcher.Junction.ForNonNullValues<T> {
    /**
     * The matcher to apply to the parameters.
     */
    private final ElementMatcher<? super List<? extends NamedElement.ParameterElement>> matcher;

    /**
     * Creates a new matcher for a method's parameters.
     *
     * @param matcher The matcher to apply to the parameters.
     */
    MethodParametersMatcher(ElementMatcher<? super List<? extends NamedElement.ParameterElement>> matcher) {
        this.matcher = matcher;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected boolean doMatch(T target) {
        return matcher.matches(target.getParameters());
    }

    @Override
    public String toString() {
        return "hasParameter(" + matcher + ")";
    }
}
