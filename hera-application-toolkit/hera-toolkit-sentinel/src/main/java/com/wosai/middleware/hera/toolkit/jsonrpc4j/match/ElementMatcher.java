package com.wosai.middleware.hera.toolkit.jsonrpc4j.match;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public interface ElementMatcher<T> {
    boolean matches(T target);

    /**
     * A junctions allows to chain different {@link ElementMatcher}s in a readable manner.
     *
     * @param <S> The type of the object that is being matched.
     */
    interface Junction<S> extends ElementMatcher<S> {
        /**
         * Creates a conjunction where this matcher and the {@code other} matcher must both be matched in order
         * to constitute a successful match. The other matcher is only invoked if this matcher constitutes a successful
         * match.
         *
         * @param other The second matcher to consult.
         * @param <U>   The type of the object that is being matched. Note that Java's type inference might not
         *              be able to infer the common subtype of this instance and the {@code other} matcher such that
         *              this type must need to be named explicitly.
         * @return A conjunction of this matcher and the other matcher.
         */
        <U extends S> Junction<U> and(ElementMatcher<? super U> other);

        /**
         * Creates a disjunction where either this matcher or the {@code other} matcher must be matched in order
         * to constitute a successful match. The other matcher is only invoked if this matcher constitutes an
         * unsuccessful match.
         *
         * @param other The second matcher to consult.
         * @param <U>   The type of the object that is being matched. Note that Java's type inference might not
         *              be able to infer the common subtype of this instance and the {@code other} matcher such that
         *              this type must need to be named explicitly.
         * @return A disjunction of this matcher and the other matcher.
         */
        <U extends S> Junction<U> or(ElementMatcher<? super U> other);

        /**
         * A base implementation of {@link Junction}.
         *
         * @param <V> The type of the object that is being matched.
         */
        abstract class AbstractBase<V> implements Junction<V> {
            /**
             * {@inheritDoc}
             */
            @Override
            @SuppressWarnings("unchecked") // In absence of @SafeVarargs
            public <U extends V> Junction<U> and(ElementMatcher<? super U> other) {
                return new Conjunction<U>(this, other);
            }

            /**
             * {@inheritDoc}
             */
            @Override
            @SuppressWarnings("unchecked") // In absence of @SafeVarargs
            public <U extends V> Junction<U> or(ElementMatcher<? super U> other) {
                return new Disjunction<U>(this, other);
            }
        }

        @EqualsAndHashCode(callSuper = false)
        class Conjunction<W> extends AbstractBase<W> {
            /**
             * The element matchers that constitute this conjunction.
             */
            @Getter
            private final List<ElementMatcher<? super W>> matchers;

            /**
             * Creates a new conjunction matcher.
             *
             * @param matcher The represented matchers in application order.
             */
            @SuppressWarnings("unchecked") // In absence of @SafeVarargs
            public Conjunction(ElementMatcher<? super W>... matcher) {
                this(Arrays.asList(matcher));
            }

            /**
             * Creates a new conjunction matcher.
             *
             * @param matchers The represented matchers in application order.
             */
            @SuppressWarnings("unchecked")
            public Conjunction(List<ElementMatcher<? super W>> matchers) {
                this.matchers = new ArrayList<>(matchers.size());
                for (ElementMatcher<? super W> matcher : matchers) {
                    if (matcher instanceof Conjunction<?>) {
                        this.matchers.addAll(((Conjunction<Object>) matcher).matchers);
                    } else {
                        this.matchers.add(matcher);
                    }
                }
            }

            /**
             * {@inheritDoc}
             */
            @Override
            public boolean matches(W target) {
                for (ElementMatcher<? super W> matcher : matchers) {
                    if (!matcher.matches(target)) {
                        return false;
                    }
                }
                return true;
            }

            @Override
            public String toString() {
                StringBuilder stringBuilder = new StringBuilder("(");
                boolean first = true;
                for (ElementMatcher<? super W> matcher : matchers) {
                    if (first) {
                        first = false;
                    } else {
                        stringBuilder.append(" and ");
                    }
                    stringBuilder.append(matcher);
                }
                return stringBuilder.append(")").toString();
            }
        }


        /**
         * A disjunction matcher which matches an element against matchers in order to constitute a successful match.
         *
         * @param <W> The type of the object that is being matched.
         */
        @EqualsAndHashCode(callSuper = false)
        class Disjunction<W> extends AbstractBase<W> implements ExactClassMatcher {
            /**
             * The element matchers that constitute this disjunction.
             */
            @Getter
            private final List<ElementMatcher<? super W>> matchers;

            /**
             * Creates a new disjunction matcher.
             *
             * @param matcher The represented matchers in application order.
             */
            @SuppressWarnings("unchecked") // In absence of @SafeVarargs
            public Disjunction(ElementMatcher<? super W>... matcher) {
                this(Arrays.asList(matcher));
            }

            /**
             * Creates a new disjunction matcher.
             *
             * @param matchers The represented matchers in application order.
             */
            @SuppressWarnings("unchecked")
            public Disjunction(List<ElementMatcher<? super W>> matchers) {
                this.matchers = new ArrayList<>(matchers.size());
                for (ElementMatcher<? super W> matcher : matchers) {
                    if (matcher instanceof Disjunction<?>) {
                        this.matchers.addAll(((Disjunction<Object>) matcher).matchers);
                    } else {
                        this.matchers.add(matcher);
                    }
                }
            }

            /**
             * {@inheritDoc}
             */
            @Override
            public boolean matches(W target) {
                for (ElementMatcher<? super W> matcher : matchers) {
                    if (matcher.matches(target)) {
                        return true;
                    }
                }
                return false;
            }

            @Override
            public String toString() {
                StringBuilder stringBuilder = new StringBuilder("(");
                boolean first = true;
                for (ElementMatcher<? super W> matcher : matchers) {
                    if (first) {
                        first = false;
                    } else {
                        stringBuilder.append(" or ");
                    }
                    stringBuilder.append(matcher);
                }
                return stringBuilder.append(")").toString();
            }

            @Override
            public Set<String> tryResolveClassName() {
                Set<String> names = new HashSet<>(this.matchers.size());
                for (final ElementMatcher<? super W> matcher : this.matchers) {
                    if (matcher instanceof ExactClassMatcher) {
                        names.addAll(((ExactClassMatcher) matcher).tryResolveClassName());
                    }
                }
                return names;
            }
        }

        /**
         * An abstract base implementation that rejects null values.
         *
         * @param <W> The type of the object that is being matched.
         */
        @EqualsAndHashCode(callSuper = false)
        abstract class ForNonNullValues<W> extends AbstractBase<W> {

            /**
             * {@inheritDoc}
             */
            @Override
            public boolean matches(W target) {
                return target != null && doMatch(target);
            }

            /**
             * Matches the supplied value if it was found not to be {@code null}.
             *
             * @param target The instance to be matched.
             * @return {@code true} if the given element is matched by this matcher or {@code false} otherwise.
             */
            protected abstract boolean doMatch(W target);
        }
    }
}
