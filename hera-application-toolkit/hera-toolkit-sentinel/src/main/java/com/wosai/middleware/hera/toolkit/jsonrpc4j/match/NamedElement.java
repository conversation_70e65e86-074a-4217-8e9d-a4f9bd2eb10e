package com.wosai.middleware.hera.toolkit.jsonrpc4j.match;

import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;

import java.lang.reflect.Executable;
import java.util.ArrayList;
import java.util.List;

public interface NamedElement {
    /**
     * Represents an element without a name in the source code.
     */
    String EMPTY_NAME = "";

    /**
     * Returns the display name of this element as it is found in the source code. If no such name exists,
     * an empty string is returned.
     *
     * @return The name of this element as given in a Java program's source code.
     */
    String getActualName();

    @RequiredArgsConstructor(staticName = "of")
    class MethodElement implements NamedElement {
        private final Executable executable;

        @Override
        public String getActualName() {
            return executable.getName();
        }

        /**
         * Returns a list of this method's parameters.
         *
         * @return A list of this method's parameters.
         */
        List<? extends ParameterElement> getParameters() {
            // TODO: fully lazy support with proxy
            List<ParameterElement> parameterElements = new ArrayList<>(executable.getParameterCount());
            for (int i = 0; i < executable.getParameterCount(); i++) {
                parameterElements.add(ParameterElement.of(executable, i));
            }
            return parameterElements;
        }
    }

    @RequiredArgsConstructor(staticName = "of")
    @EqualsAndHashCode
    class TypeElement implements NamedElement {
        private final Class<?> clazz;

        @Override
        public String getActualName() {
            return getName(this.clazz);
        }

        /**
         * Returns the type's actual name where it is taken into consideration that this type might be loaded anonymously.
         * In this case, the remainder of the types name is suffixed by {@code /<id>} which is removed when using this method
         * but is retained when calling {@link Class#getName()}.
         *
         * @param type The type for which to resolve its name.
         * @return The type's actual name.
         */
        public static String getName(Class<?> type) {
            String name = type.getName();
            int anonymousLoaderIndex = name.indexOf('/');
            return anonymousLoaderIndex == -1
                    ? name
                    : name.substring(0, anonymousLoaderIndex);
        }
    }

    @RequiredArgsConstructor(staticName = "of")
    @EqualsAndHashCode
    class ParameterElement implements NamedElement {
        private final Executable executable;
        private final int index;

        @Override
        public String getActualName() {
            return getName();
        }

        private String getName() {
            return executable.getParameters()[index].getName();
        }

        TypeElement asType() {
            return TypeElement.of(executable.getParameters()[index].getType());
        }
    }
}
