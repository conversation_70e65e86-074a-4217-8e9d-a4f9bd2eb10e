package com.wosai.middleware.hera.toolkit.jsonrpc4j.match;

import lombok.EqualsAndHashCode;

/**
 * An element matcher that returns a fixed result.
 *
 * @param <T> The actual matched type of this matcher.
 */
@EqualsAndHashCode(callSuper = false)
public class BooleanMatcher<T> extends ElementMatcher.Junction.AbstractBase<T> {

    /**
     * A matcher that always returns {@code true}.
     */
    private static final BooleanMatcher<?> TRUE = new BooleanMatcher<>(true);

    /**
     * A matcher that always returns {@code false}.
     */
    private static final BooleanMatcher<?> FALSE = new BooleanMatcher<>(false);

    /**
     * Returns an element matcher that returns the provided result.
     *
     * @param <T>     The type of the matched entity.
     * @param matches A matcher that always matches or never matches.
     * @return A matcher that returns the provided result for all inputs.
     */
    @SuppressWarnings("unchecked")
    public static <T> ElementMatcher.Junction<T> of(boolean matches) {
        return (ElementMatcher.Junction<T>) (matches ? TRUE : FALSE);
    }

    /**
     * The predefined result.
     */
    protected final boolean matches;

    /**
     * Creates a new boolean element matcher.
     *
     * @param matches The predefined result.
     */
    private BooleanMatcher(boolean matches) {
        this.matches = matches;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean matches(T target) {
        return matches;
    }

    @Override
    public String toString() {
        return Boolean.toString(matches);
    }

    public static boolean isAnyMatcher(ElementMatcher<?> elementMatcher) {
        return TRUE.equals(elementMatcher);
    }
}
