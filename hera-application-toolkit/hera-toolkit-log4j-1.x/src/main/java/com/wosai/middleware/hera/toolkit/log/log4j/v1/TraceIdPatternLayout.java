package com.wosai.middleware.hera.toolkit.log.log4j.v1;

import org.apache.log4j.PatternLayout;
import org.apache.log4j.helpers.PatternParser;

/**
 * The log4j extend pattern. By using this pattern, if hera agent is also active, {@link
 * PatternParser#finalizeConverter(char)} method will be override dynamic. <p>
 */
public class TraceIdPatternLayout extends PatternLayout {
    @Override
    protected PatternParser createPatternParser(String pattern) {
        return new TraceIdPatternParser(pattern);
    }
}
