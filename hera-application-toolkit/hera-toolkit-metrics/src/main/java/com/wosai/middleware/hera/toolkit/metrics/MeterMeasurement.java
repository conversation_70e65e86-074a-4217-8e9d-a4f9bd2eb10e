package com.wosai.middleware.hera.toolkit.metrics;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Map;

/**
 * MeterMeasurement contains necessary metadata and value for a metric
 */
@Getter
@RequiredArgsConstructor
public class MeterMeasurement {
    private final String name;
    private final Map<String, String> labels;
    private final String stat;
    private final double value;
}
