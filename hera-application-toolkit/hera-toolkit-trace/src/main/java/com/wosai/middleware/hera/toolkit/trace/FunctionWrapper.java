/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.wosai.middleware.hera.toolkit.trace;

import java.util.function.Function;

@TraceCrossThread
public class FunctionWrapper<T, R> implements Function<T, R> {
    final Function<T, R> function;

    public FunctionWrapper(Function<T, R> function) {
        this.function = function;
    }

    public static <T, R> FunctionWrapper<T, R> of(Function<T, R> function) {
        return new FunctionWrapper(function);
    }

    @Override
    public R apply(T t) {
        return this.function.apply(t);
    }

}
