package com.wosai.middleware.hera.toolkit.trace;

import java.util.function.Supplier;

@TraceCrossThread
public class SupplierWrapper<V> implements Supplier<V> {
    final Supplier<V> supplier;

    public static <V> SupplierWrapper<V> of(Supplier<V> r) {
        return new SupplierWrapper<V>(r);
    }

    public SupplierWrapper(Supplier<V> supplier) {
        this.supplier = supplier;
    }

    @Override
    public V get() {
        return supplier.get();
    }
}
