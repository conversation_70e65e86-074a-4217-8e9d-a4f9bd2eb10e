package com.wosai.middleware.hera.toolkit.trace;

import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Tag the current active span with key {@link #key()} and value {@link #value()}, if there is no active span, this
 * annotation takes no effect.
 *
 * @see Tags
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Repeatable(Tags.class)
public @interface Tag {
    /**
     * @return the key of the tag to be injected into the current active span
     */
    String key();

    /**
     * @return the value of the tag to be injected into the current active span, in the form of the customized
     * enhancement rules, for more information, refer to https://github.com/apache/skywalking/blob/master/docs/en/setup/service-agent/java-agent/Customize-enhance-trace.md#how-to-configure
     */
    String value();
}
