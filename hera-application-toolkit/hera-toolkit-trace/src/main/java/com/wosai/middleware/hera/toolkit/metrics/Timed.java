package com.wosai.middleware.hera.toolkit.metrics;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.time.Duration;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Timed {
    long DEFAULT_MINIMUM_EXPECTED_VALUE = 1;
    long DEFAULT_MAXIMUM_EXPECTED_VALUE = 30_000;

    /**
     * Name of the Timer metric.
     *
     * @return name of the Timer metric
     */
    String value() default "";

    /**
     * Deprecated: Please do not use this method since it is ignored from 1.1.1,
     * and will be removed in the next minor release
     */
    @Deprecated
    String[] extraTags() default {};

    /**
     * Flag of whether the Timer should be a {@link com.wosai.middleware.hera.agent.metrics.api.LongTaskTimer}.
     *
     * @return whether the timer is a LongTaskTimer
     */
    boolean longTask() default false;

    /**
     * List of percentiles to calculate client-side for the {@link io.micrometer.core.instrument.Timer}.
     * For example, the 95th percentile should be passed as {@code 0.95}.
     *
     * @return percentiles to calculate
     * @see io.micrometer.core.instrument.Timer.Builder#publishPercentiles(double...)
     */
    double[] percentiles() default {};

    /**
     * Whether to enable recording of a percentile histogram for the {@link io.micrometer.core.instrument.Timer Timer}.
     *
     * @return whether percentile histogram is enabled
     * @see io.micrometer.core.instrument.Timer.Builder#publishPercentileHistogram(Boolean)
     */
    boolean histogram() default false;

    /**
     * Description of the {@link io.micrometer.core.instrument.Timer}.
     *
     * @return meter description
     * @see io.micrometer.core.instrument.Timer.Builder#description(String)
     */
    String description() default "";

    /**
     * Publish at a minimum a histogram containing your defined service level objective
     * (SLO) boundaries. When used in conjunction with
     * {@link Timed#histogram()}, the boundaries defined
     * here are included alongside other buckets used to generate aggregable percentile
     * approximations.
     * @return Publish SLO boundaries in the set of histogram buckets shipped to the
     * monitoring system.In milliseconds.
     * @see io.micrometer.core.instrument.Timer.Builder#serviceLevelObjectives(Duration...) 
     */
    long[] serviceLevelObjectives() default {};


    /**
     * Sets the minimum value that this timer is expected to observe. Sets a lower bound
     * on histogram buckets that are shipped to monitoring systems that support aggregable
     * percentile approximations.
     * @return The minimum value that this timer is expected to observe.In milliseconds.
     * @see io.micrometer.core.instrument.Timer.Builder#minimumExpectedValue(Duration)
     */
    long minimumExpectedValue() default DEFAULT_MINIMUM_EXPECTED_VALUE;

    /**
     * Sets the maximum value that this timer is expected to observe. Sets an upper bound
     * on histogram buckets that are shipped to monitoring systems that support aggregable
     * percentile approximations.
     * @return The maximum value that this timer is expected to observe.In milliseconds.
     * @see io.micrometer.core.instrument.Timer.Builder#maximumExpectedValue(Duration) 
     */
    long maximumExpectedValue() default DEFAULT_MAXIMUM_EXPECTED_VALUE;
}

