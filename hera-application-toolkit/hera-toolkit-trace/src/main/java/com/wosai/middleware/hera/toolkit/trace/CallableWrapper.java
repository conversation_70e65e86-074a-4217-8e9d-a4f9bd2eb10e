package com.wosai.middleware.hera.toolkit.trace;

import java.util.concurrent.Callable;

@TraceCrossThread
public class CallableWrapper<V> implements Callable<V> {
    final Callable<V> callable;

    public static <V> CallableWrapper<V> of(Callable<V> r) {
        return new CallableWrapper<V>(r);
    }

    public CallableWrapper(Callable<V> callable) {
        this.callable = callable;
    }

    @Override
    public V call() throws Exception {
        return callable.call();
    }
}
