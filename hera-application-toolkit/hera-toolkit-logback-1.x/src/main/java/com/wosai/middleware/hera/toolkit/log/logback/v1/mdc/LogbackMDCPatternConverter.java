package com.wosai.middleware.hera.toolkit.log.logback.v1.mdc;

import ch.qos.logback.classic.pattern.MDCConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.util.OptionHelper;

public class LogbackMDCPatternConverter extends MD<PERSON>onverter {
    private static final String CONVERT_KEY = "tid";

    private boolean convert4TID = false;

    @Override
    public void start() {
        super.start();
        String[] key = OptionHelper.extractDefaultReplacement(getFirstOption());
        if (null != key && key.length > 0 && CONVERT_KEY.equals(key[0])) {
            convert4TID = true;
        }
    }

    @Override
    public String convert(ILoggingEvent iLoggingEvent) {
        return convert4TID ? convertTID(iLoggingEvent) : super.convert(iLoggingEvent);
    }

    public String convertTID(ILoggingEvent iLoggingEvent) {
        return "TID: N/A";
    }
}

