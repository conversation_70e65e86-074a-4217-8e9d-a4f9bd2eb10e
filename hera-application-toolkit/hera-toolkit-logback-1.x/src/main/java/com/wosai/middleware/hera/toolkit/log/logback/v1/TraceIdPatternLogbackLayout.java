package com.wosai.middleware.hera.toolkit.log.logback.v1;

import ch.qos.logback.classic.PatternLayout;

/**
 * Based on the logback-compoenent convert register mechanism, register {@link LogbackPatternConverter} as a new
 * convert, match to "tid". You can use "%tid" in logback config file, "Pattern" section.
 * <p>
 */
public class TraceIdPatternLogbackLayout extends PatternLayout {
    static {
        defaultConverterMap.put("tid", LogbackPatternConverter.class.getName());
    }
}
