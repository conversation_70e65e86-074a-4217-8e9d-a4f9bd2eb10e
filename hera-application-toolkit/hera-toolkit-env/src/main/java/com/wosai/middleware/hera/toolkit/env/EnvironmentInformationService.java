package com.wosai.middleware.hera.toolkit.env;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.file.FileSystem;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Properties;

public class EnvironmentInformationService {

    private static final String DOWNWARD_API_LABELS_PATH = "/etc/podinfo/labels;/opt/podinfo/labels";
    private static final FileSystem FILE_SYSTEM = FileSystems.getDefault();
    private static String ENV_FLAG;

    static {
        Properties labels = readDownwardAPI(DOWNWARD_API_LABELS_PATH);
        if (labels.containsKey("version")) {
            ENV_FLAG = labels.getProperty("version");
        }
    }

    // parse labels from Downward API
    private static Properties readDownwardAPI(String downwardAPIPath) {
        Properties podProperties = new Properties();
        for (final String filePath : downwardAPIPath.split(";")) {
            final Path path = FILE_SYSTEM.getPath(filePath);
            if (Files.isReadable(path)) {
                try (BufferedReader bufferedReader = Files.newBufferedReader(path)) {
                    podProperties.load(bufferedReader);
                    for (String key : podProperties.stringPropertyNames()) {
                        if (podProperties.getProperty(key) == null || podProperties.getProperty(key).isEmpty()) {
                            continue;
                        }
                        // strip whitespace
                        // as define here https://util.unicode.org/UnicodeJsps/list-unicodeset.jsp?a=%5Cp%7Bwhitespace%7D
                        // CharMatcher.whitespace() includes both \t (ASCII 9) and whitespace (ASCII 20)
                        String value = podProperties.getProperty(key).trim();
                        // remove double quotes
                        value = trimCharFromEnds(value, '\"');
                        podProperties.put(key, value);
                    }
                    // only read the first available file
                    break;
                } catch (IOException ignored) {
                }
            }
        }
        return podProperties;
    }

    private static String trimCharFromEnds(String str, char removeChr) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        int start = 0;
        int end = str.length();

        while (start < end && str.charAt(start) == removeChr) {
            start++;
        }

        while (start < end && str.charAt(end - 1) == removeChr) {
            end--;
        }

        String result = str;

        if (start > 0 || end < str.length()) {
            result = str.substring(start, end);
        }
        return result;
    }

    public static String getEnvInformation() {
        return ENV_FLAG;
    }
}