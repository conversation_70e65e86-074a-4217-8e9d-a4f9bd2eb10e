<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.shouqianba.middleware</groupId>
		<artifactId>dubbo-jsonrpc-demo</artifactId>
		<version>1.1.4-SNAPSHOT</version>
	</parent>
	<groupId>com.example</groupId>
	<artifactId>spiffe-provider</artifactId>
	<version>1.1.4-SNAPSHOT</version>
	<name>provider</name>
	<description>Demo project for Spring Boot</description>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>io.spiffe</groupId>
			<artifactId>java-spiffe-core</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.persistence</groupId>
			<artifactId>jakarta.persistence-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>io.spiffe</groupId>-->
<!--			<artifactId>grpc-netty-macos-aarch64</artifactId>-->
<!--			<version>0.8.4</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>io.spiffe</groupId>
			<artifactId>grpc-netty-linux</artifactId>
			<version>0.8.4</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>io.netty</groupId>-->
<!--			<artifactId>netty-transport-native-kqueue</artifactId>-->
<!--			<classifier>osx-aarch_64</classifier>-->
<!--			<version>4.1.87.Final</version>-->
<!--		</dependency>-->

		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<version>1.4.199</version>
		</dependency>
	</dependencies>

	<build>
		<finalName>spiffe-provider</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
							<goal>build-info</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
