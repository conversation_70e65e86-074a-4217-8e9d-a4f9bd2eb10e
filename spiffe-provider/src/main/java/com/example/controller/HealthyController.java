package com.example.controller;

import io.spiffe.bundle.jwtbundle.JwtBundle;
import io.spiffe.exception.AuthorityNotFoundException;
import io.spiffe.exception.BundleNotFoundException;
import io.spiffe.exception.JwtSvidException;
import io.spiffe.spiffeid.SpiffeId;
import io.spiffe.spiffeid.TrustDomain;
import io.spiffe.svid.jwtsvid.JwtSvid;
import io.spiffe.workloadapi.JwtSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Set;

/**
 * Created by <PERSON><PERSON>dongquan on 2018/11/22.
 * <p>
 * 注意: 不要、不要、千万不要删除，这个是作为接入bingo的必要条件
 */
@RestController
@RequestMapping("")
@Slf4j
public class HealthyController {

    @Autowired
    private JwtSource jwtSource;

    /**
     * 健康检查接口
     */
    @RequestMapping(value = "/check", method = RequestMethod.GET)
    public
    @ResponseBody
    String healthy() {
        return "group: success"; //可以自定义逻辑来验证系统已经启动成功
    }

    @RequestMapping(value = "/validToken", method = RequestMethod.GET)
    public String validToken(@RequestParam(value = "token") String token, @RequestParam(value = "aud") String audience) {
        try {
            JwtSvid.parseAndValidate(token, jwtSource, Collections.singleton(audience));
        } catch (JwtSvidException | BundleNotFoundException | AuthorityNotFoundException e) {
            throw new RuntimeException(e);
        }
        return "success"; //可以自定义逻辑来验证系统已经启动成功
    }

    @RequestMapping(value = "/fetchJwt", method = RequestMethod.GET)
    public JwtSvid fetchJwt(@RequestParam(value = "aud") String audience) {
        JwtSvid jwtSvid = null;
        try {
            jwtSvid = jwtSource.fetchJwtSvid(SpiffeId.parse("spiffe://shouqianba.com/sqb/spiffe-provider"), audience);
        } catch (JwtSvidException e) {
            throw new RuntimeException(e);
        }
        return jwtSvid;
    }

    @RequestMapping(value = "/getKeys", method = RequestMethod.GET)
    public Set<String> getKeys() {
        Set<String> keys  = null;
        try {
            JwtBundle jwtBundle = jwtSource.getBundleForTrustDomain(TrustDomain.parse("shouqianba.com"));
            keys = jwtBundle.getJwtAuthorities().keySet();
        } catch (BundleNotFoundException e) {
            throw new RuntimeException(e);
        }
        return keys;
    }
}