package com.example;

import io.spiffe.svid.jwtsvid.JwtSvid;
import io.spiffe.workloadapi.JwtSource;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;

@Component
public class JwtRequestFilter extends OncePerRequestFilter {

    @Autowired
    private JwtSource jwtSource;

    @SneakyThrows
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                    Filter<PERSON>hai<PERSON> filterChain) throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        if (!requestURI.startsWith("/tasks")) {
            filterChain.doFilter(request, response);
            return;
        }

        String token = request.getHeader("x-hera-spiffe");
        if (StringUtils.isBlank(token)) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST);
            filterChain.doFilter(request, response);
            return;
        }

        try {
            // 可以拿到调用者的SpiffeId，并通过自己的服务名验证token是否正确。
            JwtSvid jwtSvid = JwtSvid.parseAndValidate(token, jwtSource, Collections.singleton("spiffe-provider"));
            System.out.println(jwtSvid.getSpiffeId());
            System.out.println(jwtSvid.getIssuedAt());
            System.out.println(jwtSvid.getExpiry());
            System.out.println(token);
        } catch (Exception e) {
            // 如果token验证有问题返回401
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED);
        }

        filterChain.doFilter(request, response);
    }
}
