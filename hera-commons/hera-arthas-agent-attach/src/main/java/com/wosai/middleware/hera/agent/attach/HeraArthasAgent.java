package com.wosai.middleware.hera.agent.attach;

import java.arthas.SpyAPI;
import java.io.File;
import java.lang.instrument.Instrumentation;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> 2020-06-22
 */
public class HeraArthasAgent {
    private static final String HERA_ARTHAS_CORE_JAR = "hera-arthas-core.jar";
    private static final String HERA_ARTHAS_SPY_JAR = "hera-arthas-spy.jar";
    private static final String ARTHAS_BOOTSTRAP = "com.taobao.arthas.core.server.ArthasBootstrap";
    private static final String GET_INSTANCE = "getInstance";
    private static final String IS_BIND = "isBind";

    private String errorMessage;

    private Map<String, String> configMap = new HashMap<>();
    private String arthasHome;
    private boolean silentInit;
    private Instrumentation instrumentation;

    public HeraArthasAgent(Map<String, String> configMap, String arthasHome, boolean silentInit, Instrumentation instrumentation) {
        if (configMap != null) {
            this.configMap = configMap;
        }

        this.arthasHome = arthasHome;
        this.silentInit = silentInit;
        this.instrumentation = instrumentation;
    }

    /**
     * use the specified arthas
     *
     * @param arthasHome      arthas directory
     * @param instrumentation Instrumentation
     */
    public static void attach(String arthasHome, Instrumentation instrumentation) {
        new HeraArthasAgent(null, arthasHome, false, instrumentation).init();
    }

    public void init() throws IllegalStateException {
        try {
            Class.forName("java.arthas.SpyAPI");
            if (SpyAPI.isInited()) {
                return;
            }
        } catch (Throwable e) {
            // ignore
        }

        try {
            // find arthas-core.jar
            File arthasCoreJarFile = new File(arthasHome, HERA_ARTHAS_CORE_JAR);
            if (!arthasCoreJarFile.exists()) {
                throw new IllegalStateException("can not find hera-arthas-core.jar under arthasHome: " + arthasHome);
            }
            AttachArthasClassloader arthasClassLoader = new AttachArthasClassloader(
                    new URL[]{
                            arthasCoreJarFile.toURI().toURL()
                    });

            /**
             * <pre>
             * ArthasBootstrap bootstrap = ArthasBootstrap.getInstance(inst);
             * </pre>
             */
            Class<?> bootstrapClass = arthasClassLoader.loadClass(ARTHAS_BOOTSTRAP);
            Object bootstrap = bootstrapClass.getMethod(GET_INSTANCE, Instrumentation.class, Map.class).invoke(null, instrumentation, configMap);
            boolean isBind = (Boolean) bootstrapClass.getMethod(IS_BIND).invoke(bootstrap);
            if (!isBind) {
                String errorMsg = "Arthas server port binding failed! Please check $HOME/logs/arthas/arthas.log for more details.";
                throw new RuntimeException(errorMsg);
            }
        } catch (Throwable e) {
            errorMessage = e.getMessage();
            if (!silentInit) {
                throw new IllegalStateException(e);
            }
        }
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
