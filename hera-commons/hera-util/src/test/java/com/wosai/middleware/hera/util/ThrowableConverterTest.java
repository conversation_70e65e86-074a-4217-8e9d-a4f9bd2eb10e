package com.wosai.middleware.hera.util;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.IThrowableProxy;
import ch.qos.logback.classic.spi.LoggerContextVO;
import ch.qos.logback.classic.spi.ThrowableProxy;
import com.google.common.collect.ImmutableList;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import net.logstash.logback.stacktrace.ShortenedThrowableConverter;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Marker;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static com.wosai.middleware.hera.util.ExceptionHelper.entryMethod;
import static org.assertj.core.api.Assertions.assertThat;

public class ThrowableConverterTest {
    private ThrowableConverter converter;
    private ThrowableConverter rootCauseLastConverter;
    private ShortenedThrowableConverter logstashConverter;
    private ShortenedThrowableConverter logstashCauseLastConverter;
    private Throwable t;

    @Before
    public void setup() {
        converter = ThrowableConverter.builder().build();
        rootCauseLastConverter = ThrowableConverter.builder().rootCauseFirst(false).build();
        t = entryMethod();
        logstashConverter = createConverter(true, null);
        logstashCauseLastConverter = createConverter(false, null);
    }

    private ShortenedThrowableConverter createConverter(boolean rootCauseFirst, String patterns) {
        ShortenedThrowableConverter logstashConverter = new ShortenedThrowableConverter();
        logstashConverter.setInlineHash(true);
        logstashConverter.setRootCauseFirst(rootCauseFirst);
        if (StringUtils.isNotBlank(patterns)) {
            logstashConverter.setExcludes(Arrays.asList(patterns.split(",")));
        }
        logstashConverter.start();
        return logstashConverter;
    }

    @Test
    public void filterWholeStackTrace() {
        String filteredStackTrace = converter.convert(t);
        List<String> lines = Arrays.asList(filteredStackTrace.split(System.getProperty("line.separator")));

        String example = logstashConverter.convert(new LoggingEvent(t));
        List<String> exampleLines = Arrays.asList(example.split(System.getProperty("line.separator")));

        assertThat(lines).contains("\tSuppressed: java.lang.RuntimeException: suppressed");
        assertThat(lines).contains("\t\tWrapped by: java.lang.IllegalArgumentException: java.lang.NumberFormatException: For input string: \"text\"");
        assertThat(lines).containsExactlyElementsOf(exampleLines);
    }

    @Test
    public void stateShouldBeCleared_whenOnlyOneLineIsExcluded() {
        List<Pattern> patterns = ImmutableList.of(Pattern.compile("^org\\.junit\\.internal"), Pattern.compile("^java\\.lang\\.reflect\\.Method"));
        StackElementFilter filter = StackElementFilter.byPattern(patterns);
        ThrowableConverter filteredConverter = ThrowableConverter.builder().stackHasher(new StackHasher(filter)).stackElementFilter(filter).build();

        String filteredStackTrace = filteredConverter.convert(entryMethod());
        List<String> lines = Arrays.asList(filteredStackTrace.split(System.getProperty("line.separator")));

        assertThat(lines).doesNotContain("\t... 2 frames excluded by [^java\\.lang\\.reflect\\.Method, ^org\\.junit\\.internal]");
        assertThat(lines).contains("\t... 2 frames excluded by [^org\\.junit\\.internal]");
    }

    @Test
    public void filterOrgDotJunit() {
        List<Pattern> patterns = ImmutableList.of(Pattern.compile("^org\\.junit"));
        StackElementFilter filter = StackElementFilter.byPattern(patterns);
        ThrowableConverter filteredConverter = ThrowableConverter.builder().stackHasher(new StackHasher(filter)).stackElementFilter(filter).build();

        String filteredStackTrace = filteredConverter.convert(t);
        List<String> lines = Arrays.asList(filteredStackTrace.split(System.getProperty("line.separator")));

        assertThat(lines).anyMatch(line -> line.matches("^\\t\\.\\.\\. \\d{2} frames excluded by \\[\\^org\\\\\\.junit]$"));
    }

    @Test
    public void filterConsecutiveFilters() {
        List<Pattern> patterns = ImmutableList.of(Pattern.compile("^org\\.junit"), Pattern.compile("^java\\.lang\\.reflect\\.Method"));
        StackElementFilter filter = StackElementFilter.byPattern(patterns);
        ThrowableConverter filteredConverter = ThrowableConverter.builder().stackHasher(new StackHasher(filter)).stackElementFilter(filter).build();

        String filteredStackTrace = filteredConverter.convert(entryMethod());
        List<String> lines = Arrays.asList(filteredStackTrace.split(System.getProperty("line.separator")));

        assertThat(lines).anyMatch(line -> line.matches("\\t\\.\\.\\. \\d{2} frames excluded by \\[\\^java\\\\\\.lang\\\\\\.reflect\\\\\\.Method, \\^org\\\\\\.junit]"));
    }

    @Test
    public void testCauseLast() {
        String filteredStackTrace = rootCauseLastConverter.convert(t);
        List<String> lines = Arrays.asList(filteredStackTrace.split(System.getProperty("line.separator")));

        String example = logstashCauseLastConverter.convert(new LoggingEvent(t));
        List<String> exampleLines = Arrays.asList(example.split(System.getProperty("line.separator")));

        assertThat(lines).contains("\tSuppressed: java.lang.RuntimeException: suppressed");
        assertThat(lines).contains("\t\tCaused by: java.lang.NumberFormatException: For input string: \"text\"");
        assertThat(lines).containsExactlyElementsOf(exampleLines);
    }

    public static class LoggingEvent implements ILoggingEvent {
        private final IThrowableProxy proxy;

        public LoggingEvent(Throwable proxy) {
            this.proxy = new ThrowableProxy(proxy);
        }

        @Override
        public String getThreadName() {
            return null;
        }

        @Override
        public Level getLevel() {
            return null;
        }

        @Override
        public String getMessage() {
            return null;
        }

        @Override
        public Object[] getArgumentArray() {
            return new Object[0];
        }

        @Override
        public String getFormattedMessage() {
            return null;
        }

        @Override
        public String getLoggerName() {
            return null;
        }

        @Override
        public LoggerContextVO getLoggerContextVO() {
            return null;
        }

        @Override
        public IThrowableProxy getThrowableProxy() {
            return this.proxy;
        }

        @Override
        public StackTraceElement[] getCallerData() {
            return new StackTraceElement[0];
        }

        @Override
        public boolean hasCallerData() {
            return false;
        }

        @Override
        public Marker getMarker() {
            return null;
        }

        @Override
        public Map<String, String> getMDCPropertyMap() {
            return null;
        }

        @Override
        public Map<String, String> getMdc() {
            return null;
        }

        @Override
        public long getTimeStamp() {
            return 0;
        }

        @Override
        public void prepareForDeferredProcessing() {

        }
    }
}
