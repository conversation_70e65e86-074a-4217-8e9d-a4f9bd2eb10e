package com.wosai.middleware.hera.util;

import com.google.common.collect.ImmutableList;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class KafkaClusterHasherTest {
    @Test
    public void testHash() {
        // the hash comes from ControlPlane project
        assertThat(KafkaClusterHasher.hash(ImmutableList.of("192.168.101.89:9092", "192.168.100.52:9092", "192.168.101.90:9092")))
                .isEqualTo("52af15fc739659c5");
    }
}
