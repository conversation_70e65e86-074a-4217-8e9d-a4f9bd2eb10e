package com.wosai.middleware.hera.util;

import com.google.common.io.CharStreams;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

@Slf4j
public class HttpUtil {

    public static String doGet(String url, int connectionTimeout, int readTimeout, int retry) throws Exception {
        InputStreamReader isr = null;
        InputStreamReader esr = null;
        try {
            HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(connectionTimeout);
            conn.setReadTimeout(readTimeout);
            conn.connect();
            String response;

            try {
                isr = new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8);
                response = CharStreams.toString(isr);
                return response;
            } catch (IOException ex) {
                /**
                 * according to https://docs.oracle.com/javase/7/docs/technotes/guides/net/http-keepalive.html,
                 * we should clean up the connection by reading the response body so that the connection
                 * could be reused.
                 */
                InputStream errorStream = conn.getErrorStream();

                if (errorStream != null) {
                    esr = new InputStreamReader(errorStream, StandardCharsets.UTF_8);
                    String errorMsg = null;
                    try {
                        errorMsg = CharStreams.toString(esr);
                    } catch (IOException ioe) {
                        //ignore
                    }
                    throw new Exception(errorMsg, ex);
                }
                throw ex;
            }
        } catch (Exception e) {
            if (retry > 0) {
                retry--;
                log.error("Get config from apollo error url: " + url + ", now begin retry, remaining times " + retry, e);
                Thread.sleep(5000);
                doGet(url, connectionTimeout, readTimeout, retry);
            }
            throw e;
        } finally {
            if (isr != null) {
                try {
                    isr.close();
                } catch (IOException ex) {
                    // ignore
                }
            }

            if (esr != null) {
                try {
                    esr.close();
                } catch (IOException ex) {
                    // ignore
                }
            }
        }
    }
}
