package com.wosai.middleware.hera.util;

import java.util.HashSet;
import java.util.Set;

public class TagsUtil {
    private static Set<String> TAG_KEYWORD_SET = new HashSet<>();

    public static boolean isOverWritable(String tag) {
        return TAG_KEYWORD_SET.contains(tag);
        // default false
    }

    public static void appendKeyword(String key) {
        if (key == null || "".equals(key.trim())) return;
        TAG_KEYWORD_SET.add(key);
    }
}
