package com.wosai.middleware.hera.util;

public class ExtraTags {
    // will be included by OpenTracing-Java in the next release
    @Deprecated
    public static final String PEER_ADDRESS = "peer.address";

    public static final String HTTP_PARAMS = "http.params";

    public static final String HTTP_BODY = "http.body";

    public static final String HTTP_HEADER = "http.header";

    public static final String THREAD_NAME = "thread.name";

    public static final String MQ_BROKER = "mq.broker";

    /**
     * DB_BIND_VARIABLES records the parameters in MongoDB access
     */
    public static final String DB_BIND_VARIABLES = "db.bind.variables";

    /**
     * Simple name of the error class
     */
    public static final String ERROR_CLASS = "error.class";

    /**
     * Raw statement being executed when using apache/ShardingSphere,
     * usually the table and/or database have number suffix which means various shards
     */
    public static final String DB_STATEMENT_RAW = "db.statement.raw";
}
