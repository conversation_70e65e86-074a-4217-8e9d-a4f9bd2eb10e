package com.wosai.middleware.hera.util;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class KafkaClusterHasher {
    private KafkaClusterHasher() {
    }

    public static String hash(List<String> bootstrapServers) {
        return Long.toHexString(XxHash64.INSTANCE.hashAsciiString(Joiner.on(",").join(
                bootstrapServers.stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList())
        )));
    }

    public static String hash(String bootstrapServers) {
        return Long.toHexString(XxHash64.INSTANCE.hashAsciiString(Joiner.on(",").join(
                normalizeBootstrapServers(bootstrapServers)
        )));
    }

    static List<String> normalizeBootstrapServers(String bootstrapServers) {
        return Splitter.on(",").splitToStream(bootstrapServers)
                .sorted(Comparator.naturalOrder())
                .collect(Collectors.toList());
    }
}

