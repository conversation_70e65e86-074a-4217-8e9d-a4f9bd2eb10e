package com.wosai.middleware.hera.util;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
public class ThreadPoolUtil {
    private ThreadPoolUtil() {
    }

    public static void gracefulShutdown(@Nullable ExecutorService scheduledExecutorService, int nSecs, boolean ignoreInterrupted) {
        if (scheduledExecutorService != null) {
            scheduledExecutorService.shutdown();
            try {
                if (scheduledExecutorService.awaitTermination(nSecs, TimeUnit.SECONDS)) {
                    scheduledExecutorService.shutdownNow();
                }
            } catch (InterruptedException ex) {
                log.warn("fail to stop execution service", ex);
                scheduledExecutorService.shutdownNow();
                if (!ignoreInterrupted) Thread.currentThread().interrupt();
            }
        }
    }

    public static void gracefulShutdown(@Nullable ExecutorService scheduledExecutorService, int nSecs) {
        gracefulShutdown(scheduledExecutorService, nSecs, false);
    }
}
