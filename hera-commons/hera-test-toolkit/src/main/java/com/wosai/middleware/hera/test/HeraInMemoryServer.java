package com.wosai.middleware.hera.test;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.HeraTracer;
import com.wosai.middleware.hera.tracing.network.InMemoryReporter;
import com.wosai.middleware.hera.tracing.network.NetworkSpan;
import com.wosai.middleware.hera.tracing.propagation.B3TextMapCodec;
import com.wosai.middleware.hera.tracing.propagation.Format;
import com.wosai.middleware.hera.tracing.spi.Codec;
import org.apache.skywalking.apm.agent.core.conf.Config;

import java.util.List;

public class HeraInMemoryServer extends HeraInMemoryMetrics {
    private final String serviceName;
    private HeraTracer tracer;
    private InMemoryReporter reporter;

    public HeraInMemoryServer(String serviceName) {
        Config.Agent.SERVICE_NAME = serviceName;
        this.serviceName = serviceName;
    }

    @Override
    protected void before() throws Throwable {
        super.before();

        this.reporter = new InMemoryReporter();
        this.tracer = new HeraTracer.Builder(this.serviceName)
                .registerCodec(Format.Builtin.HTTP_HEADERS, createCodec())
                .registerCodec(Format.Builtin.TEXT_MAP, createCodec())
                .withConstSampler(true)
                .withReporter(reporter)
                .build();

        ContextManager.installTracer(tracer);
        Config.Agent.SERVICE_NAME = "";
    }

    protected <T> Codec<T> createCodec() {
        return (Codec<T>) new B3TextMapCodec.Builder().build();
    }

    @Override
    protected void after() {
        super.after();

        this.tracer.close();
        this.reporter.close();
    }

    public List<NetworkSpan> getSpans() {
        if (this.reporter == null) {
            throw new IllegalStateException("the reporter is not initialized");
        }
        return this.reporter.getSpans();
    }

    public HeraTracer tracer() {
        return this.tracer;
    }
}
