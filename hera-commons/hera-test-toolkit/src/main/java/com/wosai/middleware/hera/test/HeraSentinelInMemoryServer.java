package com.wosai.middleware.hera.test;

import com.wosai.middleware.hera.agent.conf.HeraConfig;
import com.wosai.middleware.hera.agent.sentinel.FlowRule;
import com.wosai.middleware.hera.agent.services.SentinelContextManager;
import com.wosai.middleware.hera.tracing.baggage.BaggageField;
import com.wosai.middleware.hera.tracing.baggage.BaggagePropagation;
import com.wosai.middleware.hera.tracing.baggage.BaggagePropagationConfig;
import com.wosai.middleware.hera.tracing.spi.Codec;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class HeraSentinelInMemoryServer extends HeraInMemoryServer {
    private final List<BaggageField> baggageFields;

    public HeraSentinelInMemoryServer(String serviceName, BaggageField... baggageFields) {
        super(serviceName);
        this.baggageFields = baggageFields != null ? Arrays.asList(baggageFields) : Collections.emptyList();
    }

    @Override
    protected void before() throws Throwable {
        super.before();
        HeraConfig.Sentinel.SWITCH_FLAG = true;
    }

    @Override
    protected <T> Codec<T> createCodec() {
        if (baggageFields.isEmpty()) {
            return super.createCodec();
        }
        BaggagePropagation.Builder bld = BaggagePropagation.newBuilder(super.createCodec());
        for (final BaggageField field : baggageFields) {
            bld.add(BaggagePropagationConfig.SingleBaggageField.remote(field));
        }
        return (Codec<T>) bld.build();
    }

    public void loadRules(FlowRule... flowRules) {
        SentinelContextManager.loadRules(Arrays.asList(flowRules));
    }

    @Override
    protected void after() {
        super.after();
        HeraConfig.Sentinel.SWITCH_FLAG = false;
        SentinelContextManager.resetRules();
    }
}
