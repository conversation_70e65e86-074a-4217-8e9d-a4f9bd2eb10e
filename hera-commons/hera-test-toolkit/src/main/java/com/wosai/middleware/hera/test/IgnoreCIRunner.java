package com.wosai.middleware.hera.test;

import org.junit.runners.BlockJUnit4ClassRunner;
import org.junit.runners.model.FrameworkMethod;
import org.junit.runners.model.InitializationError;

public class IgnoreCIRunner extends BlockJUnit4ClassRunner {
    public IgnoreCIRunner(Class<?> testClass) throws InitializationError {
        super(testClass);
    }

    @Override
    protected boolean isIgnored(FrameworkMethod child) {
        if (shouldIgnore()) {
            return true;
        }
        return super.isIgnored(child);
    }

    protected boolean shouldIgnore() {
        return "true".equals(System.getenv("CI"));
    }
}
