package com.taobao.arthas.core.command.model;

import com.taobao.arthas.core.shell.system.ExecStatus;
import com.taobao.arthas.core.shell.system.Job;
import lombok.Getter;

import java.util.Date;

@Getter
public class JobStatusModel extends ResultModel {
    private int executionCount;

    private Date startTime;

    private String command;

    private String cacheLocation;

    private Date timeoutDate;

    private String sessionId;

    private ExecStatus status;

    @Override
    public String getType() {
        return "job_status";
    }

    public static JobStatusModel from(Job job) {
        return from(job, job.status());
    }

    public static JobStatusModel from(Job job, ExecStatus execStatus) {
        JobStatusModel model = new JobStatusModel();
        model.setJobId(job.id());
        model.executionCount = job.process().times();
        model.startTime = job.process().startTime();
        model.command = job.line();
        model.cacheLocation = job.process().cacheLocation();
        model.timeoutDate = job.timeoutDate();
        model.sessionId = job.getSession().getSessionId();
        model.status = execStatus;
        return model;
    }
}
