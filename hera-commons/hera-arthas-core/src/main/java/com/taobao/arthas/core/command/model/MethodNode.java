package com.taobao.arthas.core.command.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

/**
 * Method call node of TraceCommand
 *
 * <AUTHOR> 2020/4/29
 * <AUTHOR> Lu 2024/4/29: in order to serialize beginTs and endTs
 */
@Setter
@Getter
public class MethodNode extends TraceNode {

    private String className;
    private String methodName;
    private int lineNumber;
    // @Getter generates getIsThrow()
    // https://stackoverflow.com/questions/42619986/lombok-annotation-getter-for-boolean-field
    private Boolean isThrow;
    private String throwExp;

    /**
     * 是否为invoke方法，true为beforeInvoke，false为方法体入口的onBefore
     */
    private boolean isInvoking;

    /**
     * 开始时间戳
     */
    private long beginTimestamp;

    /**
     * 结束时间戳
     */
    private long endTimestamp;

    /**
     * 合并统计相同调用,并计算最小\最大\总耗时
     */
    private long minCost = Long.MAX_VALUE;
    private long maxCost = Long.MIN_VALUE;
    private long totalCost = 0;
    private long times = 0;

    public MethodNode(String className, String methodName, int lineNumber, boolean isInvoking) {
        super("method");
        this.className = className;
        this.methodName = methodName;
        this.lineNumber = lineNumber;
        this.isInvoking = isInvoking;
    }

    public void begin() {
        beginTimestamp = System.nanoTime();
    }

    public void end() {
        endTimestamp = System.nanoTime();

        long cost = getCost();
        if (cost < minCost) {
            minCost = cost;
        }
        if (cost > maxCost) {
            maxCost = cost;
        }
        times++;
        totalCost += cost;
    }

    public void setThrow(Boolean aThrow) {
        isThrow = aThrow;
    }

    @JSONField(serialize = false)
    public long getCost() {
        return endTimestamp - beginTimestamp;
    }
}
