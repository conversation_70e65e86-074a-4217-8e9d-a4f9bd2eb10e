<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hera</artifactId>
        <groupId>com.wosai.middleware</groupId>
        <version>1.14.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hera-commons</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>hera-util</module>
        <module>hera-test-toolkit</module>
        <module>hera-xds</module>
        <module>hera-arthas-spy</module>
        <module>hera-arthas-core</module>
        <module>hera-arthas-agent-attach</module>
        <module>hera-arthas-api</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <agent.package.dest.dir>${project.build.directory}/../../../target/hera-agent
        </agent.package.dest.dir>
        <arthas.dest.dir>${agent.package.dest.dir}/arthas</arthas.dest.dir>
    </properties>

</project>