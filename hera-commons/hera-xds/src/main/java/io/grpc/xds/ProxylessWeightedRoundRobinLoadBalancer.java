package io.grpc.xds;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import com.google.common.base.Preconditions;
import com.wosai.middleware.hera.rpc.ForwardingServer;
import com.wosai.middleware.hera.rpc.util.ForwardingLoadBalancerHelper;
import com.wosai.middleware.hera.rpc.util.RoundRobinLoadBalancer;
import io.grpc.Deadline;
import io.grpc.LoadBalancer;
import io.grpc.Status;
import io.grpc.SynchronizationContext;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.google.common.base.Preconditions.checkNotNull;

@Slf4j
public class ProxylessWeightedRoundRobinLoadBalancer extends RoundRobinLoadBalancer {
    private WeightedRoundRobinLoadBalancerConfig config;
    private final SynchronizationContext syncContext;
    private final ScheduledExecutorService timeService;
    private SynchronizationContext.ScheduledHandle weightUpdateTimer;
    private final Runnable updateWeightTask;
    private final Random random;
    private final Deadline.Ticker ticker;

    public ProxylessWeightedRoundRobinLoadBalancer(Helper helper, Deadline.Ticker ticker) {
        this(new WrrHelper(helper), ticker, new Random());
    }

    public ProxylessWeightedRoundRobinLoadBalancer(WrrHelper helper, Deadline.Ticker ticker, Random random) {
        super(helper);
        helper.setLoadBalancer(this);
        this.ticker = checkNotNull(ticker, "ticker");
        this.syncContext = checkNotNull(helper.getSynchronizationContext(), "syncContext");
        this.timeService = checkNotNull(helper.getScheduledExecutorService(), "timeService");
        this.updateWeightTask = new UpdateWeightTask();
        this.random = random;
        log.trace("weighted_round_robin LB created");
    }

    @Override
    public boolean acceptResolvedAddresses(LoadBalancer.ResolvedAddresses resolvedAddresses) {
        if (resolvedAddresses.getLoadBalancingPolicyConfig() == null) {
            handleNameResolutionError(Status.UNAVAILABLE.withDescription(
                    "NameResolver returned no WeightedRoundRobinLoadBalancerConfig. addrs="
                            + resolvedAddresses.getAddresses()
                            + ", attrs=" + resolvedAddresses.getAttributes()));
            return false;
        }
        config =
                (WeightedRoundRobinLoadBalancerConfig) resolvedAddresses.getLoadBalancingPolicyConfig();
        boolean accepted = super.acceptResolvedAddresses(resolvedAddresses);
        if (weightUpdateTimer != null && weightUpdateTimer.isPending()) {
            weightUpdateTimer.cancel();
        }
        updateWeightTask.run();
        afterAcceptAddresses();
        return accepted;
    }

    @Override
    public RoundRobinPicker createReadyPicker(List<Server> activeList) {
        return new WeightedRoundRobinPicker(activeList);
    }

    private final class UpdateWeightTask implements Runnable {
        @Override
        public void run() {
            boolean scheduleNext = true;
            if (currentPicker instanceof WeightedRoundRobinPicker) {
                boolean slowStartFinished = ((WeightedRoundRobinPicker) currentPicker).updateWeight();
                if (slowStartFinished) {
                    scheduleNext = false;
                }
            }

            if (!scheduleNext) {
                log.info("slow start finished, stop scheduling weight update task");
                return;
            }
            weightUpdateTimer = syncContext.schedule(this, config.weightUpdatePeriodNanos,
                    TimeUnit.NANOSECONDS, timeService);
        }
    }

    private void afterAcceptAddresses() {
    }

    @Override
    public void shutdown() {
        if (weightUpdateTimer != null) {
            weightUpdateTimer.cancel();
        }
        super.shutdown();
    }

    static final class WrrHelper extends ForwardingLoadBalancerHelper {
        private final Helper delegate;
        private ProxylessWeightedRoundRobinLoadBalancer wrr;

        WrrHelper(Helper helper) {
            this.delegate = helper;
        }

        void setLoadBalancer(ProxylessWeightedRoundRobinLoadBalancer lb) {
            this.wrr = lb;
        }

        @Override
        protected Helper delegate() {
            return delegate;
        }

        @Override
        public Server createServer(io.grpc.LoadBalancer.CreateSubchannelArgs args) {
            return wrr.new WrrServer(delegate().createServer(args));
        }
    }

    @VisibleForTesting
    final class WrrServer extends ForwardingServer {
        private final Server delegate;
        private final long firstObservedNanos;
        private volatile double weight;
        private volatile boolean slowStartFinished;

        WrrServer(Server delegate) {
            this.delegate = checkNotNull(delegate, "delegate");
            this.firstObservedNanos = ticker.nanoTime();
            this.slowStartFinished = false;
        }

        double getWeight() {
            if (config == null) {
                return 0; // normally, it must not be null
            }
            // a faster check
            if (slowStartFinished) {
                return 1.D;
            }
            final long now = ticker.nanoTime();
            final long uptimeNanos = now - firstObservedNanos;
            if (now - firstObservedNanos >= config.slowStartWindowNanos) {
                this.weight = 1.D;
                slowStartFinished = true;
            } else {
                final double timeFactor = Math.max(uptimeNanos, 1E9) / config.slowStartWindowNanos;
                this.weight = Math.max(config.minWeightPercent, Math.pow(timeFactor, 1.D / config.aggression));
            }
            return this.weight;
        }

        @Override
        protected Server delegate() {
            return this.delegate;
        }

        @Override
        public String toString() {
            return MoreObjects.toStringHelper(WrrServer.class).add("delegate", delegate)
                    .add("weight", weight)
                    .toString();
        }
    }

    @VisibleForTesting
    final class WeightedRoundRobinPicker extends RoundRobinPicker {
        private final List<Server> list;
        private volatile WeightedRoundRobinLoadBalancer.EdfScheduler scheduler;

        WeightedRoundRobinPicker(List<Server> list) {
            checkNotNull(list, "list");
            Preconditions.checkArgument(!list.isEmpty(), "empty list");
            this.list = list;
            updateWeight();
        }

        @Override
        public PickResult pickServer(PickServerArgs args) {
            Server server = list.get(scheduler.pick());
            return PickResult.withServer(server);
        }

        private boolean slowStartFinished() {
            for (final Server value : list) {
                final WrrServer wrrServer = (WrrServer) value;
                if (!wrrServer.slowStartFinished) {
                    return false;
                }
            }
            return true;
        }

        private boolean updateWeight() {
            int weightedChannelCount = 0;
            double avgWeight = 0;
            for (final Server value : list) {
                double newWeight = ((WrrServer) value).getWeight();
                if (newWeight > 0) {
                    avgWeight += newWeight;
                    weightedChannelCount++;
                }
            }
            final WeightedRoundRobinLoadBalancer.EdfScheduler scheduler =
                    new WeightedRoundRobinLoadBalancer.EdfScheduler(list.size(), random);
            if (weightedChannelCount >= 1) {
                avgWeight /= 1.0 * weightedChannelCount;
            } else {
                avgWeight = 1;
            }
            boolean slowStartFinished = true;
            for (int i = 0; i < list.size(); i++) {
                final WrrServer wrrServer = (WrrServer) list.get(i);
                double newWeight = wrrServer.getWeight();
                scheduler.add(i, newWeight > 0 ? newWeight : avgWeight);
                slowStartFinished = slowStartFinished && wrrServer.slowStartFinished;
            }
            this.scheduler = scheduler;
            return slowStartFinished;
        }

        @Override
        public String toString() {
            return MoreObjects.toStringHelper(WeightedRoundRobinPicker.class)
                    .add("list", list).toString();
        }

        @VisibleForTesting
        List<Server> getList() {
            return list;
        }

        @Override
        public boolean isEquivalentTo(RoundRobinPicker picker) {
            if (!(picker instanceof WeightedRoundRobinPicker)) {
                return false;
            }
            WeightedRoundRobinPicker other = (WeightedRoundRobinPicker) picker;
            if (other == this) {
                return true;
            }
            // the lists cannot contain duplicate servers
            return list.size() == other.list.size() && new HashSet<>(list).containsAll(other.list);
        }
    }

    @Builder
    @ToString
    @EqualsAndHashCode
    static final class WeightedRoundRobinLoadBalancerConfig {
        @Builder.Default
        final double minWeightPercent = 0.1; // If not specified, the default is 10%.

        @Builder.Default
        final double aggression = 1.0; // Defaults to 1.0, so that endpoint would get linearly increasing amount of traffic.

        final long slowStartWindowNanos;

        @Builder.Default
        final long weightUpdatePeriodNanos = 1_000_000_000L; // 1s;
    }
}
