package io.grpc.xds;

import com.wosai.middleware.hera.rpc.LoadBalancer;
import com.wosai.middleware.hera.rpc.LoadBalancerProvider;
import com.wosai.middleware.hera.rpc.LoadBalancerRegistry;
import io.grpc.InternalLogId;
import io.grpc.NameResolver;
import io.grpc.Status;
import io.grpc.SynchronizationContext;
import io.grpc.internal.ObjectPool;
import io.grpc.internal.ProxylessServiceConfigUtil;
import io.grpc.internal.ServiceConfigUtil;

import javax.annotation.Nullable;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Queue;

import static com.google.common.base.Preconditions.checkNotNull;
import static io.grpc.ConnectivityState.TRANSIENT_FAILURE;
import static io.grpc.xds.XdsLbPolicies.CLUSTER_RESOLVER_POLICY_NAME;

/**
 * Load balancer for cds_experimental LB policy. One instance per top-level cluster.
 * The top-level cluster may be a plain EDS/logical-DNS cluster or an aggregate cluster formed
 * by a group of sub-clusters in a tree hierarchy.
 */
public class ProxylessCdsLoadBalancer extends LoadBalancer {
    private final XdsSlf4jLogger logger;
    private final Helper helper;
    private final SynchronizationContext syncContext;
    private final LoadBalancerRegistry lbRegistry;
    // Following fields are effectively final.
    private ObjectPool<XdsClient> xdsClientPool;
    private XdsClient xdsClient;
    private CdsLbState cdsLbState;
    private io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses;

    public ProxylessCdsLoadBalancer(Helper helper) {
        this(helper, LoadBalancerRegistry.getDefaultRegistry());
    }

    ProxylessCdsLoadBalancer(LoadBalancer.Helper helper, LoadBalancerRegistry lbRegistry) {
        this.helper = checkNotNull(helper, "helper");
        this.syncContext = checkNotNull(helper.getSynchronizationContext(), "syncContext");
        this.lbRegistry = checkNotNull(lbRegistry, "lbRegistry");
        logger = XdsSlf4jLogger.withLogId(ProxylessCdsLoadBalancer.class, InternalLogId.allocate("cds-lb", helper.getAuthority()));
        logger.debug("Created");
    }

    @Override
    public boolean acceptResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses) {
        if (this.resolvedAddresses != null) {
            return true;
        }
        logger.trace("Received resolution result: {}", resolvedAddresses);
        this.resolvedAddresses = resolvedAddresses;
        xdsClientPool = resolvedAddresses.getAttributes().get(InternalXdsAttributes.XDS_CLIENT_POOL);
        xdsClient = xdsClientPool.getObject();
        CdsLoadBalancerProvider.CdsConfig config = (CdsLoadBalancerProvider.CdsConfig) resolvedAddresses.getLoadBalancingPolicyConfig();
        logger.debug("Config: {}", config);
        // config.name is the cluster name,
        // e.g. outbound|80|e2e-service-provider-red|e2e-service-provider.default.svc.cluster.local
        cdsLbState = new CdsLbState(config.name);
        cdsLbState.start();
        return true;
    }

    @Override
    public void handleNameResolutionError(Status error) {
        logger.warn("Received name resolution error: {}", error);
        if (cdsLbState != null && cdsLbState.childLb != null) {
            cdsLbState.childLb.handleNameResolutionError(error);
        } else {
            helper.updateBalancingState(TRANSIENT_FAILURE, new ProxylessXdsServerPickers.ErrorPicker(error));
        }
    }

    @Override
    public void shutdown() {
        logger.debug("Shutdown");
        if (cdsLbState != null) {
            cdsLbState.shutdown();
        }
        if (xdsClientPool != null) {
            xdsClientPool.returnObject(xdsClient);
        }
    }

    /**
     * The state of a CDS working session of {@link ProxylessCdsLoadBalancer}. Created and started when
     * receiving the CDS LB policy config with the top-level cluster name.
     */
    private final class CdsLbState {
        private final ClusterState root;
        private LoadBalancer childLb;

        private CdsLbState(String rootCluster) {
            root = new ClusterState(rootCluster);
        }

        private void start() {
            root.start();
        }

        private void shutdown() {
            root.shutdown();
            if (childLb != null) {
                childLb.shutdown();
            }
        }

        private void handleClusterDiscovered() {
            List<ClusterResolverLoadBalancerProvider.ClusterResolverConfig.DiscoveryMechanism> instances = new ArrayList<>();
            // Level-order traversal.
            // Collect configurations for all non-aggregate (leaf) clusters.
            Queue<ClusterState> queue = new ArrayDeque<>();
            queue.add(root);
            while (!queue.isEmpty()) {
                int size = queue.size();
                for (int i = 0; i < size; i++) {
                    ClusterState clusterState = queue.remove();
                    if (!clusterState.discovered) {
                        return;  // do not proceed until all clusters discovered
                    }
                    if (clusterState.result == null) {  // resource revoked or not exists
                        continue;
                    }
                    if (clusterState.isLeaf) { // For EDS, it is a leaf node
                        ClusterResolverLoadBalancerProvider.ClusterResolverConfig.DiscoveryMechanism instance;
                        if (clusterState.result.clusterType() == XdsClusterResource.CdsUpdate.ClusterType.EDS) {
                            instance = ClusterResolverLoadBalancerProvider.ClusterResolverConfig.DiscoveryMechanism.forEds(
                                    clusterState.name, clusterState.result.edsServiceName(),
                                    clusterState.result.lrsServerInfo(), clusterState.result.maxConcurrentRequests(),
                                    clusterState.result.upstreamTlsContext(), clusterState.result.outlierDetection());
                        } else {  // logical DNS
                            instance = ClusterResolverLoadBalancerProvider.ClusterResolverConfig.DiscoveryMechanism.forLogicalDns(
                                    clusterState.name, clusterState.result.dnsHostName(),
                                    clusterState.result.lrsServerInfo(), clusterState.result.maxConcurrentRequests(),
                                    clusterState.result.upstreamTlsContext());
                        }
                        instances.add(instance);
                    } else {
                        if (clusterState.childClusterStates != null) {
                            queue.addAll(clusterState.childClusterStates.values());
                        }
                    }
                }
            }
            if (instances.isEmpty()) {  // none of non-aggregate clusters exists
                if (childLb != null) {
                    childLb.shutdown();
                    childLb = null;
                }
                Status unavailable =
                        Status.UNAVAILABLE.withDescription("CDS error: found 0 leaf (logical DNS or EDS) "
                                + "clusters for root cluster " + root.name);
                helper.updateBalancingState(TRANSIENT_FAILURE, new ProxylessXdsServerPickers.ErrorPicker(unavailable));
                return;
            }

            // The LB policy config is provided in service_config.proto/JSON format. It is unwrapped
            // to determine the name of the policy in the load balancer registry.
            ServiceConfigUtil.LbConfig unwrappedLbConfig = ServiceConfigUtil.unwrapLoadBalancingConfig(
                    root.result.lbPolicyConfig());
            LoadBalancerProvider lbProvider = lbRegistry.getProvider(unwrappedLbConfig.getPolicyName());
            if (lbProvider == null) {
                throw NameResolver.ConfigOrError.fromError(Status.UNAVAILABLE.withDescription(
                                "No provider available for LB: " + unwrappedLbConfig.getPolicyName())).getError()
                        .asRuntimeException();
            }
            Map<String, Object> unwrappedLbConfigValue = (Map<String, Object>) unwrappedLbConfig.getRawConfigValue();
            final NameResolver.ConfigOrError configOrError = lbProvider.parseLoadBalancingPolicyConfig(unwrappedLbConfigValue);
            if (configOrError.getError() != null) {
                throw configOrError.getError().augmentDescription("Unable to parse the LB config")
                        .asRuntimeException();
            }

            ProxylessClusterResolverLoadBalancerProvider.ClusterResolverConfig config =
                    new ProxylessClusterResolverLoadBalancerProvider.ClusterResolverConfig(
                            Collections.unmodifiableList(instances),
                            new ProxylessServiceConfigUtil.PolicySelection(lbProvider, configOrError.getConfig())
                    );
            if (childLb == null) {
                childLb = lbRegistry.getProvider(CLUSTER_RESOLVER_POLICY_NAME).newLoadBalancer(helper);
            }
            childLb.handleResolvedAddresses(
                    resolvedAddresses.toBuilder().setLoadBalancingPolicyConfig(config).build());
        }

        private void handleClusterDiscoveryError(Status error) {
            if (childLb != null) {
                childLb.handleNameResolutionError(error);
            } else {
                helper.updateBalancingState(TRANSIENT_FAILURE, new ProxylessXdsServerPickers.ErrorPicker(error));
            }
        }

        private final class ClusterState implements XdsClient.ResourceWatcher<XdsClusterResource.CdsUpdate> {
            private final String name;
            @Nullable
            private Map<String, ClusterState> childClusterStates;
            @Nullable
            private XdsClusterResource.CdsUpdate result;
            // Following fields are effectively final.
            private boolean isLeaf;
            private boolean discovered;
            private boolean shutdown;

            private ClusterState(String name) {
                this.name = name;
            }

            private void start() {
                xdsClient.watchXdsResource(XdsClusterResource.getInstance(), name, this);
            }

            void shutdown() {
                shutdown = true;
                xdsClient.cancelXdsResourceWatch(XdsClusterResource.getInstance(), name, this);
                if (childClusterStates != null) {  // recursively shut down all descendants
                    for (ClusterState state : childClusterStates.values()) {
                        state.shutdown();
                    }
                }
            }

            @Override
            public void onError(Status error) {
                Status status = Status.UNAVAILABLE
                        .withDescription(
                                String.format("Unable to load CDS %s. xDS server returned: %s: %s",
                                        name, error.getCode(), error.getDescription()))
                        .withCause(error.getCause());
                syncContext.execute(new Runnable() {
                    @Override
                    public void run() {
                        if (shutdown) {
                            return;
                        }
                        // All watchers should receive the same error, so we only propagate it once.
                        if (ClusterState.this == root) {
                            handleClusterDiscoveryError(status);
                        }
                    }
                });
            }

            @Override
            public void onResourceDoesNotExist(String resourceName) {
                syncContext.execute(new Runnable() {
                    @Override
                    public void run() {
                        if (shutdown) {
                            return;
                        }
                        discovered = true;
                        result = null;
                        if (childClusterStates != null) {
                            for (ClusterState state : childClusterStates.values()) {
                                state.shutdown();
                            }
                            childClusterStates = null;
                        }
                        handleClusterDiscovered();
                    }
                });
            }

            @Override
            public void onChanged(final XdsClusterResource.CdsUpdate update) {
                class ClusterDiscovered implements Runnable {
                    @Override
                    public void run() {
                        if (shutdown) {
                            return;
                        }
                        logger.trace("Received cluster update {}", update);
                        discovered = true;
                        result = update;
                        if (update.clusterType() == XdsClusterResource.CdsUpdate.ClusterType.AGGREGATE) {
                            // Aggregate cluster is used for failover between clusters with different configuration,
                            // e.g., from EDS upstream cluster to STRICT_DNS upstream cluster
                            //       from cluster using ROUND_ROBIN load balancing policy to cluster using MAGLEV
                            //       from cluster with 0.1s connection timeout to cluster with 1s connection timeout
                            // https://www.envoyproxy.io/docs/envoy/latest/intro/arch_overview/upstream/aggregate_cluster
                            // TODO: how istio supports aggregate cluster?
                            isLeaf = false;
                            logger.debug("Aggregate cluster {}, underlying clusters: {}",
                                    update.clusterName(), update.prioritizedClusterNames());
                            Map<String, ClusterState> newChildStates = new LinkedHashMap<>();
                            for (String cluster : update.prioritizedClusterNames()) {
                                if (childClusterStates == null || !childClusterStates.containsKey(cluster)) {
                                    ClusterState childState = new ClusterState(cluster);
                                    childState.start();
                                    newChildStates.put(cluster, childState);
                                } else {
                                    newChildStates.put(cluster, childClusterStates.remove(cluster));
                                }
                            }
                            if (childClusterStates != null) {  // stop subscribing to revoked child clusters
                                for (ClusterState watcher : childClusterStates.values()) {
                                    watcher.shutdown();
                                }
                            }
                            childClusterStates = newChildStates;
                        } else if (update.clusterType() == XdsClusterResource.CdsUpdate.ClusterType.EDS) {
                            // for most cases, EDS clusters will be returned.
                            isLeaf = true;
                            logger.debug("EDS cluster {}, edsServiceName: {}",
                                    update.clusterName(), update.edsServiceName());
                        } else {  // logical DNS
                            isLeaf = true;
                            logger.debug("Logical DNS cluster {}", update.clusterName());
                        }
                        handleClusterDiscovered();
                    }
                }

                syncContext.execute(new ClusterDiscovered());
            }
        }
    }
}
