package io.grpc.xds;

import com.google.common.base.Preconditions;
import io.grpc.InternalLogId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

final class XdsSlf4jLogger {
    private final String prefix;
    private final Logger logger;

    static XdsSlf4jLogger withLogId(Class<?> clazz, InternalLogId logId) {
        Preconditions.checkNotNull(logId, "logId");
        return new XdsSlf4jLogger(clazz, logId.toString());
    }

    private XdsSlf4jLogger(Class<?> clazz, String prefix) {
        this.prefix = Preconditions.checkNotNull(prefix, "prefix");
        this.logger = LoggerFactory.getLogger(clazz);
    }

    public void warn(String msg, Object... arguments) {
        this.logger.warn("[" + prefix + "] " + msg, arguments);
    }

    public void error(String msg, Object... arguments) {
        this.logger.error("[" + prefix + "] " + msg, arguments);
    }

    public boolean isDebugEnabled() {
        return this.logger.isDebugEnabled();
    }

    public boolean isTraceEnabled() {
        return this.logger.isDebugEnabled();
    }

    public void info(String msg, Object... arguments) {
        this.logger.info("[" + prefix + "] " + msg, arguments);
    }

    public void debug(String msg, Object... arguments) {
        this.logger.debug("[" + prefix + "] " + msg, arguments);
    }

    public void trace(String msg, Object... arguments) {
        this.logger.trace("[" + prefix + "] " + msg, arguments);
    }
}
