package io.grpc.xds;

import com.google.common.base.Strings;

public final class ProxylessXdsNameConfigurer {
    /**
     * Try to set a fallback rdsResourceName for {@link ProxylessXdsNameResolver}
     * if {@link ProxylessXdsNameResolver#FALLBACK_RDS_RESOURCE_NAME} is empty or null.
     *
     * @param rdsWatchResourceName a user-defined rdsResourceName.
     * @return if the given name is set successfully.
     */
    public static boolean trySetFallbackRdsResourceName(String rdsWatchResourceName) {
        if (Strings.isNullOrEmpty(ProxylessXdsNameResolver.FALLBACK_RDS_RESOURCE_NAME)) {
            ProxylessXdsNameResolver.FALLBACK_RDS_RESOURCE_NAME = rdsWatchResourceName;
            return true;
        }
        return false;
    }
}
