package io.grpc.xds;

import com.google.common.base.Strings;
import com.wosai.middleware.hera.rpc.LoadBalancer;
import io.grpc.Attributes;
import io.grpc.CallOptions;
import io.grpc.Internal;
import io.grpc.NameResolver;
import io.grpc.Status;
import lombok.Getter;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkNotNull;
import static com.google.common.base.Preconditions.checkState;

@Internal
public abstract class ProxylessInternalConfigSelector {
    @NameResolver.ResolutionResultAttr
    public static final Attributes.Key<ProxylessInternalConfigSelector> KEY
            = Attributes.Key.create("io.grpc.proxyless-config-selector");
    public static final CallOptions.Key<String> CLUSTER_SELECTION_KEY =
            CallOptions.Key.create("io.grpc.xds.PROXYLESS_CLUSTER_SELECTION_KEY");
    static final CallOptions.Key<Long> RPC_HASH_KEY =
            CallOptions.Key.create("io.grpc.xds.PROXYLESS_RPC_HASH_KEY");

    /**
     * Selects the config for an PRC.
     * Use PickServerArgs for SelectConfigArgs for now. May change over time.
     */
    public abstract Result selectConfig(LoadBalancer.PickServerArgs args);

    @Getter
    public static final class Result {
        private final Status status;
        private final Object config;
        private final String cluster;
        private final Long hash;

        private Result(Status status, Object config, String cluster, Long hash) {
            this.status = checkNotNull(status, "status");
            this.config = config;
            this.cluster = cluster;
            this.hash = hash;
        }

        /**
         * Creates a {@code Result} with the given error status.
         */
        public static Result forError(Status status) {
            checkArgument(!status.isOk(), "status is OK");
            return new Result(status, null, null, null);
        }

        public static Builder newBuilder() {
            return new Builder();
        }

        public static final class Builder {
            private Object config;
            private String cluster;
            private Long hash;

            private Builder() {
            }

            /**
             * Sets the parsed config. This field is required.
             *
             * @return this
             */
            public Builder setConfig(Object config) {
                this.config = checkNotNull(config, "config");
                return this;
            }

            public Builder setCluster(String cluster) {
                this.cluster = checkNotNull(cluster, "cluster");
                return this;
            }

            public Builder setHash(Long hash) {
                this.hash = checkNotNull(hash, "hash");
                return this;
            }

            /**
             * Build this {@link Result}.
             */
            public Result build() {
                checkState(config != null, "config is not set");
                checkState(!Strings.isNullOrEmpty(cluster), "cluster is not set");
                checkState(hash != null, "hash is not set");
                return new Result(Status.OK, config, cluster, hash);
            }
        }
    }
}
