package io.grpc.xds;

import com.google.common.annotations.VisibleForTesting;
import com.wosai.middleware.hera.rpc.LoadBalancer;
import com.wosai.middleware.hera.rpc.LoadBalancerProvider;
import io.grpc.Deadline;
import io.grpc.NameResolver;
import io.grpc.Status;
import io.grpc.internal.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

// NOTE: WeightedRoundRobinLoadBalancerProvider was introduced from grpc-java 1.54.0
@Slf4j
public class ProxylessWeightedRoundRobinLoadBalancerProvider extends LoadBalancerProvider {
    @VisibleForTesting
    static final long MIN_WEIGHT_UPDATE_PERIOD_NANOS = 100_000_000L; // 100ms

    static final String SCHEME = "weighted_round_robin";

    @Override
    public LoadBalancer newLoadBalancer(LoadBalancer.Helper helper) {
        return new ProxylessWeightedRoundRobinLoadBalancer(helper, Deadline.getSystemTicker());
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 5;
    }

    @Override
    public String getPolicyName() {
        return SCHEME;
    }

    @Override
    public NameResolver.ConfigOrError parseLoadBalancingPolicyConfig(Map<String, ?> rawConfig) {
        try {
            return parseLoadBalancingPolicyConfigInternal(rawConfig);
        } catch (RuntimeException e) {
            return NameResolver.ConfigOrError.fromError(
                    Status.UNAVAILABLE.withCause(e).withDescription(
                            "Failed parsing configuration for " + getPolicyName()));
        }
    }

    private NameResolver.ConfigOrError parseLoadBalancingPolicyConfigInternal(Map<String, ?> rawConfig) {
        Long slowStartWindowNanos = JsonUtil.getStringAsDuration(rawConfig, LoadBalancerConfigFactory.SLOW_START_WINDOW_FIELD);
        Long weightUpdatePeriodNanos = JsonUtil.getStringAsDuration(rawConfig, "weightUpdatePeriod");
        Double aggression = JsonUtil.getNumberAsDouble(rawConfig, LoadBalancerConfigFactory.SLOW_START_AGGRESSION);
        Double minWeightPercent = JsonUtil.getNumberAsDouble(rawConfig, LoadBalancerConfigFactory.SLOW_START_MIN_WEIGHT_PERCENT);

        ProxylessWeightedRoundRobinLoadBalancer.WeightedRoundRobinLoadBalancerConfig.WeightedRoundRobinLoadBalancerConfigBuilder
                configBuilder = ProxylessWeightedRoundRobinLoadBalancer.WeightedRoundRobinLoadBalancerConfig.builder();
        if (slowStartWindowNanos != null) {
            configBuilder.slowStartWindowNanos(slowStartWindowNanos);
        }
        if (aggression != null) {
            configBuilder.aggression(aggression);
        }
        if (minWeightPercent != null) {
            configBuilder.minWeightPercent(minWeightPercent / 100);
        }
        if (weightUpdatePeriodNanos != null) {
            configBuilder.weightUpdatePeriodNanos(weightUpdatePeriodNanos);
            if (weightUpdatePeriodNanos < MIN_WEIGHT_UPDATE_PERIOD_NANOS) {
                configBuilder.weightUpdatePeriodNanos(MIN_WEIGHT_UPDATE_PERIOD_NANOS);
            }
        }
        return NameResolver.ConfigOrError.fromConfig(configBuilder.build());
    }
}
