package io.grpc.xds;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.wosai.middleware.hera.rpc.LoadBalancer;
import com.wosai.middleware.hera.rpc.LoadBalancerProvider;
import com.wosai.middleware.hera.rpc.LoadBalancerRegistry;
import com.wosai.middleware.hera.rpc.util.ForwardingLoadBalancerHelper;
import com.wosai.middleware.hera.rpc.util.GracefulSwitchLoadBalancer;
import io.grpc.Attributes;
import io.grpc.EquivalentAddressGroup;
import io.grpc.InternalLogId;
import io.grpc.NameResolver;
import io.grpc.Status;
import io.grpc.SynchronizationContext;
import io.grpc.internal.BackoffPolicy;
import io.grpc.internal.ExponentialBackoffPolicy;
import io.grpc.internal.ObjectPool;
import io.grpc.internal.ProxylessServiceConfigUtil;
import lombok.RequiredArgsConstructor;

import javax.annotation.Nullable;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.google.common.base.Preconditions.checkNotNull;
import static io.grpc.ConnectivityState.TRANSIENT_FAILURE;
import static io.grpc.xds.XdsLbPolicies.PRIORITY_POLICY_NAME;
import static java.util.stream.Collectors.toMap;

/**
 * Load balancer for cluster_resolver_experimental LB policy. This LB policy is the child LB policy
 * of the cds_experimental LB policy, i.e. {@link ProxylessCdsLoadBalancer} and
 * the parent LB policy of the priority_experimental LB policy, i.e. {@link ProxylessPriorityLoadBalancer}
 * in the xDS load balancing hierarchy. This policy resolves endpoints of non-aggregate
 * clusters (e.g., EDS or Logical DNS) and groups endpoints in priorities and localities to be
 * used in the downstream LB policies for fine-grained load balancing purposes.
 */
final class ProxylessClusterResolverLoadBalancer extends LoadBalancer {
    // DNS-resolved endpoints do not have the definition of the locality it belongs to, just hardcode
    // to an empty locality.
    private static final Locality LOGICAL_DNS_CLUSTER_LOCALITY = Locality.create("", "", "");
    // Locality LB is disabled by default.
    @VisibleForTesting
    static boolean ENABLE_LOCALITY_LOAD_BALANCING = !Strings.isNullOrEmpty(System.getenv("HERA_XDS_EXPERIMENTAL_LOCALITY_LOAD_BALANCING"))
            && Boolean.parseBoolean(System.getenv("HERA_XDS_EXPERIMENTAL_LOCALITY_LOAD_BALANCING"));

    private final XdsSlf4jLogger logger;
    private final SynchronizationContext syncContext;
    private final ScheduledExecutorService timeService;
    private final LoadBalancerRegistry lbRegistry;
    private final BackoffPolicy.Provider backoffPolicyProvider;
    private final GracefulSwitchLoadBalancer delegate;
    private ObjectPool<XdsClient> xdsClientPool;
    private XdsClient xdsClient;
    private ProxylessClusterResolverLoadBalancerProvider.ClusterResolverConfig config;

    ProxylessClusterResolverLoadBalancer(LoadBalancer.Helper helper) {
        this(helper, LoadBalancerRegistry.getDefaultRegistry(),
                new ExponentialBackoffPolicy.Provider());
    }

    @VisibleForTesting
    ProxylessClusterResolverLoadBalancer(Helper helper, LoadBalancerRegistry lbRegistry,
                                         BackoffPolicy.Provider backoffPolicyProvider) {
        this.lbRegistry = checkNotNull(lbRegistry, "lbRegistry");
        this.backoffPolicyProvider = checkNotNull(backoffPolicyProvider, "backoffPolicyProvider");
        this.syncContext = checkNotNull(helper.getSynchronizationContext(), "syncContext");
        this.timeService = checkNotNull(helper.getScheduledExecutorService(), "timeService");
        delegate = new GracefulSwitchLoadBalancer(helper);
        logger = XdsSlf4jLogger.withLogId(ProxylessClusterResolverLoadBalancer.class,
                InternalLogId.allocate("cluster-resolver-lb", helper.getAuthority()));
        logger.debug("Created");
    }

    @Override
    public boolean acceptResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses) {
        logger.trace("Received resolution result: {}", resolvedAddresses);
        if (xdsClientPool == null) {
            xdsClientPool = resolvedAddresses.getAttributes().get(InternalXdsAttributes.XDS_CLIENT_POOL);
            xdsClient = xdsClientPool.getObject();
        }
        ProxylessClusterResolverLoadBalancerProvider.ClusterResolverConfig config =
                (ProxylessClusterResolverLoadBalancerProvider.ClusterResolverConfig) resolvedAddresses.getLoadBalancingPolicyConfig();
        if (!Objects.equals(this.config, config)) {
            // For example,
            // ProxylessClusterResolverLoadBalancerProvider.ClusterResolverConfig(
            //   discoveryMechanisms=[DiscoveryMechanism{cluster=outbound|80|e2e-service-provider-base|e2e-service-provider.default.svc.cluster.local, type=EDS, edsServiceName=outbound|80|e2e-service-provider-base|e2e-service-provider.default.svc.cluster.local, dnsHostName=null, lrsServerInfo=null, maxConcurrentRequests=null}],
            //   lbPolicy=ProxylessServiceConfigUtil.PolicySelection(provider=ProxylessWrrLocalityLoadBalancerProvider{policy=wrr_locality_experimental, priority=5, available=true},
            //                                                       config=ProxylessWrrLocalityLoadBalancer.WrrLocalityConfig(childPolicy=ProxylessServiceConfigUtil.PolicySelection(provider=Provider{policy=round_robin, priority=5, available=true}, config=no service config)))
            // )
            // TODO: in which circumstances, there are multiple discoveryMechanisms/clusters?
            logger.trace("Config: {}", config);
            delegate.switchTo(new ClusterResolverLbStateFactory());
            this.config = config;
            delegate.handleResolvedAddresses(resolvedAddresses);
        }
        return true;
    }

    @Override
    public void shutdown() {
        logger.debug("Shutdown");
        delegate.shutdown();
        if (xdsClientPool != null) {
            xdsClientPool.returnObject(xdsClient);
        }
    }

    @Override
    public void handleNameResolutionError(Status error) {
        logger.warn("Received name resolution error: {}", error);
        delegate.handleNameResolutionError(error);
    }

    private final class ClusterResolverLbStateFactory extends LoadBalancer.Factory {
        @Override
        public LoadBalancer newLoadBalancer(LoadBalancer.Helper helper) {
            return new ClusterResolverLbState(helper);
        }
    }

    /**
     * The state of a cluster_resolver LB working session. A new instance is created whenever
     * the cluster_resolver LB receives a new config. The old instance is replaced when the
     * new one is ready to handle new RPCs.
     */
    private final class ClusterResolverLbState extends LoadBalancer {
        private final LoadBalancer.Helper helper;
        private final List<String> clusters = new ArrayList<>();
        private final Map<String, ClusterState> clusterStates = new HashMap<>();
        private ProxylessServiceConfigUtil.PolicySelection endpointLbPolicy;
        private io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses;
        private LoadBalancer childLb;

        ClusterResolverLbState(LoadBalancer.Helper helper) {
            this.helper = new RefreshableHelper(checkNotNull(helper, "helper"));
            logger.trace("New ClusterResolverLbState");
        }

        @Override
        public boolean acceptResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses) {
            this.resolvedAddresses = resolvedAddresses;
            ProxylessClusterResolverLoadBalancerProvider.ClusterResolverConfig config =
                    (ProxylessClusterResolverLoadBalancerProvider.ClusterResolverConfig) resolvedAddresses.getLoadBalancingPolicyConfig();
            endpointLbPolicy = config.lbPolicy;
            for (final ClusterResolverLoadBalancerProvider.ClusterResolverConfig.DiscoveryMechanism instance : config.discoveryMechanisms) {
                clusters.add(instance.cluster);
                ClusterState state;
                if (instance.type == ClusterResolverLoadBalancerProvider.ClusterResolverConfig.DiscoveryMechanism.Type.EDS) {
                    state = new EdsClusterState(instance.cluster, instance.edsServiceName,
                            instance.lrsServerInfo, instance.maxConcurrentRequests, instance.tlsContext,
                            instance.outlierDetection);
                } else {  // logical DNS
                    state = new LogicalDnsClusterState(instance.cluster, instance.dnsHostName,
                            instance.lrsServerInfo, instance.maxConcurrentRequests, instance.tlsContext);
                }
                clusterStates.put(instance.cluster, state);
                state.start();
            }
            return true;
        }

        @Override
        public void shutdown() {
            for (ClusterState state : clusterStates.values()) {
                state.shutdown();
            }
            if (childLb != null) {
                childLb.shutdown();
            }
        }

        @Override
        public void handleNameResolutionError(Status error) {
            if (childLb != null) {
                childLb.handleNameResolutionError(error);
            } else {
                helper.updateBalancingState(TRANSIENT_FAILURE, new ProxylessXdsServerPickers.ErrorPicker(error));
            }
        }

        private void handleEndpointResourceUpdate() {
            final List<EquivalentAddressGroup> addresses = new ArrayList<>();
            Map<String, ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig> priorityChildConfigs = new HashMap<>();
            List<String> priorities = new ArrayList<>();  // totally ordered priority list

            Status endpointNotFound = Status.OK;
            for (String cluster : clusters) {
                ClusterState state = clusterStates.get(cluster);
                // Propagate endpoints to the child LB policy only after all clusters have been resolved.
                if (!state.resolved && state.status.isOk()) {
                    return;
                }
                if (state.result != null) {
                    addresses.addAll(state.result.addresses);
                    priorityChildConfigs.putAll(state.result.priorityChildConfigs);
                    priorities.addAll(state.result.priorities);
                } else {
                    endpointNotFound = state.status;
                }
            }
            if (addresses.isEmpty()) {
                if (endpointNotFound.isOk()) {
                    endpointNotFound = Status.UNAVAILABLE.withDescription(
                            "No usable endpoint from cluster(s): " + clusters);
                } else {
                    endpointNotFound =
                            Status.UNAVAILABLE.withCause(endpointNotFound.getCause())
                                    .withDescription(endpointNotFound.getDescription());
                }
                helper.updateBalancingState(TRANSIENT_FAILURE, new ProxylessXdsServerPickers.ErrorPicker(endpointNotFound));
                if (childLb != null) {
                    childLb.shutdown();
                    childLb = null;
                }
                return;
            }
            ProxylessPriorityLoadBalancerProvider.PriorityLbConfig childConfig =
                    new ProxylessPriorityLoadBalancerProvider.PriorityLbConfig(Collections.unmodifiableMap(priorityChildConfigs),
                            Collections.unmodifiableList(priorities));
            if (childLb == null) {
                // create a priority lb as the child
                childLb = lbRegistry.getProvider(PRIORITY_POLICY_NAME).newLoadBalancer(helper);
            }
            childLb.handleResolvedAddresses(
                    resolvedAddresses.toBuilder()
                            .setLoadBalancingPolicyConfig(childConfig)
                            .setAddresses(Collections.unmodifiableList(addresses))
                            .build());
        }

        private void handleEndpointResolutionError() {
            boolean allInError = true;
            Status error = null;
            for (String cluster : clusters) {
                ClusterState state = clusterStates.get(cluster);
                if (state.status.isOk()) {
                    allInError = false;
                } else {
                    error = state.status;
                }
            }
            if (allInError) {
                if (childLb != null) {
                    childLb.handleNameResolutionError(error);
                } else {
                    helper.updateBalancingState(TRANSIENT_FAILURE, new ProxylessXdsServerPickers.ErrorPicker(error));
                }
            }
        }

        private final class RefreshableHelper extends ForwardingLoadBalancerHelper {
            private final LoadBalancer.Helper delegate;

            private RefreshableHelper(LoadBalancer.Helper delegate) {
                this.delegate = checkNotNull(delegate, "delegate");
            }

            @Override
            public void refreshNameResolution() {
                for (ClusterState state : clusterStates.values()) {
                    if (state instanceof LogicalDnsClusterState) {
                        ((LogicalDnsClusterState) state).refresh();
                    }
                }
            }

            @Override
            protected LoadBalancer.Helper delegate() {
                return delegate;
            }
        }

        /**
         * Resolution state of an underlying cluster.
         */
        private abstract class ClusterState {
            // Name of the cluster to be resolved.
            protected final String name;
            @Nullable
            protected final Bootstrapper.ServerInfo lrsServerInfo;
            @Nullable
            protected final Long maxConcurrentRequests;
            @Nullable
            protected final EnvoyServerProtoData.UpstreamTlsContext tlsContext;
            @Nullable
            protected final EnvoyServerProtoData.OutlierDetection outlierDetection;
            // Resolution status, may contain most recent error encountered.
            protected Status status = Status.OK;
            // True if has received resolution result.
            protected boolean resolved;
            // Most recently resolved addresses and config, or null if resource not exists.
            @Nullable
            protected ClusterResolutionResult result;

            protected boolean shutdown;

            private ClusterState(String name, @Nullable Bootstrapper.ServerInfo lrsServerInfo,
                                 @Nullable Long maxConcurrentRequests, @Nullable EnvoyServerProtoData.UpstreamTlsContext tlsContext,
                                 @Nullable EnvoyServerProtoData.OutlierDetection outlierDetection) {
                this.name = name;
                this.lrsServerInfo = lrsServerInfo;
                this.maxConcurrentRequests = maxConcurrentRequests;
                this.tlsContext = tlsContext;
                this.outlierDetection = outlierDetection;
            }

            abstract void start();

            void shutdown() {
                shutdown = true;
            }
        }

        private final class EdsClusterState extends ClusterState implements XdsClient.ResourceWatcher<XdsEndpointResource.EdsUpdate> {
            @Nullable
            private final String edsServiceName;
            // localityPriorityNames maps Locality to a formatted name, i.e. [${edsServiceName}:child${generatedId}]
            private Map<Locality, String> localityPriorityNames = Collections.emptyMap();
            int priorityNameGenId = 1;

            private EdsClusterState(String name, @Nullable String edsServiceName,
                                    @Nullable Bootstrapper.ServerInfo lrsServerInfo, @Nullable Long maxConcurrentRequests,
                                    @Nullable EnvoyServerProtoData.UpstreamTlsContext tlsContext, @Nullable EnvoyServerProtoData.OutlierDetection outlierDetection) {
                super(name, lrsServerInfo, maxConcurrentRequests, tlsContext, outlierDetection);
                this.edsServiceName = edsServiceName;
            }

            @Override
            void start() {
                String resourceName = edsServiceName != null ? edsServiceName : name;
                logger.debug("Start watching EDS resource {}", resourceName);
                xdsClient.watchXdsResource(XdsEndpointResource.getInstance(), resourceName, this);
            }

            @Override
            protected void shutdown() {
                super.shutdown();
                String resourceName = edsServiceName != null ? edsServiceName : name;
                logger.debug("Stop watching EDS resource {}", resourceName);
                xdsClient.cancelXdsResourceWatch(XdsEndpointResource.getInstance(), resourceName, this);
            }

            @Override
            public void onChanged(final XdsEndpointResource.EdsUpdate update) {
                class EndpointsUpdated implements Runnable {
                    @Override
                    public void run() {
                        if (shutdown) {
                            return;
                        }
                        // EDS watch callback with EdsUpdate:
                        // EdsUpdate{
                        //   clusterName=outbound|80|e2e-service-provider-red|e2e-service-provider.default.svc.cluster.local,
                        //   localityLbEndpointsMap={
                        //     Locality{region=, zone=, subZone=}=
                        //       LocalityLbEndpoints{
                        //         endpoints=[
                        //           LbEndpoint{eag=[[/10.244.1.6:8080]/{}], loadBalancingWeight=1, isHealthy=true},
                        //           LbEndpoint{eag=[[/10.244.1.7:8080]/{}], loadBalancingWeight=1, isHealthy=true},
                        //           LbEndpoint{eag=[[/10.244.1.8:8080]/{}], loadBalancingWeight=1, isHealthy=true}
                        //         ],
                        //         localityWeight=3,
                        //         priority=0
                        //       }
                        //     },
                        //   dropPolicies=[]
                        // }
                        // EDSUpdate may be probably transferred one by one
                        logger.trace("Received endpoint update {}", update);
                        if (logger.isDebugEnabled()) {
                            logger.debug("Cluster {}: {} localities, {} drop categories",
                                    update.clusterName, update.localityLbEndpointsMap.size(),
                                    update.dropPolicies.size());
                        }
                        final Map<Locality, Endpoints.LocalityLbEndpoints> localityLbEndpoints =
                                regroupLocalityEndpoints(update.localityLbEndpointsMap);
                        final List<Endpoints.DropOverload> dropOverloads = update.dropPolicies;
                        final List<EquivalentAddressGroup> addresses = new ArrayList<>();
                        final Map<String, Map<Locality, Integer>> prioritizedLocalityWeights = new HashMap<>();
                        final List<String> sortedPriorityNames = generatePriorityNames(name, localityLbEndpoints);
                        for (final Locality locality : localityLbEndpoints.keySet()) {
                            final Endpoints.LocalityLbEndpoints localityLbInfo = localityLbEndpoints.get(locality);
                            // find pre-calculated name
                            final String priorityName = localityPriorityNames.get(locality);
                            boolean discard = true;
                            for (final Endpoints.LbEndpoint endpoint : localityLbInfo.endpoints()) {
                                if (endpoint.isHealthy()) {
                                    discard = false;
                                    long weight = localityLbInfo.localityWeight();
                                    if (endpoint.loadBalancingWeight() != 0) {
                                        weight *= endpoint.loadBalancingWeight();
                                    }
                                    final Attributes attr = endpoint.eag().getAttributes().toBuilder()
                                            .set(InternalXdsAttributes.ATTR_LOCALITY, locality)
                                            .set(InternalXdsAttributes.ATTR_LOCALITY_WEIGHT,
                                                    localityLbInfo.localityWeight())
                                            .set(InternalXdsAttributes.ATTR_SERVER_WEIGHT, weight)
                                            .build();
                                    EquivalentAddressGroup eag = new EquivalentAddressGroup(
                                            endpoint.eag().getAddresses(), attr);
                                    eag = AddressFilter.setPathFilter(
                                            eag, Arrays.asList(priorityName, localityName(locality)));
                                    addresses.add(eag);
                                }
                            }
                            if (discard) {
                                logger.debug("Discard locality {} with 0 healthy endpoints", locality);
                                continue;
                            }
                            prioritizedLocalityWeights.computeIfAbsent(priorityName, k -> new HashMap<>())
                                    .put(locality, localityLbInfo.localityWeight());
                        }
                        if (prioritizedLocalityWeights.isEmpty()) {
                            // Will still update the result, as if the cluster resource is revoked.
                            logger.debug("Cluster {} has no usable priority/locality/endpoint", update.clusterName);
                        }
                        sortedPriorityNames.retainAll(prioritizedLocalityWeights.keySet());
                        Map<String, ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig> priorityChildConfigs =
                                generateEdsBasedPriorityChildConfigs(
                                        name, edsServiceName, lrsServerInfo, maxConcurrentRequests, tlsContext,
                                        outlierDetection, endpointLbPolicy, lbRegistry, prioritizedLocalityWeights,
                                        dropOverloads);
                        status = Status.OK;
                        resolved = true;
                        result = new ClusterResolutionResult(addresses, priorityChildConfigs, sortedPriorityNames);
                        handleEndpointResourceUpdate();
                    }
                }

                syncContext.execute(new EndpointsUpdated());
            }

            private Map<Locality, Endpoints.LocalityLbEndpoints> regroupLocalityEndpoints(Map<Locality,
                    Endpoints.LocalityLbEndpoints> localityLbEndpoints) {
                if (ENABLE_LOCALITY_LOAD_BALANCING) {
                    return localityLbEndpoints;
                }
                // When locality lb is disabled, the zone and subZone fields are both ignored and set to the same Locality
                // See https://github.com/grpc/grpc-java/issues/10688
                return localityLbEndpoints.entrySet().stream().map(e -> {
                    final Locality locality = Locality.create(e.getKey().region(), "", "");
                    return Maps.immutableEntry(locality, e.getValue());
                }).collect(toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> {
                    int newLocalityWeight = a.localityWeight() + b.localityWeight();
                    ImmutableList<Endpoints.LbEndpoint> newEndpoints = ImmutableList.<Endpoints.LbEndpoint>builder()
                            .addAll(a.endpoints())
                            .addAll(b.endpoints())
                            .build();
                    return Endpoints.LocalityLbEndpoints.create(newEndpoints, newLocalityWeight, 0);
                }, HashMap::new));
            }

            private List<String> generatePriorityNames(String name,
                                                       Map<Locality, Endpoints.LocalityLbEndpoints> localityLbEndpoints) {
                // Int -> [Locality{region=, zone=, subZone=}]
                final TreeMap<Integer, List<Locality>> todo = new TreeMap<>();
                for (final Locality locality : localityLbEndpoints.keySet()) {
                    final int priority = localityLbEndpoints.get(locality).priority();
                    todo.computeIfAbsent(priority, k -> new ArrayList<>())
                            .add(locality);
                }
                final Map<Locality, String> newNames = new HashMap<>();
                final Set<String> usedNames = new HashSet<>();
                final List<String> ret = new ArrayList<>();
                for (final Integer priority : todo.keySet()) {
                    String foundName = "";
                    for (Locality locality : todo.get(priority)) {
                        if (localityPriorityNames.containsKey(locality)
                                && usedNames.add(localityPriorityNames.get(locality))) {
                            foundName = localityPriorityNames.get(locality);
                            break;
                        }
                    }
                    if ("".equals(foundName)) { // no used name found
                        foundName = String.format(Locale.US, "%s[child%d]", name, priorityNameGenId++);
                    }
                    for (final Locality locality : todo.get(priority)) {
                        newNames.put(locality, foundName); // memorize locality -> name
                    }
                    ret.add(foundName);
                }
                localityPriorityNames = newNames;
                return ret;
            }

            @Override
            public void onResourceDoesNotExist(final String resourceName) {
                syncContext.execute(new Runnable() {
                    @Override
                    public void run() {
                        if (shutdown) {
                            return;
                        }
                        logger.debug("Resource {} unavailable", resourceName);
                        status = Status.OK;
                        resolved = true;
                        result = null;  // resource revoked
                        handleEndpointResourceUpdate();
                    }
                });
            }

            @Override
            public void onError(final Status error) {
                syncContext.execute(new Runnable() {
                    @Override
                    public void run() {
                        if (shutdown) {
                            return;
                        }
                        String resourceName = edsServiceName != null ? edsServiceName : name;
                        status = Status.UNAVAILABLE
                                .withDescription(String.format("Unable to load EDS %s. xDS server returned: %s: %s",
                                        resourceName, error.getCode(), error.getDescription()))
                                .withCause(error.getCause());
                        logger.warn("Received EDS error: {}", error);
                        handleEndpointResolutionError();
                    }
                });
            }
        }

        private final class LogicalDnsClusterState extends ClusterState {
            private final String dnsHostName;
            private final NameResolver.Factory nameResolverFactory;
            private final NameResolver.Args nameResolverArgs;
            private NameResolver resolver;
            @Nullable
            private BackoffPolicy backoffPolicy;
            @Nullable
            private SynchronizationContext.ScheduledHandle scheduledRefresh;

            private LogicalDnsClusterState(String name, String dnsHostName,
                                           @Nullable Bootstrapper.ServerInfo lrsServerInfo, @Nullable Long maxConcurrentRequests,
                                           @Nullable EnvoyServerProtoData.UpstreamTlsContext tlsContext) {
                super(name, lrsServerInfo, maxConcurrentRequests, tlsContext, null);
                this.dnsHostName = checkNotNull(dnsHostName, "dnsHostName");
                nameResolverFactory =
                        checkNotNull(helper.getNameResolverRegistry().asFactory(), "nameResolverFactory");
                nameResolverArgs = checkNotNull(helper.getNameResolverArgs(), "nameResolverArgs");
            }

            @Override
            void start() {
                URI uri;
                try {
                    uri = new URI("dns", "", "/" + dnsHostName, null);
                } catch (URISyntaxException e) {
                    status = Status.INTERNAL.withDescription(
                            "Bug, invalid URI creation: " + dnsHostName).withCause(e);
                    handleEndpointResolutionError();
                    return;
                }
                resolver = nameResolverFactory.newNameResolver(uri, nameResolverArgs);
                if (resolver == null) {
                    status = Status.INTERNAL.withDescription("Xds cluster resolver lb for logical DNS "
                            + "cluster [" + name + "] cannot find DNS resolver with uri:" + uri);
                    handleEndpointResolutionError();
                    return;
                }
                resolver.start(new NameResolverListener());
            }

            void refresh() {
                if (resolver == null) {
                    return;
                }
                cancelBackoff();
                resolver.refresh();
            }

            @Override
            void shutdown() {
                super.shutdown();
                if (resolver != null) {
                    resolver.shutdown();
                }
                cancelBackoff();
            }

            private void cancelBackoff() {
                if (scheduledRefresh != null) {
                    scheduledRefresh.cancel();
                    scheduledRefresh = null;
                    backoffPolicy = null;
                }
            }

            private class DelayedNameResolverRefresh implements Runnable {
                @Override
                public void run() {
                    scheduledRefresh = null;
                    if (!shutdown) {
                        resolver.refresh();
                    }
                }
            }

            private class NameResolverListener extends NameResolver.Listener2 {
                @Override
                public void onResult(final NameResolver.ResolutionResult resolutionResult) {
                    class NameResolved implements Runnable {
                        @Override
                        public void run() {
                            if (shutdown) {
                                return;
                            }
                            backoffPolicy = null;  // reset backoff sequence if succeeded
                            // Arbitrary priority notation for all DNS-resolved endpoints.
                            String priorityName = priorityName(name, 0);  // value doesn't matter
                            List<EquivalentAddressGroup> addresses = new ArrayList<>();
                            for (EquivalentAddressGroup eag : resolutionResult.getAddresses()) {
                                // No weight attribute is attached, all endpoint-level LB policy should be able
                                // to handle such it.
                                Attributes attr = eag.getAttributes().toBuilder().set(
                                        InternalXdsAttributes.ATTR_LOCALITY, LOGICAL_DNS_CLUSTER_LOCALITY).build();
                                eag = new EquivalentAddressGroup(eag.getAddresses(), attr);
                                eag = AddressFilter.setPathFilter(
                                        eag, Arrays.asList(priorityName, LOGICAL_DNS_CLUSTER_LOCALITY.toString()));
                                addresses.add(eag);
                            }
                            ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig priorityChildConfig =
                                    generateDnsBasedPriorityChildConfig(name, lrsServerInfo, maxConcurrentRequests,
                                            tlsContext, lbRegistry, Collections.emptyList());
                            status = Status.OK;
                            resolved = true;
                            result = new ClusterResolutionResult(addresses, priorityName, priorityChildConfig);
                            handleEndpointResourceUpdate();
                        }
                    }

                    syncContext.execute(new NameResolved());
                }

                @Override
                public void onError(final Status error) {
                    syncContext.execute(new Runnable() {
                        @Override
                        public void run() {
                            if (shutdown) {
                                return;
                            }
                            status = error;
                            // NameResolver.Listener API cannot distinguish between address-not-found and
                            // transient errors. If the error occurs in the first resolution, treat it as
                            // address not found. Otherwise, either there is previously resolved addresses
                            // previously encountered error, propagate the error to downstream/upstream and
                            // let downstream/upstream handle it.
                            if (!resolved) {
                                resolved = true;
                                handleEndpointResourceUpdate();
                            } else {
                                handleEndpointResolutionError();
                            }
                            if (scheduledRefresh != null && scheduledRefresh.isPending()) {
                                return;
                            }
                            if (backoffPolicy == null) {
                                backoffPolicy = backoffPolicyProvider.get();
                            }
                            long delayNanos = backoffPolicy.nextBackoffNanos();
                            logger.trace("Logical DNS resolver for cluster {} encountered name resolution "
                                            + "error: {}, scheduling DNS resolution backoff for {} ns",
                                    name, error, delayNanos);
                            scheduledRefresh =
                                    syncContext.schedule(
                                            new DelayedNameResolverRefresh(), delayNanos, TimeUnit.NANOSECONDS,
                                            timeService);
                        }
                    });
                }
            }
        }
    }

    @RequiredArgsConstructor
    private static class ClusterResolutionResult {
        // Endpoint addresses.
        private final List<EquivalentAddressGroup> addresses;
        // Config (include load balancing policy/config) for each priority in the cluster.
        private final Map<String, ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig> priorityChildConfigs;
        // List of priority names ordered in descending priorities.
        private final List<String> priorities;

        ClusterResolutionResult(List<EquivalentAddressGroup> addresses, String priority,
                                ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig config) {
            this(addresses, Collections.singletonMap(priority, config),
                    Collections.singletonList(priority));
        }
    }

    /**
     * Generates the config to be used in the priority LB policy for the single priority of
     * logical DNS cluster.
     *
     * <p>priority LB -> cluster_impl LB (single hardcoded priority) -> pick_first
     */
    private static ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig generateDnsBasedPriorityChildConfig(
            String cluster, @Nullable Bootstrapper.ServerInfo lrsServerInfo, @Nullable Long maxConcurrentRequests,
            @Nullable EnvoyServerProtoData.UpstreamTlsContext tlsContext, LoadBalancerRegistry lbRegistry,
            List<Endpoints.DropOverload> dropOverloads) {
        // Override endpoint-level LB policy with pick_first for logical DNS cluster.
        ProxylessServiceConfigUtil.PolicySelection endpointLbPolicy =
                new ProxylessServiceConfigUtil.PolicySelection(lbRegistry.getProvider("pick_first"), null);
        ProxylessClusterImplLoadBalancerProvider.ClusterImplConfig clusterImplConfig =
                new ProxylessClusterImplLoadBalancerProvider.ClusterImplConfig(cluster, null, lrsServerInfo, maxConcurrentRequests,
                        dropOverloads, endpointLbPolicy, tlsContext);
        LoadBalancerProvider clusterImplLbProvider =
                lbRegistry.getProvider(XdsLbPolicies.CLUSTER_IMPL_POLICY_NAME);
        ProxylessServiceConfigUtil.PolicySelection clusterImplPolicy =
                new ProxylessServiceConfigUtil.PolicySelection(clusterImplLbProvider, clusterImplConfig);
        return new ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig(clusterImplPolicy, false /* ignoreReresolution*/);
    }

    /**
     * Generates configs to be used in the priority LB policy for priorities in an EDS cluster.
     * <p>
     * priority LB -> cluster_impl LB (one per priority) -> (weighted_target LB
     * -> round_robin / least_request_experimental (one per locality)) / ring_hash_experimental
     */
    private static Map<String, ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig> generateEdsBasedPriorityChildConfigs(
            String cluster, @Nullable String edsServiceName, @Nullable Bootstrapper.ServerInfo lrsServerInfo,
            @Nullable Long maxConcurrentRequests, @Nullable EnvoyServerProtoData.UpstreamTlsContext tlsContext,
            @Nullable EnvoyServerProtoData.OutlierDetection outlierDetection, ProxylessServiceConfigUtil.PolicySelection endpointLbPolicy,
            LoadBalancerRegistry lbRegistry,
            Map<String, Map<Locality, Integer>> prioritizedLocalityWeights, List<Endpoints.DropOverload> dropOverloads) {
        final Map<String, ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig> configs =
                new HashMap<>();
        for (final String priority : prioritizedLocalityWeights.keySet()) {
            ProxylessClusterImplLoadBalancerProvider.ClusterImplConfig clusterImplConfig =
                    new ProxylessClusterImplLoadBalancerProvider.ClusterImplConfig(cluster, edsServiceName, lrsServerInfo, maxConcurrentRequests,
                            dropOverloads, endpointLbPolicy, tlsContext);
            LoadBalancerProvider clusterImplLbProvider =
                    lbRegistry.getProvider(XdsLbPolicies.CLUSTER_IMPL_POLICY_NAME);
            ProxylessServiceConfigUtil.PolicySelection priorityChildPolicy =
                    new ProxylessServiceConfigUtil.PolicySelection(clusterImplLbProvider, clusterImplConfig);

            // If outlier detection has been configured we wrap the child policy in the outlier detection
            // load balancer.
            if (outlierDetection != null) {
                // TODO: support outlierDetection
                throw new UnsupportedOperationException("outlierDetection is not supported");
            }

            ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig priorityChildConfig =
                    new ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig(priorityChildPolicy, true /* ignoreReresolution */);
            configs.put(priority, priorityChildConfig);
        }
        return configs;
    }

    /**
     * Generates a string that represents the priority in the LB policy config. The string is unique
     * across priorities in all clusters and priorityName(c, p1) < priorityName(c, p2) iff p1 < p2.
     * The ordering is undefined for priorities in different clusters.
     */
    private static String priorityName(String cluster, int priority) {
        return cluster + "[child" + priority + "]";
    }

    /**
     * Generates a string that represents the locality in the LB policy config. The string is unique
     * across all localities in all clusters.
     */
    private static String localityName(Locality locality) {
        return locality.toString();
    }
}
