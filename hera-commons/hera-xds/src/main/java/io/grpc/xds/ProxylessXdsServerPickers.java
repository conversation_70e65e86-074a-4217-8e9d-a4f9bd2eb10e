package io.grpc.xds;

import com.google.common.base.MoreObjects;
import com.wosai.middleware.hera.rpc.LoadBalancer;
import io.grpc.Status;

import static com.google.common.base.Preconditions.checkNotNull;

final class ProxylessXdsServerPickers {
    private ProxylessXdsServerPickers() { /* DO NOT CALL ME */ }

    static final LoadBalancer.ServerPicker BUFFER_PICKER = new LoadBalancer.ServerPicker() {
        @Override
        public LoadBalancer.PickResult pickServer(LoadBalancer.PickServerArgs args) {
            return LoadBalancer.PickResult.withNoResult();
        }

        @Override
        public String toString() {
            return "BUFFER_PICKER";
        }
    };

    static final class ErrorPicker extends LoadBalancer.ServerPicker {

        private final Status error;

        ErrorPicker(Status error) {
            this.error = checkNotNull(error, "error");
        }

        @Override
        public LoadBalancer.PickResult pickServer(LoadBalancer.PickServerArgs args) {
            return LoadBalancer.PickResult.withError(error);
        }

        public String toString() {
            return MoreObjects.toStringHelper(this).add("error", this.error).toString();
        }
    }
}
