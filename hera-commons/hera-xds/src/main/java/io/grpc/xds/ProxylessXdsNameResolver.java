package io.grpc.xds;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.wosai.middleware.hera.rpc.LoadBalancer;
import io.grpc.Attributes;
import io.grpc.InternalLogId;
import io.grpc.NameResolver;
import io.grpc.Status;
import io.grpc.SynchronizationContext;
import io.grpc.internal.GrpcUtil;
import io.grpc.internal.ObjectPool;
import io.grpc.xds.internal.Matchers;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.atomic.AtomicInteger;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkNotNull;
import static io.grpc.xds.Bootstrapper.XDSTP_SCHEME;

/**
 * A {@link NameResolver} for resolving gRPC target names with "xds:" scheme.
 *
 * <p>Resolving a gRPC target involves contacting the control plane management server via xDS
 * protocol to retrieve service information and produce a service config to the caller.
 *
 * @see XdsNameResolverProvider
 */
final class ProxylessXdsNameResolver extends NameResolver {
    @VisibleForTesting
    static boolean ENABLE_TIMEOUT =
            Strings.isNullOrEmpty(System.getenv("GRPC_XDS_EXPERIMENTAL_ENABLE_TIMEOUT"))
                    || Boolean.parseBoolean(System.getenv("GRPC_XDS_EXPERIMENTAL_ENABLE_TIMEOUT"));

    static String FALLBACK_RDS_RESOURCE_NAME = System.getenv("PROXYLESS_FALLBACK_RDS_RESOURCE_NAME");

    private final InternalLogId logId;
    private final XdsSlf4jLogger logger;
    @Nullable
    private final String targetAuthority;
    private final String serviceAuthority;
    private final String overrideAuthority;
    private final ServiceConfigParser serviceConfigParser;
    private final SynchronizationContext syncContext;
    private final ScheduledExecutorService scheduler;
    private final XdsNameResolverProvider.XdsClientPoolFactory xdsClientPoolFactory;
    private final ThreadSafeRandom random;
    private final FilterRegistry filterRegistry;
    private final XxHash64 hashFunc = XxHash64.INSTANCE;
    // Clusters (with reference counts) to which new/existing requests can be/are routed.
    // put()/remove() must be called in SyncContext, and get() can be called in any thread.
    private final ConcurrentMap<String, ClusterRefState> clusterRefs = new ConcurrentHashMap<>();
    private final ConfigSelector configSelector = new ConfigSelector();
    private final long randomChannelId;

    private volatile RoutingConfig routingConfig = RoutingConfig.EMPTY;
    private Listener2 listener;
    private ObjectPool<XdsClient> xdsClientPool;
    private XdsClient xdsClient;
    private XdsNameResolverProvider.CallCounterProvider callCounterProvider;
    private ResolveState resolveState;
    // Workaround for https://github.com/grpc/grpc-java/issues/8886 . This should be handled in
    // XdsClient instead of here.
    private boolean receivedConfig;

    ProxylessXdsNameResolver(
            @Nullable String targetAuthority, String name, @Nullable String overrideAuthority,
            ServiceConfigParser serviceConfigParser,
            SynchronizationContext syncContext, ScheduledExecutorService scheduler,
            @Nullable Map<String, ?> bootstrapOverride) {
        this(targetAuthority, name, overrideAuthority, serviceConfigParser, syncContext, scheduler,
                SharedXdsClientPoolProvider.getDefaultProvider(), ThreadSafeRandom.ThreadSafeRandomImpl.instance,
                FilterRegistry.getDefaultRegistry(), bootstrapOverride);
    }

    @VisibleForTesting
    ProxylessXdsNameResolver(
            @Nullable String targetAuthority, String name, @Nullable String overrideAuthority,
            ServiceConfigParser serviceConfigParser,
            SynchronizationContext syncContext, ScheduledExecutorService scheduler,
            XdsNameResolverProvider.XdsClientPoolFactory xdsClientPoolFactory, ThreadSafeRandom random,
            FilterRegistry filterRegistry, @Nullable Map<String, ?> bootstrapOverride) {
        this.targetAuthority = targetAuthority;
        serviceAuthority = GrpcUtil.checkAuthority(checkNotNull(name, "name"));
        this.overrideAuthority = overrideAuthority;
        this.serviceConfigParser = checkNotNull(serviceConfigParser, "serviceConfigParser");
        this.syncContext = checkNotNull(syncContext, "syncContext");
        this.scheduler = checkNotNull(scheduler, "scheduler");
        this.xdsClientPoolFactory = bootstrapOverride == null ? checkNotNull(xdsClientPoolFactory,
                "xdsClientPoolFactory") : new SharedXdsClientPoolProvider();
        this.xdsClientPoolFactory.setBootstrapOverride(bootstrapOverride);
        this.random = checkNotNull(random, "random");
        this.filterRegistry = checkNotNull(filterRegistry, "filterRegistry");
        randomChannelId = random.nextLong();
        logId = InternalLogId.allocate("xds-resolver", name);
        logger = XdsSlf4jLogger.withLogId(ProxylessXdsNameResolver.class, logId);
        logger.debug("Created resolver for {}", name);
    }

    @Override
    public String getServiceAuthority() {
        return serviceAuthority;
    }

    @Override
    public void start(Listener2 listener) {
        this.listener = checkNotNull(listener, "listener");
        try {
            xdsClientPool = xdsClientPoolFactory.getOrCreate();
        } catch (Exception e) {
            listener.onError(
                    Status.UNAVAILABLE.withDescription("Failed to initialize xDS").withCause(e));
            return;
        }
        xdsClient = xdsClientPool.getObject();
        Bootstrapper.BootstrapInfo bootstrapInfo = xdsClient.getBootstrapInfo();
        String listenerNameTemplate;
        if (targetAuthority == null) {
            listenerNameTemplate = bootstrapInfo.clientDefaultListenerResourceNameTemplate();
        } else {
            Bootstrapper.AuthorityInfo authorityInfo = bootstrapInfo.authorities().get(targetAuthority);
            if (authorityInfo == null) {
                listener.onError(Status.INVALID_ARGUMENT.withDescription(
                        "invalid target URI: target authority not found in the bootstrap"));
                return;
            }
            listenerNameTemplate = authorityInfo.clientListenerResourceNameTemplate();
        }
        String replacement = serviceAuthority;
        if (listenerNameTemplate.startsWith(XDSTP_SCHEME)) {
            replacement = XdsClient.percentEncodePath(replacement);
        }
        String ldsResourceName = expandPercentS(listenerNameTemplate, replacement);
        if (!XdsClient.isResourceNameValid(ldsResourceName, XdsListenerResource.getInstance().typeUrl())
                && !XdsClient.isResourceNameValid(ldsResourceName,
                XdsListenerResource.getInstance().typeUrl())) {
            listener.onError(Status.INVALID_ARGUMENT.withDescription(
                    "invalid listener resource URI for service authority: " + serviceAuthority));
            return;
        }
        ldsResourceName = XdsClient.canonifyResourceName(ldsResourceName);
        callCounterProvider = SharedCallCounterMap.getInstance();
        resolveState = new ResolveState(ldsResourceName);
        resolveState.start();
    }

    private static String expandPercentS(String template, String replacement) {
        return template.replace("%s", replacement);
    }

    @Override
    public void shutdown() {
        logger.debug("Shutdown");
        if (resolveState != null) {
            resolveState.stop();
        }
        if (xdsClient != null) {
            xdsClient = xdsClientPool.returnObject(xdsClient);
        }
    }

    @VisibleForTesting
    XdsClient getXdsClient() {
        return xdsClient;
    }

    // called in syncContext
    private void updateResolutionResult() {
        syncContext.throwIfNotInThisSynchronizationContext();

        ImmutableMap.Builder<String, Object> childPolicy = new ImmutableMap.Builder<>();
        for (String name : clusterRefs.keySet()) {
            Map<String, ?> lbPolicy = clusterRefs.get(name).toLbPolicy();
            childPolicy.put(name, ImmutableMap.of("lbPolicy", ImmutableList.of(lbPolicy)));
        }
        Map<String, ?> rawServiceConfig = ImmutableMap.of(
                "loadBalancingConfig",
                ImmutableList.of(ImmutableMap.of(
                        XdsLbPolicies.CLUSTER_MANAGER_POLICY_NAME,
                        ImmutableMap.of("childPolicy", childPolicy.buildOrThrow()))));

        if (logger.isDebugEnabled()) {
            logger.debug("Generated service config:\n{}", new Gson().toJson(rawServiceConfig));
        }
        ConfigOrError parsedServiceConfig = serviceConfigParser.parseServiceConfig(rawServiceConfig);
        Attributes attrs =
                Attributes.newBuilder()
                        .set(InternalXdsAttributes.XDS_CLIENT_POOL, xdsClientPool)
                        .set(InternalXdsAttributes.CALL_COUNTER_PROVIDER, callCounterProvider)
                        .set(ProxylessInternalConfigSelector.KEY, configSelector)
                        .build();
        ResolutionResult result =
                ResolutionResult.newBuilder()
                        .setAttributes(attrs)
                        .setServiceConfig(parsedServiceConfig)
                        .build();
        listener.onResult(result);
        receivedConfig = true;
    }

    private final class ConfigSelector extends ProxylessInternalConfigSelector {
        @Override
        public Result selectConfig(LoadBalancer.PickServerArgs args) {
            String cluster = null;
            VirtualHost.Route selectedRoute = null;
            RoutingConfig routingCfg;
            Map<String, Filter.FilterConfig> selectedOverrideConfigs;
            Map<String, String> headers = args.getHeaders();
            do {
                routingCfg = routingConfig;
                selectedOverrideConfigs = new HashMap<>(routingCfg.virtualHostOverrideConfig);
                for (VirtualHost.Route route : routingCfg.routes) {
                    if (matchRoute(route.routeMatch(), args.path() + "/" + args.rpcMethod(),
                            headers, random)) {
                        selectedRoute = route;
                        selectedOverrideConfigs.putAll(route.filterConfigOverrides());
                        break;
                    }
                }
                if (selectedRoute == null) {
                    return Result.forError(
                            Status.UNAVAILABLE.withDescription("Could not find xDS route matching RPC"));
                }
                if (selectedRoute.routeAction() == null) {
                    return Result.forError(Status.UNAVAILABLE.withDescription(
                            "Could not route RPC to Route with non-forwarding action"));
                }
                VirtualHost.Route.RouteAction action = selectedRoute.routeAction();
                // Support Headers manipulation
                if (!action.requestHeadersToAdd().isEmpty()) {
                    headers.putAll(action.requestHeadersToAdd());
                }
                if (action.cluster() != null) {
                    cluster = prefixedClusterName(action.cluster());
                } else if (action.weightedClusters() != null) {
                    long totalWeight = 0;
                    for (VirtualHost.Route.RouteAction.ClusterWeight weightedCluster : action.weightedClusters()) {
                        totalWeight += weightedCluster.weight();
                    }
                    long select = random.nextLong(totalWeight);
                    long accumulator = 0;
                    for (VirtualHost.Route.RouteAction.ClusterWeight weightedCluster : action.weightedClusters()) {
                        accumulator += weightedCluster.weight();
                        if (select < accumulator) {
                            cluster = prefixedClusterName(weightedCluster.name());
                            selectedOverrideConfigs.putAll(weightedCluster.filterConfigOverrides());
                            break;
                        }
                    }
                } else if (action.namedClusterSpecifierPluginConfig() != null) {
                    cluster = prefixedClusterSpecifierPluginName(action.namedClusterSpecifierPluginConfig().name());
                }
            } while (!retainCluster(cluster));
            Long timeoutNanos = null;
            if (XdsNameResolver.enableTimeout) {
                if (selectedRoute != null) {
                    timeoutNanos = selectedRoute.routeAction().timeoutNano();
                }
                if (timeoutNanos == null) {
                    timeoutNanos = routingCfg.fallbackTimeoutNano;
                }
                if (timeoutNanos <= 0) {
                    timeoutNanos = null;
                }
            }
            VirtualHost.Route.RouteAction.RetryPolicy retryPolicy =
                    selectedRoute == null ? null : selectedRoute.routeAction().retryPolicy();
            // TODO(chengyuanzhang): avoid service config generation and parsing for each call.
            Map<String, ?> rawServiceConfig =
                    XdsNameResolver.generateServiceConfigWithMethodConfig(timeoutNanos, retryPolicy);
            ConfigOrError parsedServiceConfig = serviceConfigParser.parseServiceConfig(rawServiceConfig);
            Object config = parsedServiceConfig.getConfig();
            if (config == null) {
                releaseCluster(cluster);
                return Result.forError(
                        parsedServiceConfig.getError().augmentDescription(
                                "Failed to parse service config (method config)"));
            }
            if (routingCfg.filterChain != null) {
                for (Filter.NamedFilterConfig namedFilter : routingCfg.filterChain) {
                    Filter.FilterConfig filterConfig = namedFilter.filterConfig;
                    // TODO: support filter
                }
            }
            final long hash = generateHash(selectedRoute.routeAction().hashPolicies(), headers);
            return Result.newBuilder()
                    .setConfig(config)
                    .setCluster(cluster)
                    .setHash(hash)
                    .build();
        }

        private boolean retainCluster(String cluster) {
            // we don't need to use ref counting technicals for HTTP connection
            return true;
        }

        private void releaseCluster(final String cluster) {
            // do nothing
        }

        private long generateHash(List<VirtualHost.Route.RouteAction.HashPolicy> hashPolicies,
                                  Map<String, String> headers) {
            Long hash = null;
            for (VirtualHost.Route.RouteAction.HashPolicy policy : hashPolicies) {
                Long newHash = null;
                if (policy.type() == VirtualHost.Route.RouteAction.HashPolicy.Type.HEADER) {
                    String value = getHeaderValue(headers, policy.headerName());
                    if (value != null) {
                        if (policy.regEx() != null && policy.regExSubstitution() != null) {
                            value = policy.regEx().matcher(value).replaceAll(policy.regExSubstitution());
                        }
                        newHash = hashFunc.hashAsciiString(value);
                    }
                } else if (policy.type() == VirtualHost.Route.RouteAction.HashPolicy.Type.CHANNEL_ID) {
                    newHash = hashFunc.hashLong(randomChannelId);
                }
                if (newHash != null) {
                    // Rotating the old value prevents duplicate hash rules from cancelling each other out
                    // and preserves all of the entropy.
                    long oldHash = hash != null ? ((hash << 1L) | (hash >> 63L)) : 0;
                    hash = oldHash ^ newHash;
                }
                // If the policy is a terminal policy and a hash has been generated, ignore
                // the rest of the hash policies.
                if (policy.isTerminal() && hash != null) {
                    break;
                }
            }
            return hash == null ? random.nextLong() : hash;
        }
    }

    @VisibleForTesting
    static boolean matchRoute(VirtualHost.Route.RouteMatch routeMatch, String fullMethodName,
            /* headers is a map instead of a Metadata in gRPC */Map<String, String> headers, ThreadSafeRandom random) {
        if (!matchPath(routeMatch.pathMatcher(), fullMethodName)) {
            return false;
        }
        for (Matchers.HeaderMatcher headerMatcher : routeMatch.headerMatchers()) {
            if (!headerMatcher.matches(getHeaderValue(headers, headerMatcher.name()))) {
                return false;
            }
        }
        Matchers.FractionMatcher fraction = routeMatch.fractionMatcher();
        return fraction == null || random.nextInt(fraction.denominator()) < fraction.numerator();
    }

    private static boolean matchPath(VirtualHost.Route.RouteMatch.PathMatcher pathMatcher, String fullMethodName) {
        if (pathMatcher.path() != null) {
            return pathMatcher.caseSensitive()
                    ? pathMatcher.path().equals(fullMethodName)
                    : pathMatcher.path().equalsIgnoreCase(fullMethodName);
        } else if (pathMatcher.prefix() != null) {
            return pathMatcher.caseSensitive()
                    ? fullMethodName.startsWith(pathMatcher.prefix())
                    : fullMethodName.toLowerCase(Locale.US).startsWith(
                    pathMatcher.prefix().toLowerCase(Locale.US));
        }
        return pathMatcher.regEx().matches(fullMethodName);
    }

    @Nullable
    private static String getHeaderValue(Map<String, String> headers, String headerName) {
        // TODO: we may possibly intercept header values here
        return headers.get(headerName);
    }

    private static String prefixedClusterName(String name) {
        return "cluster:" + name;
    }

    private static String prefixedClusterSpecifierPluginName(String pluginName) {
        return "cluster_specifier_plugin:" + pluginName;
    }

    private static final class FailingConfigSelector extends ProxylessInternalConfigSelector {
        private final Result result;

        public FailingConfigSelector(Status error) {
            this.result = Result.forError(error);
        }

        @Override
        public Result selectConfig(LoadBalancer.PickServerArgs args) {
            return result;
        }
    }

    private class ResolveState implements XdsClient.ResourceWatcher<XdsListenerResource.LdsUpdate> {
        private final ConfigOrError emptyServiceConfig =
                serviceConfigParser.parseServiceConfig(Collections.emptyMap());
        private final ResolutionResult emptyResult =
                ResolutionResult.newBuilder()
                        .setServiceConfig(emptyServiceConfig)
                        // let channel take action for no config selector
                        .build();
        private final String ldsResourceName;
        private boolean stopped;
        @Nullable
        private Set<String> existingClusters;  // clusters to which new requests can be routed
        @Nullable
        private RouteDiscoveryState routeDiscoveryState;

        ResolveState(String ldsResourceName) {
            this.ldsResourceName = ldsResourceName;
        }

        @Override
        public void onChanged(final XdsListenerResource.LdsUpdate update) {
            syncContext.execute(new Runnable() {
                @Override
                public void run() {
                    if (stopped) {
                        return;
                    }
                    logger.debug("Receive LDS resource update: {}", update);
                    HttpConnectionManager httpConnectionManager = update.httpConnectionManager();
                    List<VirtualHost> virtualHosts = httpConnectionManager.virtualHosts();
                    String rdsName = httpConnectionManager.rdsName();
                    cleanUpRouteDiscoveryState();
                    if (virtualHosts != null) { // grpcgen returns an empty VHs list
                        updateRoutes(virtualHosts, httpConnectionManager.httpMaxStreamDurationNano(),
                                httpConnectionManager.httpFilterConfigs());
                    } else {
                        routeDiscoveryState = new RouteDiscoveryState(
                                rdsName, httpConnectionManager.httpMaxStreamDurationNano(),
                                httpConnectionManager.httpFilterConfigs());
                        // FIXME
                        // See: https://github.com/istio/istio/blob/1.13.4/pilot/pkg/networking/core/v1alpha3/httproute.go#L350
                        // For 80 port, istio will return all virtual hosts that listen on the port.
                        // So all dependent services subscribe to the same RDS name request, e.g. "outbound|80||<service>.<namespace>.svc.cluster.local"
                        final String port = ldsResourceName.substring()
                        rdsName = Strings.isNullOrEmpty(FALLBACK_RDS_RESOURCE_NAME) ? rdsName : "outbound|50051||" + FALLBACK_RDS_RESOURCE_NAME;
                        logger.debug("Start watching RDS resource {}", rdsName);
                        xdsClient.watchXdsResource(XdsRouteConfigureResource.getInstance(),
                                rdsName, routeDiscoveryState);
                    }
                }
            });
        }

        @Override
        public void onError(final Status error) {
            syncContext.execute(new Runnable() {
                @Override
                public void run() {
                    if (stopped || receivedConfig) {
                        return;
                    }
                    listener.onError(Status.UNAVAILABLE.withCause(error.getCause()).withDescription(
                            String.format("Unable to load LDS %s. xDS server returned: %s: %s",
                                    ldsResourceName, error.getCode(), error.getDescription())));
                }
            });
        }

        @Override
        public void onResourceDoesNotExist(final String resourceName) {
            syncContext.execute(new Runnable() {
                @Override
                public void run() {
                    if (stopped) {
                        return;
                    }
                    String error = "LDS resource does not exist: " + resourceName;
                    logger.debug(error);
                    cleanUpRouteDiscoveryState();
                    cleanUpRoutes(error);
                }
            });
        }

        private void start() {
            logger.debug("Start watching LDS resource {}", ldsResourceName);
            xdsClient.watchXdsResource(XdsListenerResource.getInstance(), ldsResourceName, this);
        }

        private void stop() {
            logger.debug("Stop watching LDS resource {}", ldsResourceName);
            stopped = true;
            cleanUpRouteDiscoveryState();
            xdsClient.cancelXdsResourceWatch(XdsListenerResource.getInstance(), ldsResourceName, this);
        }

        // called in syncContext
        private void updateRoutes(List<VirtualHost> virtualHosts, long httpMaxStreamDurationNano,
                                  @Nullable List<Filter.NamedFilterConfig> filterConfigs) {
            String authority = overrideAuthority != null ? overrideAuthority : ldsResourceName;
            VirtualHost virtualHost = RoutingUtils.findVirtualHostForHostName(virtualHosts, ldsResourceName);
            if (virtualHost == null) {
                String error = "Failed to find virtual host matching hostname: " + authority;
                logger.warn("{}", error);
                cleanUpRoutes(error);
                return;
            }

            List<VirtualHost.Route> routes = virtualHost.routes();

            // Populate all clusters to which requests can be routed to through the virtual host.
            Set<String> clusters = new HashSet<>();
            // uniqueName -> clusterName
            Map<String, String> clusterNameMap = new HashMap<>();
            // uniqueName -> pluginConfig
            Map<String, RouteLookupServiceClusterSpecifierPlugin.RlsPluginConfig> rlsPluginConfigMap = new HashMap<>();
            for (VirtualHost.Route route : routes) {
                VirtualHost.Route.RouteAction action = route.routeAction();
                String prefixedName;
                if (action != null) {
                    if (action.cluster() != null) {
                        prefixedName = prefixedClusterName(action.cluster());
                        clusters.add(prefixedName);
                        clusterNameMap.put(prefixedName, action.cluster());
                    } else if (action.weightedClusters() != null) {
                        for (VirtualHost.Route.RouteAction.ClusterWeight weighedCluster : action.weightedClusters()) {
                            prefixedName = prefixedClusterName(weighedCluster.name());
                            clusters.add(prefixedName);
                            clusterNameMap.put(prefixedName, weighedCluster.name());
                        }
                    } else if (action.namedClusterSpecifierPluginConfig() != null) {
                        ClusterSpecifierPlugin.PluginConfig pluginConfig = action.namedClusterSpecifierPluginConfig().config();
                        if (pluginConfig instanceof RouteLookupServiceClusterSpecifierPlugin.RlsPluginConfig) {
                            prefixedName = prefixedClusterSpecifierPluginName(
                                    action.namedClusterSpecifierPluginConfig().name());
                            clusters.add(prefixedName);
                            rlsPluginConfigMap.put(prefixedName, (RouteLookupServiceClusterSpecifierPlugin.RlsPluginConfig) pluginConfig);
                        }
                    }
                }
            }

            // Updates channel's load balancing config whenever the set of selectable clusters changes.
            boolean shouldUpdateResult = existingClusters == null;
            Set<String> addedClusters =
                    existingClusters == null ? clusters : Sets.difference(clusters, existingClusters);
            Set<String> deletedClusters =
                    existingClusters == null
                            ? Collections.<String>emptySet() : Sets.difference(existingClusters, clusters);
            existingClusters = clusters;
            for (String cluster : addedClusters) {
                if (clusterRefs.containsKey(cluster)) {
                    clusterRefs.get(cluster).refCount.incrementAndGet();
                } else {
                    if (clusterNameMap.containsKey(cluster)) {
                        clusterRefs.put(
                                cluster,
                                ClusterRefState.forCluster(new AtomicInteger(1), clusterNameMap.get(cluster)));
                    }
                    if (rlsPluginConfigMap.containsKey(cluster)) {
                        clusterRefs.put(
                                cluster,
                                ClusterRefState.forRlsPlugin(
                                        new AtomicInteger(1), rlsPluginConfigMap.get(cluster)));
                    }
                    shouldUpdateResult = true;
                }
            }
            for (String cluster : clusters) {
                RouteLookupServiceClusterSpecifierPlugin.RlsPluginConfig rlsPluginConfig = rlsPluginConfigMap.get(cluster);
                if (!Objects.equals(rlsPluginConfig, clusterRefs.get(cluster).rlsPluginConfig)) {
                    ClusterRefState newClusterRefState =
                            ClusterRefState.forRlsPlugin(clusterRefs.get(cluster).refCount, rlsPluginConfig);
                    clusterRefs.put(cluster, newClusterRefState);
                    shouldUpdateResult = true;
                }
            }
            // Update service config to include newly added clusters.
            if (shouldUpdateResult) {
                updateResolutionResult();
            }
            // Make newly added clusters selectable by config selector and deleted clusters no longer
            // selectable.
            routingConfig =
                    new RoutingConfig(
                            httpMaxStreamDurationNano, routes, filterConfigs,
                            virtualHost.filterConfigOverrides());
            shouldUpdateResult = false;
            for (String cluster : deletedClusters) {
                int count = clusterRefs.get(cluster).refCount.decrementAndGet();
                if (count == 0) {
                    clusterRefs.remove(cluster);
                    shouldUpdateResult = true;
                }
            }
            if (shouldUpdateResult) {
                updateResolutionResult();
            }
        }

        private void cleanUpRoutes(String error) {
            if (existingClusters != null) {
                for (String cluster : existingClusters) {
                    int count = clusterRefs.get(cluster).refCount.decrementAndGet();
                    if (count == 0) {
                        clusterRefs.remove(cluster);
                    }
                }
                existingClusters = null;
            }
            routingConfig = RoutingConfig.EMPTY;
            // Without addresses the default LB (normally pick_first) should become TRANSIENT_FAILURE, and
            // the config selector handles the error message itself. Once the LB API allows providing
            // failure information for addresses yet still providing a service config, the config selector
            // could be avoided.
            listener.onResult(ResolutionResult.newBuilder()
                    .setAttributes(Attributes.newBuilder()
                            .set(ProxylessInternalConfigSelector.KEY,
                                    new FailingConfigSelector(Status.UNAVAILABLE.withDescription(error)))
                            .build())
                    .setServiceConfig(emptyServiceConfig)
                    .build());
            receivedConfig = true;
        }

        private void cleanUpRouteDiscoveryState() {
            if (routeDiscoveryState != null) {
                String rdsName = routeDiscoveryState.resourceName;
                logger.debug("Stop watching RDS resource {}", rdsName);
                xdsClient.cancelXdsResourceWatch(XdsRouteConfigureResource.getInstance(), rdsName,
                        routeDiscoveryState);
                routeDiscoveryState = null;
            }
        }

        /**
         * Discovery state for RouteConfiguration resource. One instance for each Listener resource
         * update.
         */
        private class RouteDiscoveryState implements XdsClient.ResourceWatcher<XdsRouteConfigureResource.RdsUpdate> {
            private final String resourceName;
            private final long httpMaxStreamDurationNano;
            @Nullable
            private final List<Filter.NamedFilterConfig> filterConfigs;

            private RouteDiscoveryState(String resourceName, long httpMaxStreamDurationNano,
                                        @Nullable List<Filter.NamedFilterConfig> filterConfigs) {
                this.resourceName = resourceName;
                this.httpMaxStreamDurationNano = httpMaxStreamDurationNano;
                this.filterConfigs = filterConfigs;
            }

            @Override
            public void onChanged(final XdsRouteConfigureResource.RdsUpdate update) {
                syncContext.execute(new Runnable() {
                    @Override
                    public void run() {
                        if (RouteDiscoveryState.this != routeDiscoveryState) {
                            return;
                        }
                        logger.debug("Received RDS resource update: {}", update);
                        updateRoutes(update.virtualHosts, httpMaxStreamDurationNano, filterConfigs);
                    }
                });
            }

            @Override
            public void onError(final Status error) {
                syncContext.execute(new Runnable() {
                    @Override
                    public void run() {
                        if (RouteDiscoveryState.this != routeDiscoveryState || receivedConfig) {
                            return;
                        }
                        listener.onError(Status.UNAVAILABLE.withCause(error.getCause()).withDescription(
                                String.format("Unable to load RDS %s. xDS server returned: %s: %s",
                                        resourceName, error.getCode(), error.getDescription())));
                    }
                });
            }

            @Override
            public void onResourceDoesNotExist(final String resourceName) {
                syncContext.execute(new Runnable() {
                    @Override
                    public void run() {
                        if (RouteDiscoveryState.this != routeDiscoveryState) {
                            return;
                        }
                        String error = "RDS resource does not exist: " + resourceName;
                        logger.debug("{}", error);
                        cleanUpRoutes(error);
                    }
                });
            }
        }
    }

    /**
     * VirtualHost-level configuration for request routing.
     */
    private static class RoutingConfig {
        private final long fallbackTimeoutNano;
        final List<VirtualHost.Route> routes;
        // Null if HttpFilter is not supported.
        @Nullable
        final List<Filter.NamedFilterConfig> filterChain;
        final Map<String, Filter.FilterConfig> virtualHostOverrideConfig;

        private static final RoutingConfig EMPTY = new RoutingConfig(
                0L, Collections.<VirtualHost.Route>emptyList(), null, Collections.<String, Filter.FilterConfig>emptyMap());

        private RoutingConfig(
                long fallbackTimeoutNano, List<VirtualHost.Route> routes, @Nullable List<Filter.NamedFilterConfig> filterChain,
                Map<String, Filter.FilterConfig> virtualHostOverrideConfig) {
            this.fallbackTimeoutNano = fallbackTimeoutNano;
            this.routes = routes;
            checkArgument(filterChain == null || !filterChain.isEmpty(), "filterChain is empty");
            this.filterChain = filterChain == null ? null : Collections.unmodifiableList(filterChain);
            this.virtualHostOverrideConfig = Collections.unmodifiableMap(virtualHostOverrideConfig);
        }
    }

    private static class ClusterRefState {
        final AtomicInteger refCount;
        @Nullable
        final String traditionalCluster;
        @Nullable
        final RouteLookupServiceClusterSpecifierPlugin.RlsPluginConfig rlsPluginConfig;

        private ClusterRefState(
                AtomicInteger refCount, @Nullable String traditionalCluster,
                @Nullable RouteLookupServiceClusterSpecifierPlugin.RlsPluginConfig rlsPluginConfig) {
            this.refCount = refCount;
            checkArgument(traditionalCluster == null ^ rlsPluginConfig == null,
                    "There must be exactly one non-null value in traditionalCluster and pluginConfig");
            this.traditionalCluster = traditionalCluster;
            this.rlsPluginConfig = rlsPluginConfig;
        }

        private Map<String, ?> toLbPolicy() {
            if (traditionalCluster != null) {
                return ImmutableMap.of(
                        XdsLbPolicies.CDS_POLICY_NAME,
                        ImmutableMap.of("cluster", traditionalCluster));
            } else {
                ImmutableMap<String, ?> rlsConfig = new ImmutableMap.Builder<String, Object>()
                        .put("routeLookupConfig", rlsPluginConfig.config())
                        .put(
                                "childPolicy",
                                ImmutableList.of(ImmutableMap.of(XdsLbPolicies.CDS_POLICY_NAME, ImmutableMap.of())))
                        .put("childPolicyConfigTargetFieldName", "cluster")
                        .buildOrThrow();
                return ImmutableMap.of("rls_experimental", rlsConfig);
            }
        }

        static ClusterRefState forCluster(AtomicInteger refCount, String name) {
            return new ClusterRefState(refCount, name, null);
        }

        static ClusterRefState forRlsPlugin(AtomicInteger refCount, RouteLookupServiceClusterSpecifierPlugin.RlsPluginConfig rlsPluginConfig) {
            return new ClusterRefState(refCount, null, rlsPluginConfig);
        }
    }
}