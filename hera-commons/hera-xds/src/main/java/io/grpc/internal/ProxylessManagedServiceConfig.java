package io.grpc.internal;

import com.google.common.annotations.VisibleForTesting;
import io.grpc.xds.ProxylessInternalConfigSelector;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.annotation.Nullable;

@ToString
@EqualsAndHashCode
final class ProxylessManagedServiceConfig {
    @Nullable
    private final Object loadBalancingConfig;

    ProxylessManagedServiceConfig(@Nullable Object loadBalancingConfig) {
        this.loadBalancingConfig = loadBalancingConfig;
    }

    /**
     * Returns an empty {@link ProxylessManagedServiceConfig}.
     */
    static ProxylessManagedServiceConfig empty() {
        return new ProxylessManagedServiceConfig(null);
    }

    /**
     * Parses the Service level config values (e.g. excludes load balancing)
     */
    static ProxylessManagedServiceConfig fromServiceConfig(@Nullable Object loadBalancingConfig) {
        // TODO: parse other configuration
        return new ProxylessManagedServiceConfig(loadBalancingConfig);
    }

    /**
     * Used as a fallback per-RPC config supplier when the attributes value of {@link
     * ProxylessInternalConfigSelector#KEY} is not available. Returns {@code null} if there is no method
     * config in this service config.
     */
    @Nullable
    ProxylessInternalConfigSelector getDefaultConfigSelector() {
        return null;
    }

    @VisibleForTesting
    @Nullable
    Object getLoadBalancingConfig() {
        return loadBalancingConfig;
    }
}
