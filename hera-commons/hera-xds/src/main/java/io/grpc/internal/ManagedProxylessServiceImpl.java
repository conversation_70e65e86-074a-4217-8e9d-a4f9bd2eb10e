package io.grpc.internal;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import com.wosai.middleware.hera.rpc.LoadBalancer;
import com.wosai.middleware.hera.rpc.proxyless.ManagedProxylessService;
import com.wosai.middleware.hera.rpc.proxyless.ManagedServiceBuilder;
import com.wosai.middleware.hera.rpc.proxyless.ProxylessService;
import io.grpc.Attributes;
import io.grpc.CallOptions;
import io.grpc.ConnectivityState;
import io.grpc.EquivalentAddressGroup;
import io.grpc.InternalLogId;
import io.grpc.InternalWithLogId;
import io.grpc.NameResolver;
import io.grpc.ProxyDetector;
import io.grpc.ProxylessNameResolverRegistry;
import io.grpc.Status;
import io.grpc.SynchronizationContext;
import io.grpc.xds.ProxylessInternalConfigSelector;
import io.netty.channel.EventLoopGroup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.concurrent.ThreadSafe;
import java.net.InetSocketAddress;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkNotNull;
import static com.google.common.base.Preconditions.checkState;

@ThreadSafe
@Slf4j // work as channel logger in gRPC impl
public class ManagedProxylessServiceImpl extends ManagedProxylessService implements InternalWithLogId {
    // Matching this pattern means the target string is a URI target or at least intended to be one.
    // A URI target must be an absolute hierarchical URI.
    // From RFC 2396: scheme = alpha *( alpha | digit | "+" | "-" | "." )
    @VisibleForTesting
    static final Pattern URI_PATTERN = Pattern.compile("[a-zA-Z][a-zA-Z0-9+.-]*:/.*");
    private static final ProxylessManagedServiceConfig EMPTY_SERVICE_CONFIG =
            ProxylessManagedServiceConfig.empty();
    private static final ProxylessInternalConfigSelector INITIAL_PENDING_SELECTOR =
            new ProxylessInternalConfigSelector() {
                @Override
                public Result selectConfig(LoadBalancer.PickServerArgs args) {
                    throw new IllegalStateException("Resolution is pending");
                }
            };
    private final InternalLogId logId;
    private final String target;
    // service authority
    private final String authority;
    @Nullable
    private final String authorityOverride;
    private final ProxylessNameResolverRegistry nameResolverRegistry;
    private final NameResolver.Factory nameResolverFactory;
    private final NameResolver.Args nameResolverArgs;
    private final ProxylessAutoConfiguredLoadBalancerFactory loadBalancerFactory;
    private final ObjectPool<? extends EventLoopGroup> groupPool;
    private final EventLoopGroup group;
    private final RestrictedScheduledExecutor scheduledExecutor;
    private final Executor executor;
    private final ObjectPool<? extends Executor> executorPool;
    private final ExecutorHolder offloadExecutorHolder;
    private final FallbackServerPicker fallbackPicker;
    @VisibleForTesting
    final SynchronizationContext syncContext = new SynchronizationContext(
            new Thread.UncaughtExceptionHandler() {
                @Override
                public void uncaughtException(Thread t, Throwable e) {
                    log.error("[" + getLogId() + "] Uncaught exception in the SynchronizationContext. Panic!", e);
                    panic(e);
                }
            });
    private final BackoffPolicy.Provider backoffPolicyProvider;
    // Only null after channel is terminated. Must be assigned from the syncContext.
    private NameResolver nameResolver;

    // Must be accessed from the syncContext.
    private boolean nameResolverStarted;

    // null when channel is in idle mode.  Must be assigned from syncContext.
    @Nullable
    private LbHelperImpl lbHelper;
    @Nullable
    private volatile LoadBalancer.ServerPicker serverPicker;
    // Must be accessed from the syncContext
    private boolean panicMode;
    private final AtomicBoolean shutdown = new AtomicBoolean(false);
    // Must only be mutated and read from syncContext
    private boolean shutdownNowed;
    // Must only be mutated from syncContext
    private boolean terminating;
    // Must be mutated from syncContext
    private volatile boolean terminated;
    private final RealProxylessService realProxylessService;
    // Must be mutated and read from syncContext
    // a flag for doing channel tracing when flipped
    private ResolutionState lastResolutionState = ResolutionState.NO_RESOLUTION;
    private final CountDownLatch terminatedLatch = new CountDownLatch(1);
    private ProxylessManagedServiceConfig lastServiceConfig = EMPTY_SERVICE_CONFIG;
    @Nullable
    private final ProxylessManagedServiceConfig defaultServiceConfig;
    private boolean serviceConfigUpdated = false;
    private final boolean lookUpServiceConfig;
    // Temporary false flag that can skip the retry code path.
    private final boolean retryEnabled;

    public ManagedProxylessServiceImpl(
            ManagedServiceBuilder builder,
            BackoffPolicy.Provider backoffPolicyProvider) {
        this.target = checkNotNull(builder.target, "target");
        this.logId = InternalLogId.allocate("Channel", target);
        this.executorPool = checkNotNull(builder.executorPool, "executorPool");
        this.executor = checkNotNull(executorPool.getObject(), "executor");
        this.groupPool = checkNotNull(builder.getEventLoopGroupPool(), "eventLoopGroupPool");
        this.group = builder.getEventLoopGroupPool().getObject();
        this.scheduledExecutor = new RestrictedScheduledExecutor(this.group);
        ProxyDetector proxyDetector =
                builder.proxyDetector != null ? builder.proxyDetector : GrpcUtil.DEFAULT_PROXY_DETECTOR;
        this.retryEnabled = builder.retryEnabled;
        this.loadBalancerFactory = new ProxylessAutoConfiguredLoadBalancerFactory(builder.defaultLbPolicy);
        this.offloadExecutorHolder =
                new ExecutorHolder(
                        checkNotNull(builder.offloadExecutorPool, "offloadExecutorPool"));
        this.nameResolverRegistry = builder.nameResolverRegistry;
        ProxylessScParser serviceConfigParser =
                new ProxylessScParser(
                        retryEnabled,
                        builder.maxRetryAttempts,
                        builder.maxHedgedAttempts,
                        loadBalancerFactory);
        this.nameResolverArgs =
                NameResolver.Args.newBuilder()
                        .setDefaultPort(builder.getDefaultPort())
                        .setProxyDetector(proxyDetector)
                        .setSynchronizationContext(syncContext)
                        .setScheduledExecutorService(scheduledExecutor)
                        .setServiceConfigParser(serviceConfigParser)
                        .setOffloadExecutor(
                                // Avoid creating the offloadExecutor until it is first used
                                new Executor() {
                                    @Override
                                    public void execute(Runnable command) {
                                        offloadExecutorHolder.getExecutor().execute(command);
                                    }
                                })
                        .build();
        this.authorityOverride = builder.authorityOverride;
        this.nameResolverFactory = builder.nameResolverFactory;
        this.nameResolver = getNameResolver(
                target, authorityOverride, nameResolverFactory, nameResolverArgs);
        this.authority = nameResolver.getServiceAuthority();
        this.backoffPolicyProvider = backoffPolicyProvider;
        this.fallbackPicker = new FixedFallbackServerPicker(LoadBalancer.PickResult.withNoResult());
        if (builder.defaultServiceConfig != null) {
            NameResolver.ConfigOrError parsedDefaultServiceConfig =
                    serviceConfigParser.parseServiceConfig(builder.defaultServiceConfig);
            checkState(
                    parsedDefaultServiceConfig.getError() == null,
                    "Default config is invalid: %s",
                    parsedDefaultServiceConfig.getError());
            this.defaultServiceConfig =
                    (ProxylessManagedServiceConfig) parsedDefaultServiceConfig.getConfig();
            this.lastServiceConfig = this.defaultServiceConfig;
        } else {
            this.defaultServiceConfig = null;
        }
        this.lookUpServiceConfig = builder.lookUpServiceConfig;
        realProxylessService = new RealProxylessService(nameResolver.getServiceAuthority());
        if (!lookUpServiceConfig) {
            if (defaultServiceConfig != null) {
                log.info("Service config look-up disabled, using default service config");
            }
            serviceConfigUpdated = true;
        }
    }

    private static NameResolver getNameResolver(
            String target, NameResolver.Factory nameResolverFactory, NameResolver.Args nameResolverArgs) {
        // Finding a NameResolver. Try using the target string as the URI. If that fails, try prepending
        // "dns:///".
        URI targetUri = null;
        StringBuilder uriSyntaxErrors = new StringBuilder();
        try {
            targetUri = new URI(target);
            // For "localhost:8080" this would likely cause newNameResolver to return null, because
            // "localhost" is parsed as the scheme. Will fall into the next branch and try
            // "dns:///localhost:8080".
        } catch (URISyntaxException e) {
            // Can happen with ip addresses like "[::1]:1234" or 127.0.0.1:1234.
            uriSyntaxErrors.append(e.getMessage());
        }
        if (targetUri != null) {
            NameResolver resolver = nameResolverFactory.newNameResolver(targetUri, nameResolverArgs);
            if (resolver != null) {
                return resolver;
            }
            // "foo.googleapis.com:8080" cause resolver to be null, because "foo.googleapis.com" is an
            // unmapped scheme. Just fall through and will try "dns:///foo.googleapis.com:8080"
        }

        // If we reached here, the targetUri couldn't be used.
        if (!URI_PATTERN.matcher(target).matches()) {
            // It doesn't look like a URI target. Maybe it's an authority string. Try with the default
            // scheme from the factory.
            try {
                targetUri = new URI(nameResolverFactory.getDefaultScheme(), "", "/" + target, null);
            } catch (URISyntaxException e) {
                // Should not be possible.
                throw new IllegalArgumentException(e);
            }
            NameResolver resolver = nameResolverFactory.newNameResolver(targetUri, nameResolverArgs);
            if (resolver != null) {
                return resolver;
            }
        }
        throw new IllegalArgumentException(String.format(
                "cannot find a NameResolver for %s%s",
                target, uriSyntaxErrors.length() > 0 ? " (" + uriSyntaxErrors + ")" : ""));
    }

    @VisibleForTesting
    static NameResolver getNameResolver(
            String target, @Nullable final String overrideAuthority,
            NameResolver.Factory nameResolverFactory, NameResolver.Args nameResolverArgs) {
        NameResolver resolver = getNameResolver(target, nameResolverFactory, nameResolverArgs);
        if (overrideAuthority == null) {
            return resolver;
        }
        return new ForwardingNameResolver(resolver) {
            @Override
            public String getServiceAuthority() {
                return overrideAuthority;
            }
        };
    }

    /**
     * Make the channel exit idle mode, if it's in it.
     *
     * <p>Must be called from syncContext
     */
    @VisibleForTesting
    void exitIdleMode() {
        syncContext.throwIfNotInThisSynchronizationContext();
        if (shutdown.get() || panicMode) {
            return;
        }
        // TODO: cancel or reschedule timer?
        if (lbHelper != null) {
            return;
        }
        log.info("Exiting idle mode");
        LbHelperImpl lbHelper = new LbHelperImpl();
        lbHelper.lb = loadBalancerFactory.newLoadBalancer(lbHelper);
        // Delay setting lbHelper until fully initialized, since loadBalancerFactory is user code and
        // may throw. We don't want to confuse our state, even if we will enter panic mode.
        this.lbHelper = lbHelper;

        NameResolverListener listener = new NameResolverListener(lbHelper, nameResolver);
        nameResolver.start(listener);
        nameResolverStarted = true;
    }

    @Override
    public InternalLogId getLogId() {
        return logId;
    }

    @Override
    public String authority() {
        return authority;
    }

    @Override
    public ManagedProxylessService shutdown() {
        log.debug("shutdown() called");
        if (!shutdown.compareAndSet(false, true)) {
            return this;
        }

        class TransportTerminated implements Runnable {
            @Override
            public void run() {
                checkState(shutdown.get(), "Channel must have been shut down");
                terminating = true;
                shutdownNameResolverAndLoadBalancer(false);
                // No need to call channelStateManager since we are already in SHUTDOWN state.
                // Until LoadBalancer is shutdown, it may still create new subchannels.  We catch them
                // here.
                maybeTerminateChannel();
            }
        }

        syncContext.execute(new TransportTerminated());
        return this;
    }

    /**
     * Terminate the channel if termination conditions are met.
     */
    // Must be run from syncContext
    private void maybeTerminateChannel() {
        if (terminated) {
            return;
        }
        if (shutdown.get()) {
            log.info("Terminated");
            executorPool.returnObject(executor);
            offloadExecutorHolder.release();
            // release EventGroup
            this.groupPool.returnObject(this.group);

            terminated = true;
            terminatedLatch.countDown();
        }
    }

    @Override
    public boolean isShutdown() {
        return shutdown.get();
    }

    @Override
    public boolean isTerminated() {
        return terminated;
    }

    @Override
    public boolean awaitTermination(long timeout, TimeUnit unit) throws InterruptedException {
        return terminatedLatch.await(timeout, unit);
    }

    // Run from syncContext
    @VisibleForTesting
    class DelayedNameResolverRefresh implements Runnable {
        @Override
        public void run() {
            scheduledNameResolverRefresh = null;
            refreshNameResolution();
        }
    }

    // Must be used from syncContext
    @Nullable
    private SynchronizationContext.ScheduledHandle scheduledNameResolverRefresh;
    // The policy to control backoff between name resolution attempts. Non-null when an attempt is
    // scheduled. Must be used from syncContext
    @Nullable
    private BackoffPolicy nameResolverBackoffPolicy;

    // Must be run from syncContext
    private void cancelNameResolverBackoff() {
        syncContext.throwIfNotInThisSynchronizationContext();
        if (scheduledNameResolverRefresh != null) {
            scheduledNameResolverRefresh.cancel();
            scheduledNameResolverRefresh = null;
            nameResolverBackoffPolicy = null;
        }
    }

    @Override
    public void resetConnectBackoff() {
        final class ResetConnectBackoff implements Runnable {
            @Override
            public void run() {
                if (shutdown.get()) {
                    return;
                }
                if (scheduledNameResolverRefresh != null && scheduledNameResolverRefresh.isPending()) {
                    checkState(nameResolverStarted, "name resolver must be started");
                    refreshAndResetNameResolution();
                }
            }
        }

        syncContext.execute(new ResetConnectBackoff());
    }

    /**
     * Force name resolution refresh to happen immediately and reset refresh back-off. Must be run
     * from syncContext.
     */
    private void refreshAndResetNameResolution() {
        syncContext.throwIfNotInThisSynchronizationContext();
        cancelNameResolverBackoff();
        refreshNameResolution();
    }

    private void refreshNameResolution() {
        syncContext.throwIfNotInThisSynchronizationContext();
        if (nameResolverStarted) {
            nameResolver.refresh();
        }
    }

    // Must be called from syncContext
    private void shutdownNameResolverAndLoadBalancer(boolean channelIsActive) {
        syncContext.throwIfNotInThisSynchronizationContext();
        if (channelIsActive) {
            checkState(nameResolverStarted, "nameResolver is not started");
            checkState(lbHelper != null, "lbHelper is null");
        }
        if (nameResolver != null) {
            cancelNameResolverBackoff();
            nameResolver.shutdown();
            nameResolverStarted = false;
            if (channelIsActive) {
                nameResolver = getNameResolver(
                        target, authorityOverride, nameResolverFactory, nameResolverArgs);
            } else {
                nameResolver = null;
            }
        }
        if (lbHelper != null) {
            lbHelper.lb.shutdown();
            lbHelper = null;
        }
        serverPicker = null;
    }

    // Called from syncContext
    @VisibleForTesting
    void panic(final Throwable t) {
        if (panicMode) {
            // Preserve the first panic information
            return;
        }
        panicMode = true;
        shutdownNameResolverAndLoadBalancer(false);
        final class PanicSubchannelPicker extends LoadBalancer.ServerPicker {
            private final LoadBalancer.PickResult panicPickResult =
                    LoadBalancer.PickResult.withDrop(
                            Status.INTERNAL.withDescription("Panic! This is a bug!").withCause(t));

            @Override
            public LoadBalancer.PickResult pickServer(LoadBalancer.PickServerArgs args) {
                return panicPickResult;
            }

            @Override
            public String toString() {
                return MoreObjects.toStringHelper(PanicSubchannelPicker.class)
                        .add("panicPickResult", panicPickResult)
                        .toString();
            }
        }

        updateServerPicker(new PanicSubchannelPicker());
        log.error("PANIC! Entering TRANSIENT_FAILURE");
    }

    @Override
    public LoadBalancer.PickResult selectServer(LoadBalancer.PickServerArgs args) {
        return realProxylessService.selectServer(args);
    }

    @Override
    public List<LoadBalancer.Server> getServers() {
        return realProxylessService.getServers();
    }

    private class RealProxylessService extends ProxylessService {
        // Reference to null if no config selector is available from resolution result
        // Reference must be set() from syncContext
        private final AtomicReference<ProxylessInternalConfigSelector> configSelector =
                new AtomicReference<>(INITIAL_PENDING_SELECTOR);
        // Set when the NameResolver is initially created. When we create a new NameResolver for the
        // same target, the new instance must have the same value.
        private final String authority;

        private final Runnable exitIdleRunner = new Runnable() {
            @Override
            public void run() {
                exitIdleMode();
            }
        };

        private RealProxylessService(String authority) {
            this.authority = checkNotNull(authority, "authority");
        }

        @Override
        public String authority() {
            return this.authority;
        }

        @Override
        public LoadBalancer.PickResult selectServer(LoadBalancer.PickServerArgs args) {
            if (configSelector.get() != INITIAL_PENDING_SELECTOR) {
                if (shutdown.get()) {
                    // If channel is shut down, delayedTransport is also shut down which will fail the stream
                    // properly.
                    return fallbackPicker.pickServer(args);
                }

                if (configSelector.get() == null) {
                    return fallbackPicker.pickServer(args);
                }

                final LoadBalancer.ServerPicker copy = serverPicker;
                if (copy == null) {
                    syncContext.execute(exitIdleRunner);
                    return fallbackPicker.pickServer(args);
                }
                // select a cluster with the given configSelector
                ProxylessInternalConfigSelector.Result selectedConfig = configSelector.get().selectConfig(args);
                if (selectedConfig.getStatus().isOk()) {
                    CallOptions newOptions = args.getCallOptions().withOption(ProxylessInternalConfigSelector.CLUSTER_SELECTION_KEY, selectedConfig.getCluster());
                    return copy.pickServer(args.withNewCallOptions(newOptions));
                } else {
                    // return error
                    return LoadBalancer.PickResult.withError(selectedConfig.getStatus());
                }
            }
            syncContext.execute(exitIdleRunner);

            // either not ready or already shutdown
            // TODO: any other strategy
            return fallbackPicker.pickServer(args);
        }

        @Override
        public List<LoadBalancer.Server> getServers() {
            final LoadBalancer.ServerPicker copy = serverPicker;
            if (copy == null) {
                return Collections.emptyList();
            }
            return copy.getServers();
        }

        // Must run in SynchronizationContext.
        void updateConfigSelector(@Nullable ProxylessInternalConfigSelector config) {
            configSelector.set(config);
        }

        // Must run in SynchronizationContext.
        void onConfigError() {
            if (configSelector.get() == INITIAL_PENDING_SELECTOR) {
                updateConfigSelector(null);
            }
        }

        void shutdown() {
            final class RealChannelShutdown implements Runnable {
                @Override
                public void run() {
                    if (configSelector.get() == INITIAL_PENDING_SELECTOR) {
                        configSelector.set(null);
                    }
                }
            }

            syncContext.execute(new RealChannelShutdown());
        }
    }

    private class NameResolverListener extends NameResolver.Listener2 {
        final LbHelperImpl helper;
        final NameResolver resolver;

        public NameResolverListener(LbHelperImpl helperImpl, NameResolver resolver) {
            this.helper = checkNotNull(helperImpl, "helperImpl");
            this.resolver = checkNotNull(resolver, "resolver");
        }

        @Override
        public void onResult(final NameResolver.ResolutionResult resolutionResult) {
            final class NamesResolved implements Runnable {
                @Override
                public void run() {
                    List<EquivalentAddressGroup> servers = resolutionResult.getAddresses();
                    log.trace("Resolved address: {}, config={}",
                            servers, resolutionResult.getAttributes());

                    if (lastResolutionState != ResolutionState.SUCCESS) {
                        log.info("Address resolved: {}", servers);
                        lastResolutionState = ResolutionState.SUCCESS;
                    }

                    nameResolverBackoffPolicy = null;
                    NameResolver.ConfigOrError configOrError = resolutionResult.getServiceConfig();
                    if (configOrError == null || configOrError.getError() != null || configOrError.getConfig() == null) {
                        log.error("fail to parse config with service config");
                        return;
                    }
                    ProxylessInternalConfigSelector resolvedConfigSelector =
                            resolutionResult.getAttributes().get(ProxylessInternalConfigSelector.KEY);
                    ProxylessManagedServiceConfig validServiceConfig =
                            configOrError != null && configOrError.getConfig() != null
                                    ? (ProxylessManagedServiceConfig) configOrError.getConfig()
                                    : null;
                    Status serviceConfigError = configOrError != null ? configOrError.getError() : null;

                    ProxylessManagedServiceConfig effectiveServiceConfig;
                    if (!lookUpServiceConfig) {
                        if (validServiceConfig != null) {
                            log.info("Service config from name resolver discarded by channel settings");
                        }
                        effectiveServiceConfig =
                                defaultServiceConfig == null ? EMPTY_SERVICE_CONFIG : defaultServiceConfig;
                        if (resolvedConfigSelector != null) {
                            log.info("Config selector from name resolver discarded by channel settings");
                        }
                        realProxylessService.updateConfigSelector(effectiveServiceConfig.getDefaultConfigSelector());
                    } else {
                        // Try to use config if returned from name resolver
                        // Otherwise, try to use the default config if available
                        if (validServiceConfig != null) {
                            effectiveServiceConfig = validServiceConfig;
                            if (resolvedConfigSelector != null) {
                                realProxylessService.updateConfigSelector(resolvedConfigSelector);
                                if (effectiveServiceConfig.getDefaultConfigSelector() != null) {
                                    log.trace("Method configs in service config will be discarded due to presence of"
                                            + "config-selector");
                                }
                            } else {
                                realProxylessService.updateConfigSelector(effectiveServiceConfig.getDefaultConfigSelector());
                            }
                        } else if (defaultServiceConfig != null) {
                            effectiveServiceConfig = defaultServiceConfig;
                            realProxylessService.updateConfigSelector(effectiveServiceConfig.getDefaultConfigSelector());
                            log.info("Received no service config, using default service config");
                        } else if (serviceConfigError != null) {
                            if (!serviceConfigUpdated) {
                                // First DNS lookup has invalid service config, and cannot fall back to default
                                log.info("Fallback to error due to invalid first service config without default config");
                                onError(configOrError.getError());
                                return;
                            } else {
                                effectiveServiceConfig = lastServiceConfig;
                            }
                        } else {
                            effectiveServiceConfig = EMPTY_SERVICE_CONFIG;
                            realProxylessService.updateConfigSelector(null);
                        }
                        if (!effectiveServiceConfig.equals(lastServiceConfig)) {
                            log.info("Service config changed{}",
                                    effectiveServiceConfig == EMPTY_SERVICE_CONFIG ? " to empty" : "");
                            lastServiceConfig = effectiveServiceConfig;
                        }

                        try {
                            // TODO(creamsoup): when `servers` is empty and lastResolutionStateCopy == SUCCESS
                            //  and lbNeedAddress, it shouldn't call the handleServiceConfigUpdate. But,
                            //  lbNeedAddress is not deterministic
                            serviceConfigUpdated = true;
                        } catch (RuntimeException re) {
                            log.warn("[" + getLogId() + "] Unexpected exception from parsing service config", re);
                        }
                    }

                    Attributes effectiveAttrs = resolutionResult.getAttributes();
                    // Call LB only if it's not shutdown. If LB is shutdown, lbHelper won't match.
                    if (NameResolverListener.this.helper == ManagedProxylessServiceImpl.this.lbHelper) {
                        Attributes.Builder attrBuilder =
                                effectiveAttrs.toBuilder().discard(ProxylessInternalConfigSelector.KEY);
//                        Map<String, ?> healthCheckingConfig =
//                                effectiveServiceConfig.getHealthCheckingConfig();
//                        if (healthCheckingConfig != null) {
//                            attrBuilder
//                                    .set(io.grpc.LoadBalancer.ATTR_HEALTH_CHECKING_CONFIG, healthCheckingConfig)
//                                    .build();
//                        }

                        boolean addressesAccepted = helper.lb.tryAcceptResolvedAddresses(
                                io.grpc.LoadBalancer.ResolvedAddresses.newBuilder()
                                        .setAddresses(servers)
                                        .setAttributes(attrBuilder.build())
                                        .setLoadBalancingPolicyConfig(effectiveServiceConfig.getLoadBalancingConfig())
                                        .build());

                        if (!addressesAccepted) {
                            // TODO: we call handleErrorInSyncContext previously, check if it is correct now?
                            scheduleExponentialBackOffInSyncContext();
                        }
                    }
                }
            }

            syncContext.execute(new NamesResolved());
        }

        @Override
        public void onError(final Status error) {
            checkArgument(!error.isOk(), "the error status must not be OK");
            final class NameResolverErrorHandler implements Runnable {
                @Override
                public void run() {
                    handleErrorInSyncContext(error);
                }
            }

            syncContext.execute(new NameResolverErrorHandler());
        }

        private void handleErrorInSyncContext(Status error) {
            log.warn("[{}] Failed to resolve name. status={}", getLogId(), error);
            realProxylessService.onConfigError();
            if (lastResolutionState != ResolutionState.ERROR) {
                log.warn("Failed to resolve name: {}", error);
                lastResolutionState = ResolutionState.ERROR;
            }
            // Call LB only if it's not shutdown.  If LB is shutdown, lbHelper won't match.
            if (NameResolverListener.this.helper != ManagedProxylessServiceImpl.this.lbHelper) {
                return;
            }

            helper.lb.handleNameResolutionError(error);

            scheduleExponentialBackOffInSyncContext();
        }

        private void scheduleExponentialBackOffInSyncContext() {
            if (scheduledNameResolverRefresh != null && scheduledNameResolverRefresh.isPending()) {
                // The name resolver may invoke onError multiple times, but we only want to
                // schedule one backoff attempt
                // TODO(ericgribkoff) Update contract of NameResolver.Listener or decide if we
                // want to reset the backoff interval upon repeated onError() calls
                return;
            }
            if (nameResolverBackoffPolicy == null) {
                nameResolverBackoffPolicy = backoffPolicyProvider.get();
            }
            long delayNanos = nameResolverBackoffPolicy.nextBackoffNanos();
            log.debug("Scheduling DNS resolution backoff for {} ns", delayNanos);
            scheduledNameResolverRefresh =
                    syncContext.schedule(
                            new DelayedNameResolverRefresh(), delayNanos, TimeUnit.NANOSECONDS, group);
        }
    }

    // Called from syncContext
    private void updateServerPicker(LoadBalancer.ServerPicker newPicker) {
        serverPicker = newPicker;
    }

    @Override
    public ManagedProxylessServiceImpl shutdownNow() {
        log.debug("shutdownNow() called");
        shutdown();
        final class ShutdownNow implements Runnable {
            @Override
            public void run() {
                if (shutdownNowed) {
                    return;
                }
                shutdownNowed = true;
            }
        }

        syncContext.execute(new ShutdownNow());
        return this;
    }

    private final class LbHelperImpl extends LoadBalancer.Helper {
        ProxylessAutoConfiguredLoadBalancerFactory.AutoConfiguredLoadBalancer lb;

        @Override
        public LoadBalancer.Server createServer(io.grpc.LoadBalancer.CreateSubchannelArgs args) {
            return LoadBalancer.Server.createHttpFromSocketAddress((InetSocketAddress) args.getAddresses().get(0).getAddresses().get(0));
        }

        @Override
        public String getAuthority() {
            return ManagedProxylessServiceImpl.this.authority;
        }

        @Override
        public SynchronizationContext getSynchronizationContext() {
            return syncContext;
        }

        @Override
        public ScheduledExecutorService getScheduledExecutorService() {
            return scheduledExecutor;
        }

        @Override
        public ProxylessNameResolverRegistry getNameResolverRegistry() {
            return nameResolverRegistry;
        }

        @Override
        public NameResolver.Args getNameResolverArgs() {
            return nameResolverArgs;
        }

        @Override
        public void updateBalancingState(@Nonnull ConnectivityState newState, @Nonnull LoadBalancer.ServerPicker newPicker) {
            syncContext.throwIfNotInThisSynchronizationContext();
            checkNotNull(newState, "newState");
            checkNotNull(newPicker, "newPicker");
            final class UpdateBalancingState implements Runnable {
                @Override
                public void run() {
                    if (LbHelperImpl.this != lbHelper) {
                        return;
                    }
                    updateServerPicker(newPicker);
                    if (newState == ConnectivityState.READY) {
                        // 触发setReady，这会自动调用onReady回调
                        setReady(true);
                        notifyWatchers(newPicker.getServers());
                    }
                }
            }

            syncContext.execute(new UpdateBalancingState());
        }
    }

    private static final class ExecutorHolder {
        private final ObjectPool<? extends Executor> pool;
        private Executor executor;

        ExecutorHolder(ObjectPool<? extends Executor> executorPool) {
            this.pool = checkNotNull(executorPool, "executorPool");
        }

        synchronized Executor getExecutor() {
            if (executor == null) {
                executor = checkNotNull(pool.getObject(), "%s.getObject()", executor);
            }
            return executor;
        }

        synchronized void release() {
            if (executor != null) {
                executor = pool.returnObject(executor);
            }
        }
    }

    public abstract static class FallbackServerPicker extends LoadBalancer.ServerPicker {
    }

    @RequiredArgsConstructor
    public static class FixedFallbackServerPicker extends FallbackServerPicker {
        final LoadBalancer.PickResult identity;

        @Override
        public LoadBalancer.PickResult pickServer(LoadBalancer.PickServerArgs args) {
            return identity;
        }
    }

    /**
     * A ResolutionState indicates the status of last name resolution.
     */
    enum ResolutionState {
        NO_RESOLUTION,
        SUCCESS,
        ERROR
    }

    private static final class RestrictedScheduledExecutor implements ScheduledExecutorService {
        final ScheduledExecutorService delegate;

        private RestrictedScheduledExecutor(ScheduledExecutorService delegate) {
            this.delegate = checkNotNull(delegate, "delegate");
        }

        @Override
        public <V> ScheduledFuture<V> schedule(Callable<V> callable, long delay, TimeUnit unit) {
            return delegate.schedule(callable, delay, unit);
        }

        @Override
        public ScheduledFuture<?> schedule(Runnable cmd, long delay, TimeUnit unit) {
            return delegate.schedule(cmd, delay, unit);
        }

        @Override
        public ScheduledFuture<?> scheduleAtFixedRate(
                Runnable command, long initialDelay, long period, TimeUnit unit) {
            return delegate.scheduleAtFixedRate(command, initialDelay, period, unit);
        }

        @Override
        public ScheduledFuture<?> scheduleWithFixedDelay(
                Runnable command, long initialDelay, long delay, TimeUnit unit) {
            return delegate.scheduleWithFixedDelay(command, initialDelay, delay, unit);
        }

        @Override
        public boolean awaitTermination(long timeout, TimeUnit unit)
                throws InterruptedException {
            return delegate.awaitTermination(timeout, unit);
        }

        @Override
        public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks)
                throws InterruptedException {
            return delegate.invokeAll(tasks);
        }

        @Override
        public <T> List<Future<T>> invokeAll(
                Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit)
                throws InterruptedException {
            return delegate.invokeAll(tasks, timeout, unit);
        }

        @Override
        public <T> T invokeAny(Collection<? extends Callable<T>> tasks)
                throws InterruptedException, ExecutionException {
            return delegate.invokeAny(tasks);
        }

        @Override
        public <T> T invokeAny(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
            return delegate.invokeAny(tasks, timeout, unit);
        }

        @Override
        public boolean isShutdown() {
            return delegate.isShutdown();
        }

        @Override
        public boolean isTerminated() {
            return delegate.isTerminated();
        }

        @Override
        public void shutdown() {
            throw new UnsupportedOperationException("Restricted: shutdown() is not allowed");
        }

        @Override
        public List<Runnable> shutdownNow() {
            throw new UnsupportedOperationException("Restricted: shutdownNow() is not allowed");
        }

        @Override
        public <T> Future<T> submit(Callable<T> task) {
            return delegate.submit(task);
        }

        @Override
        public Future<?> submit(Runnable task) {
            return delegate.submit(task);
        }

        @Override
        public <T> Future<T> submit(Runnable task, T result) {
            return delegate.submit(task, result);
        }

        @Override
        public void execute(Runnable command) {
            delegate.execute(command);
        }
    }
}
