package com.wosai.middleware.hera.rpc;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

@Slf4j
@NoArgsConstructor
public class LoadBalancerRegistry {
    private static LoadBalancerRegistry INSTANCE;
    private static final Iterable<Class<?>> HARDCODED_CLASSES = getHardCodedClasses();
    private final LinkedHashSet<LoadBalancerProvider> allProviders = new LinkedHashSet<>();
    private final LinkedHashMap<String, LoadBalancerProvider> effectiveProviders = new LinkedHashMap<>();

    public synchronized void register(LoadBalancerProvider provider) {
        this.addProvider(provider);
        this.refreshProviderMap();
    }

    private synchronized void addProvider(LoadBalancerProvider provider) {
        Preconditions.checkArgument(provider.isAvailable(), "isAvailable() returned false");
        this.allProviders.add(provider);
    }

    public synchronized void deregister(LoadBalancerProvider provider) {
        this.allProviders.remove(provider);
        this.refreshProviderMap();
    }

    private synchronized void refreshProviderMap() {
        effectiveProviders.clear();
        for (LoadBalancerProvider provider : allProviders) {
            String policy = provider.getPolicyName();
            LoadBalancerProvider existing = effectiveProviders.get(policy);
            if (existing == null || existing.getPriority() < provider.getPriority()) {
                effectiveProviders.put(policy, provider);
            }
        }
    }

    /**
     * Returns the default registry that loads providers via the Java service loader mechanism.
     */
    public static synchronized LoadBalancerRegistry getDefaultRegistry() {
        if (INSTANCE == null) {
            List<LoadBalancerProvider> providerList = ServiceProviders.loadAll(
                    LoadBalancerProvider.class,
                    HARDCODED_CLASSES,
                    LoadBalancerProvider.class.getClassLoader(),
                    new LoadBalancerPriorityAccessor());
            INSTANCE = new LoadBalancerRegistry();
            for (LoadBalancerProvider provider : providerList) {
                log.trace("Service loader found " + provider);
                if (provider.isAvailable()) {
                    INSTANCE.addProvider(provider);
                }
            }
            INSTANCE.refreshProviderMap();
        }
        return INSTANCE;
    }

    @Nullable
    public synchronized LoadBalancerProvider getProvider(String policy) {
        return this.effectiveProviders.get(Preconditions.checkNotNull(policy, "policy"));
    }

    @VisibleForTesting
    synchronized Map<String, LoadBalancerProvider> providers() {
        return new LinkedHashMap<>(this.effectiveProviders);
    }

    @VisibleForTesting
    static List<Class<?>> getHardCodedClasses() {
        // Class.forName(String) is used to remove the need for ProGuard configuration. Note that
        // ProGuard does not detect usages of Class.forName(String, boolean, ClassLoader):
        // https://sourceforge.net/p/proguard/bugs/418/
        ArrayList<Class<?>> list = new ArrayList<>();
        try {
            list.add(Class.forName("io.grpc.internal.ProxylessPickFirstLoadBalancerProvider"));
        } catch (ClassNotFoundException e) {
            log.warn("Unable to find pick-first LoadBalancer", e);
        }
        return Collections.unmodifiableList(list);
    }

    private static final class LoadBalancerPriorityAccessor
            implements ServiceProviders.PriorityAccessor<LoadBalancerProvider> {

        LoadBalancerPriorityAccessor() {
        }

        @Override
        public boolean isAvailable(LoadBalancerProvider provider) {
            return provider.isAvailable();
        }

        @Override
        public int getPriority(LoadBalancerProvider provider) {
            return provider.getPriority();
        }
    }
}
