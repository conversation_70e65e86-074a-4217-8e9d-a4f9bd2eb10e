package com.wosai.middleware.hera.rpc.proxyless;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import io.grpc.NameResolver;
import io.grpc.ProxyDetector;
import io.grpc.ProxylessNameResolverRegistry;
import io.grpc.internal.ExponentialBackoffPolicy;
import io.grpc.internal.GrpcUtil;
import io.grpc.internal.ManagedProxylessServiceImpl;
import io.grpc.internal.ObjectPool;
import io.grpc.internal.SharedResourcePool;
import io.grpc.netty.GrpcNettyUtils;
import io.netty.channel.EventLoopGroup;

import javax.annotation.Nullable;
import java.util.Map;
import java.util.concurrent.Executor;

public final class ManagedServiceBuilder {
    private static final ObjectPool<? extends Executor> DEFAULT_EXECUTOR_POOL =
            SharedResourcePool.forResource(GrpcUtil.SHARED_CHANNEL_EXECUTOR);
    private static final ObjectPool<? extends EventLoopGroup> DEFAULT_EVENT_LOOP_GROUP_POOL =
            SharedResourcePool.forResource(GrpcNettyUtils.DEFAULT_WORKER_EVENT_LOOP_GROUP);
    // Access via getter, which may perform authority override as needed
    public ProxylessNameResolverRegistry nameResolverRegistry = ProxylessNameResolverRegistry.getDefaultRegistry();
    public NameResolver.Factory nameResolverFactory = nameResolverRegistry.asFactory();
    public ObjectPool<? extends Executor> executorPool = DEFAULT_EXECUTOR_POOL;
    public ObjectPool<? extends Executor> offloadExecutorPool = DEFAULT_EXECUTOR_POOL;
    @Nullable
    public Map<String, ?> defaultServiceConfig;
    public boolean lookUpServiceConfig = true;
    @Nullable
    public ProxyDetector proxyDetector;
    private boolean authorityCheckerDisabled;
    public final String target;
    public int maxRetryAttempts = 5;
    public int maxHedgedAttempts = 5;
    public boolean retryEnabled = true;
    @Nullable
    public String authorityOverride;
    public String defaultLbPolicy = GrpcUtil.DEFAULT_LB_POLICY;
    private ObjectPool<? extends EventLoopGroup> eventLoopGroupPool = DEFAULT_EVENT_LOOP_GROUP_POOL;

    public static ManagedServiceBuilder forAddress(String name, int port) {
        return new ManagedServiceBuilder(name + ":" + port);
    }

    public static ManagedServiceBuilder forTarget(String name) {
        return new ManagedServiceBuilder(name);
    }

    ManagedServiceBuilder(String target) {
        this.target = Preconditions.checkNotNull(target, "target");
    }

    public ManagedServiceBuilder defaultLoadBalancingPolicy(String policy) {
        Preconditions.checkArgument(policy != null, "policy cannot be null");
        this.defaultLbPolicy = policy;
        return this;
    }

    public ManagedServiceBuilder overrideAuthority(String authority) {
        this.authorityOverride = checkAuthority(authority);
        return this;
    }

    public ManagedServiceBuilder disableServiceConfigLookUp() {
        this.lookUpServiceConfig = false;
        return this;
    }

    /**
     * Verifies the authority is valid.
     */
    @VisibleForTesting
    String checkAuthority(String authority) {
        if (authorityCheckerDisabled) {
            return authority;
        }
        return GrpcUtil.checkAuthority(authority);
    }

    /**
     * Disable the check whether the authority is valid.
     */
    public ManagedServiceBuilder disableCheckAuthority() {
        authorityCheckerDisabled = true;
        return this;
    }

    /**
     * Enable previously disabled authority check.
     */
    public ManagedServiceBuilder enableCheckAuthority() {
        authorityCheckerDisabled = false;
        return this;
    }

    /**
     * Returns a default port to {@link NameResolver} for use in cases where the target string doesn't
     * include a port. The default implementation returns {@link GrpcUtil#DEFAULT_PORT_SSL}.
     */
    public int getDefaultPort() {
        return 80;
    }

    public ObjectPool<? extends EventLoopGroup> getEventLoopGroupPool() {
        return eventLoopGroupPool;
    }

    public ManagedProxylessService build() {
        return new ManagedProxylessServiceImpl(this, new ExponentialBackoffPolicy.Provider());
    }
}
