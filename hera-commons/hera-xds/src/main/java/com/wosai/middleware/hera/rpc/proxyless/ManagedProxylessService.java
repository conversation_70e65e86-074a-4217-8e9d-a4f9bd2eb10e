package com.wosai.middleware.hera.rpc.proxyless;

import com.wosai.middleware.hera.rpc.LoadBalancer;
import com.wosai.middleware.hera.rpc.ServerWatcher;

import javax.annotation.concurrent.ThreadSafe;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

@ThreadSafe
public abstract class ManagedProxylessService extends ProxylessService {

    private final List<ServerWatcher> watchers = new CopyOnWriteArrayList<>();

    public abstract ManagedProxylessService shutdown();

    public abstract boolean isShutdown();

    public abstract boolean isTerminated();

    public abstract ManagedProxylessService shutdownNow();

    public abstract boolean awaitTermination(long timeout, TimeUnit unit) throws InterruptedException;

    public boolean awaitReady(LoadBalancer.PickServerArgs args, long timeout, long pollInterval, TimeUnit unit) throws InterruptedException {
        long remainingMillis = unit.toMillis(timeout);
        while (remainingMillis > 0) {
            long startTimeMillis = System.currentTimeMillis();
            final LoadBalancer.PickResult pickResult = selectServer(args);
            LoadBalancer.Server avail = null;
            if (pickResult != null && pickResult.getStatus().isOk()) {
                avail = pickResult.getServer();
            }
            if (avail != null) {
                return setReady(true);
            }
            unit.sleep(pollInterval);
            remainingMillis = remainingMillis - (System.currentTimeMillis() - startTimeMillis);
        }
        return false;
    }

    public void resetConnectBackoff() {
    }

    public void addWatcher(ServerWatcher watcher) {
        watchers.add(watcher);
    }

    protected void notifyWatchers(List<LoadBalancer.Server> servers) {
        for (ServerWatcher watcher : watchers) {
            watcher.onServersUpdated(servers);
        }
    }

    public boolean setReady(boolean ready) {
        this.ready = ready;
        return this.ready;
    }

    public boolean isReady() {
        return ready;
    }

    private volatile boolean ready = false;
}
