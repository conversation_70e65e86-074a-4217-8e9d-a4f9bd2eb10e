package com.wosai.middleware.hera.rpc;

public abstract class ForwardingServer extends LoadBalancer.Server {
    /**
     * Returns the underlying Server.
     */
    protected abstract LoadBalancer.Server delegate();

    @Override
    public String scheme() {
        return delegate().scheme();
    }

    @Override
    public String addr() {
        return delegate().addr();
    }

    @Override
    public int port() {
        return delegate().port();
    }

    @Override
    protected Builder toBuilder() {
        return delegate().toBuilder();
    }
}
