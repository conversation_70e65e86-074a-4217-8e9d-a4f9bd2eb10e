package com.wosai.middleware.hera.rpc;

import com.google.common.collect.ImmutableMap;
import com.wosai.middleware.hera.rpc.proxyless.ManagedProxylessService;
import com.wosai.middleware.hera.rpc.proxyless.ManagedServiceBuilder;
import io.grpc.CallOptions;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;

@Slf4j
public class App {
    public static void main(String[] args) throws Exception {
        ManagedProxylessService managedProxylessService = ManagedServiceBuilder
                .forTarget("xds:///finance-account.sqb.svc.cluster.local:80")
                .build();
//        managedProxylessService.awaitReady(
//                LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap()),
//                10_000, 1_000, TimeUnit.MILLISECONDS);

        ManagedProxylessService managedProxylessService2 = ManagedServiceBuilder
                .forTarget("xds:///service-governance-demo.sqb.svc.cluster.local:80")
                .build();
//        managedProxylessService2.awaitReady(
//                LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap()),
//                10_000, 1_000, TimeUnit.MILLISECONDS);

        ManagedProxylessService managedProxylessService3 = ManagedServiceBuilder
                .forTarget("xds:///sp-workflow-service.sqb.svc.cluster.local:80")
                .build();
//        managedProxylessService3.awaitReady(
//                LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap()),
//                10_000, 1_000, TimeUnit.MILLISECONDS);
//
        ManagedProxylessService managedProxylessService4 = ManagedServiceBuilder
                .forTarget("xds:///core-business.sqb.svc.cluster.local:80")
                .build();

        ManagedProxylessService managedProxylessService5 = ManagedServiceBuilder
                .forTarget("xds:///sp-task.sqb.svc.cluster.local:80")
                .build();

        ManagedProxylessService managedProxylessService6 = ManagedServiceBuilder
                .forTarget("xds:///sales-system-next.sqb.svc.cluster.local:80")
                .build();

        ManagedProxylessService managedProxylessService7 = ManagedServiceBuilder
                .forTarget("xds:///finance-backend.sqb.svc.cluster.local:80")
                .build();

        ManagedProxylessService managedProxylessService8 = ManagedServiceBuilder
                .forTarget("xds:///finance-lakala.sqb.svc.cluster.local:80")
                .build();
//        managedProxylessService4.awaitReady(
//                LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap()),
//                10_000, 1_000, TimeUnit.MILLISECONDS);
//        ManagedProxylessService managedProxylessService = ManagedServiceBuilder
//                .forTarget("xds:///details.default.svc.cluster.local:9080")
//                .build();
//        managedProxylessService.awaitReady(
//                LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap()),
//                10_000, 1_000, TimeUnit.MILLISECONDS);
//
//        ManagedProxylessService managedProxylessService2 = ManagedServiceBuilder
//                .forTarget("xds:///productpage.default.svc.cluster.local:9080")
//                .build();
//        managedProxylessService2.awaitReady(
//                LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap()),
//                10_000, 1_000, TimeUnit.MILLISECONDS);
//        ManagedProxylessService managedProxylessService3 = ManagedServiceBuilder
//                .forTarget("xds:///ratings.default.svc.cluster.local:9080")
//                .build();
//        managedProxylessService3.awaitReady(
//                LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap()),
//                10_000, 1_000, TimeUnit.MILLISECONDS);
//
//        ManagedProxylessService managedProxylessService4 = ManagedServiceBuilder
//                .forTarget("xds:///reviews.default.svc.cluster.local:9080")
//                .build();
//        managedProxylessService4.awaitReady(
//                LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap()),
//                10_000, 1_000, TimeUnit.MILLISECONDS);

        while (true) {
            Thread.sleep(3_000L);
            LoadBalancer.PickResult result = managedProxylessService.selectServer(
                    LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap(), CallOptions.DEFAULT)
            );
            log.info("result = {}", result);

            result = managedProxylessService2.selectServer(
                    LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap(), CallOptions.DEFAULT)
            );
            log.info("result = {}", result);

            result = managedProxylessService3.selectServer(
                    LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap(), CallOptions.DEFAULT)
            );
            log.info("result = {}", result);

            result = managedProxylessService4.selectServer(
                    LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap(), CallOptions.DEFAULT)
            );
            log.info("result = {}", result);

            result = managedProxylessService5.selectServer(
                    LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap(), CallOptions.DEFAULT)
            );
            log.info("result = {}", result);

            result = managedProxylessService6.selectServer(
                    LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap(), CallOptions.DEFAULT)
            );
            log.info("result = {}", result);

            result = managedProxylessService7.selectServer(
                    LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap(), CallOptions.DEFAULT)
            );
            log.info("result = {}", result);

            result = managedProxylessService8.selectServer(
                    LoadBalancer.PickServerArgs.create("/", "rpc", ImmutableMap.of(/*"x-env-flag", "jacoco"*/), Collections.emptyMap(), CallOptions.DEFAULT)
            );
            log.info("result = {}", result);
        }
    }
}
