/*
 * Copyright 2020 The gRPC Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.grpc.xds;

import com.wosai.middleware.hera.rpc.LoadBalancer;
import com.wosai.middleware.hera.rpc.LoadBalancerProvider;
import com.wosai.middleware.hera.rpc.LoadBalancerRegistry;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import static com.google.common.truth.Truth.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Tests for {@link ProxylessClusterImplLoadBalancerProvider}.
 */
@RunWith(JUnit4.class)
public class ProxylessClusterImplLoadBalancerProviderTest {

    @Test
    public void provided() {
        LoadBalancerProvider provider =
                LoadBalancerRegistry.getDefaultRegistry().getProvider(
                        XdsLbPolicies.CLUSTER_IMPL_POLICY_NAME);
        assertThat(provider).isInstanceOf(ProxylessClusterImplLoadBalancerProvider.class);
    }

    @Test
    public void providesLoadBalancer() {
        LoadBalancer.Helper helper = mock(LoadBalancer.Helper.class);
        when(helper.getAuthority()).thenReturn("api.google.com");
        LoadBalancerProvider provider = new ProxylessClusterImplLoadBalancerProvider();
        LoadBalancer loadBalancer = provider.newLoadBalancer(helper);
        assertThat(loadBalancer).isInstanceOf(ProxylessClusterImplLoadBalancer.class);
    }
}
