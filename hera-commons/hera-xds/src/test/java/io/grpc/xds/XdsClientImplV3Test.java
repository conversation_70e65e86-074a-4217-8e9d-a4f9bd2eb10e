package io.grpc.xds;

import io.grpc.ChannelCredentials;
import io.grpc.InsecureChannelCredentials;
import io.grpc.ManagedChannel;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(JUnit4.class)
public class XdsClientImplV3Test {
    private static final ChannelCredentials CHANNEL_CREDENTIALS = InsecureChannelCredentials.create();
    @Mock
    private Bootstrapper.ServerInfo serverInfo;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testXdsChannelFactory() {
        when(serverInfo.target()).thenReturn("localhost:8080");
        when(serverInfo.channelCredentials()).thenReturn(CHANNEL_CREDENTIALS);
        assertThat(XdsClientImpl.XdsChannelFactory.DEFAULT_XDS_CHANNEL_FACTORY).isNotNull();
        ManagedChannel managedChannel = XdsClientImpl.XdsChannelFactory.DEFAULT_XDS_CHANNEL_FACTORY.create(serverInfo);
        assertThat(managedChannel).isNotNull().extracting("delegate.originalTransportFactory.maxMessageSize")
                .asInstanceOf(InstanceOfAssertFactories.INTEGER).isEqualTo(16 * 1024 * 1024);
    }
}
