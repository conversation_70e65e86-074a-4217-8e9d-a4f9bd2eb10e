package io.grpc.xds;

import com.google.common.collect.ImmutableMap;
import io.grpc.InternalServiceProviders;
import io.grpc.NameResolver;
import io.grpc.NameResolver.ServiceConfigParser;
import io.grpc.NameResolverProvider;
import io.grpc.NameResolverRegistry;
import io.grpc.SynchronizationContext;
import io.grpc.internal.FakeClock;
import io.grpc.internal.GrpcUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.net.URI;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static com.google.common.truth.Truth.assertThat;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.mock;

/**
 * Unit tests for {@link ProxylessXdsNameResolverProvider}.
 */
@RunWith(JUnit4.class)
public class ProxylessXdsNameResolverProviderTest {
    private final SynchronizationContext syncContext = new SynchronizationContext(
            new Thread.UncaughtExceptionHandler() {
                @Override
                public void uncaughtException(Thread t, Throwable e) {
                    throw new AssertionError(e);
                }
            });

    private final FakeClock fakeClock = new FakeClock();
    private final NameResolver.Args args = NameResolver.Args.newBuilder()
            .setDefaultPort(8080)
            .setProxyDetector(GrpcUtil.NOOP_PROXY_DETECTOR)
            .setSynchronizationContext(syncContext)
            .setServiceConfigParser(mock(ServiceConfigParser.class))
            .setScheduledExecutorService(fakeClock.getScheduledExecutorService())
            .build();

    private ProxylessXdsNameResolverProvider provider = new ProxylessXdsNameResolverProvider();

    @Test
    public void provided() {
        for (NameResolverProvider current
                : InternalServiceProviders.getCandidatesViaServiceLoader(
                NameResolverProvider.class, getClass().getClassLoader())) {
            if (current instanceof ProxylessXdsNameResolverProvider) {
                return;
            }
        }
        fail("XdsNameResolverProvider not registered");
    }

    @Test
    public void isAvailable() {
        assertThat(provider.isAvailable()).isTrue();
    }

    @Test
    public void newNameResolver() {
        assertThat(
                provider.newNameResolver(URI.create("xds://1.1.1.1/foo.googleapis.com"), args))
                .isInstanceOf(ProxylessXdsNameResolver.class);
        assertThat(
                provider.newNameResolver(URI.create("xds:///foo.googleapis.com"), args))
                .isInstanceOf(ProxylessXdsNameResolver.class);
        assertThat(
                provider.newNameResolver(URI.create("notxds://1.1.1.1/foo.googleapis.com"),
                        args))
                .isNull();
    }

    @Test
    public void validName_withAuthority() {
        ProxylessXdsNameResolver resolver =
                provider.newNameResolver(
                        URI.create("xds://trafficdirector.google.com/foo.googleapis.com"), args);
        assertThat(resolver).isNotNull();
        assertThat(resolver.getServiceAuthority()).isEqualTo("foo.googleapis.com");
    }

    @Test
    public void validName_noAuthority() {
        ProxylessXdsNameResolver resolver =
                provider.newNameResolver(URI.create("xds:///foo.googleapis.com"), args);
        assertThat(resolver).isNotNull();
        assertThat(resolver.getServiceAuthority()).isEqualTo("foo.googleapis.com");
    }

    @Test
    public void invalidName_hostnameContainsUnderscore() {
        URI uri = URI.create("xds:///foo_bar.googleapis.com");
        try {
            provider.newNameResolver(uri, args);
            fail("Expected IllegalArgumentException");
        } catch (IllegalArgumentException e) {
            // Expected
        }
    }

    @Test
    public void newProvider_multipleScheme() {
        NameResolverRegistry registry = NameResolverRegistry.getDefaultRegistry();
        ProxylessXdsNameResolverProvider provider0 = ProxylessXdsNameResolverProvider.createForTest("no-scheme", null);
        registry.register(provider0);
        ProxylessXdsNameResolverProvider provider1 = ProxylessXdsNameResolverProvider.createForTest("new-xds-scheme",
                new HashMap<String, String>());
        registry.register(provider1);
        assertThat(registry.asFactory()
                .newNameResolver(URI.create("xds:///localhost"), args)).isNotNull();
        assertThat(registry.asFactory()
                .newNameResolver(URI.create("new-xds-scheme:///localhost"), args)).isNotNull();
        assertThat(registry.asFactory()
                .newNameResolver(URI.create("no-scheme:///localhost"), args)).isNotNull();
        registry.deregister(provider1);
        assertThat(registry.asFactory()
                .newNameResolver(URI.create("new-xds-scheme:///localhost"), args)).isNull();
        registry.deregister(provider0);
        assertThat(registry.asFactory()
                .newNameResolver(URI.create("xds:///localhost"), args)).isNotNull();
    }

    @Test
    public void newProvider_overrideBootstrap() {
        Map<String, ?> b = ImmutableMap.of(
                "node", ImmutableMap.of(
                        "id", "ENVOY_NODE_ID",
                        "cluster", "ENVOY_CLUSTER"),
                "xds_servers", Collections.singletonList(
                        ImmutableMap.of(
                                "server_uri", "trafficdirector.googleapis.com:443",
                                "channel_creds", Collections.singletonList(
                                        ImmutableMap.of("type", "insecure")
                                )
                        )
                )
        );
        NameResolverRegistry registry = new NameResolverRegistry();
        ProxylessXdsNameResolverProvider provider = ProxylessXdsNameResolverProvider.createForTest("no-scheme", b);
        registry.register(provider);
        NameResolver resolver = registry.asFactory()
                .newNameResolver(URI.create("no-scheme:///localhost"), args);
        resolver.start(mock(NameResolver.Listener2.class));
        assertThat(resolver).isInstanceOf(ProxylessXdsNameResolver.class);
        assertThat(((ProxylessXdsNameResolver) resolver).getXdsClient().getBootstrapInfo().node().getId())
                .isEqualTo("ENVOY_NODE_ID");
        resolver.shutdown();
        registry.deregister(provider);
    }
}
