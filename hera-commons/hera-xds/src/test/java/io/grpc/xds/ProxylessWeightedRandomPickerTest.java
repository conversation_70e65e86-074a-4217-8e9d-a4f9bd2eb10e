package io.grpc.xds;

import com.wosai.middleware.hera.rpc.LoadBalancer;
import io.grpc.Status;
import io.grpc.xds.ProxylessWeightedRandomPicker.WeightedChildPicker;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.google.common.truth.Truth.assertThat;
import static org.mockito.Mockito.mock;

/**
 * Tests for {@link ProxylessWeightedRandomPicker}.
 */
@RunWith(JUnit4.class)
public class ProxylessWeightedRandomPickerTest {
    @SuppressWarnings("deprecation") // https://github.com/grpc/grpc-java/issues/7467
    @Rule
    public final ExpectedException thrown = ExpectedException.none();

    @Rule
    public final MockitoRule mockitoRule = MockitoJUnit.rule();

    @Mock
    private LoadBalancer.PickServerArgs pickServerArgs;

    private final LoadBalancer.PickResult pickResult0 = LoadBalancer.PickResult.withNoResult();
    private final LoadBalancer.PickResult pickResult1 = LoadBalancer.PickResult.withDrop(Status.UNAVAILABLE);
    private final LoadBalancer.PickResult pickResult2 = LoadBalancer.PickResult.withServer(mock(LoadBalancer.Server.class));
    private final LoadBalancer.PickResult pickResult3 = LoadBalancer.PickResult.withServer(mock(LoadBalancer.Server.class));

    private final LoadBalancer.ServerPicker childPicker0 = new LoadBalancer.ServerPicker() {
        @Override
        public LoadBalancer.PickResult pickServer(LoadBalancer.PickServerArgs args) {
            return pickResult0;
        }
    };

    private final LoadBalancer.ServerPicker childPicker1 = new LoadBalancer.ServerPicker() {
        @Override
        public LoadBalancer.PickResult pickServer(LoadBalancer.PickServerArgs args) {
            return pickResult1;
        }
    };

    private final LoadBalancer.ServerPicker childPicker2 = new LoadBalancer.ServerPicker() {
        @Override
        public LoadBalancer.PickResult pickServer(LoadBalancer.PickServerArgs args) {
            return pickResult2;
        }
    };

    private final LoadBalancer.ServerPicker childPicker3 = new LoadBalancer.ServerPicker() {
        @Override
        public LoadBalancer.PickResult pickServer(LoadBalancer.PickServerArgs args) {
            return pickResult3;
        }
    };

    private static final class FakeRandom implements ThreadSafeRandom {
        int nextInt;
        long bound;
        Long nextLong;

        @Override
        public int nextInt(int bound) {
            this.bound = bound;

            assertThat(nextInt).isAtLeast(0);
            assertThat(nextInt).isLessThan(bound);
            return nextInt;
        }

        @Override
        public long nextLong() {
            throw new UnsupportedOperationException("Should not be called");
        }

        @Override
        public long nextLong(long bound) {
            this.bound = bound;

            if (nextLong == null) {
                assertThat(nextInt).isAtLeast(0);
                if (bound <= Integer.MAX_VALUE) {
                    assertThat(nextInt).isLessThan((int)bound);
                }
                return nextInt;
            }

            assertThat(nextLong).isAtLeast(0);
            assertThat(nextLong).isLessThan(bound);
            return nextLong;
        }
    }

    private final FakeRandom fakeRandom = new FakeRandom();

    @Test
    public void emptyList() {
        List<WeightedChildPicker> emptyList = new ArrayList<>();

        thrown.expect(IllegalArgumentException.class);
        new ProxylessWeightedRandomPicker(emptyList);
    }

    @Test
    public void negativeWeight() {
        thrown.expect(IllegalArgumentException.class);
        new WeightedChildPicker(-1, childPicker0);
    }

    @Test
    public void overWeightSingle() {
        thrown.expect(IllegalArgumentException.class);
        new WeightedChildPicker(Integer.MAX_VALUE * 3L, childPicker0);
    }

    @Test
    public void overWeightAggregate() {

        List<WeightedChildPicker> weightedChildPickers = Arrays.asList(
                new WeightedChildPicker(Integer.MAX_VALUE, childPicker0),
                new WeightedChildPicker(Integer.MAX_VALUE, childPicker1),
                new WeightedChildPicker(10, childPicker2));

        thrown.expect(IllegalArgumentException.class);
        new ProxylessWeightedRandomPicker(weightedChildPickers, fakeRandom);
    }

    @Test
    public void pickWithFakeRandom() {
        WeightedChildPicker weightedChildPicker0 = new WeightedChildPicker(0, childPicker0);
        WeightedChildPicker weightedChildPicker1 = new WeightedChildPicker(15, childPicker1);
        WeightedChildPicker weightedChildPicker2 = new WeightedChildPicker(0, childPicker2);
        WeightedChildPicker weightedChildPicker3 = new WeightedChildPicker(10, childPicker3);

        ProxylessWeightedRandomPicker xdsPicker = new ProxylessWeightedRandomPicker(
                Arrays.asList(
                        weightedChildPicker0,
                        weightedChildPicker1,
                        weightedChildPicker2,
                        weightedChildPicker3),
                fakeRandom);

        fakeRandom.nextInt = 0;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult1);
        assertThat(fakeRandom.bound).isEqualTo(25);

        fakeRandom.nextInt = 1;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult1);
        assertThat(fakeRandom.bound).isEqualTo(25);

        fakeRandom.nextInt = 14;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult1);
        assertThat(fakeRandom.bound).isEqualTo(25);

        fakeRandom.nextInt = 15;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult3);
        assertThat(fakeRandom.bound).isEqualTo(25);

        fakeRandom.nextInt = 24;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult3);
        assertThat(fakeRandom.bound).isEqualTo(25);
    }

    @Test
    public void pickFromLargeTotal() {

        List<WeightedChildPicker> weightedChildPickers = Arrays.asList(
                new WeightedChildPicker(10, childPicker0),
                new WeightedChildPicker(Integer.MAX_VALUE, childPicker1),
                new WeightedChildPicker(10, childPicker2));
        ProxylessWeightedRandomPicker xdsPicker = new ProxylessWeightedRandomPicker(weightedChildPickers,fakeRandom);

        long totalWeight = weightedChildPickers.stream()
                .mapToLong(WeightedChildPicker::getWeight)
                .reduce(0, Long::sum);

        fakeRandom.nextLong = 5L;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult0);
        assertThat(fakeRandom.bound).isEqualTo(totalWeight);

        fakeRandom.nextLong = 16L;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult1);
        assertThat(fakeRandom.bound).isEqualTo(totalWeight);

        fakeRandom.nextLong = Integer.MAX_VALUE + 10L;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult2);
        assertThat(fakeRandom.bound).isEqualTo(totalWeight);

        fakeRandom.nextLong = Integer.MAX_VALUE + 15L;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult2);
        assertThat(fakeRandom.bound).isEqualTo(totalWeight);
    }

    @Test
    public void allZeroWeights() {
        WeightedChildPicker weightedChildPicker0 = new WeightedChildPicker(0, childPicker0);
        WeightedChildPicker weightedChildPicker1 = new WeightedChildPicker(0, childPicker1);
        WeightedChildPicker weightedChildPicker2 = new WeightedChildPicker(0, childPicker2);
        WeightedChildPicker weightedChildPicker3 = new WeightedChildPicker(0, childPicker3);

        ProxylessWeightedRandomPicker xdsPicker = new ProxylessWeightedRandomPicker(
                Arrays.asList(
                        weightedChildPicker0,
                        weightedChildPicker1,
                        weightedChildPicker2,
                        weightedChildPicker3),
                fakeRandom);

        fakeRandom.nextInt = 0;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult0);
        assertThat(fakeRandom.bound).isEqualTo(4);

        fakeRandom.nextInt = 1;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult1);
        assertThat(fakeRandom.bound).isEqualTo(4);

        fakeRandom.nextInt = 2;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult2);
        assertThat(fakeRandom.bound).isEqualTo(4);

        fakeRandom.nextInt = 3;
        assertThat(xdsPicker.pickServer(pickServerArgs)).isSameInstanceAs(pickResult3);
        assertThat(fakeRandom.bound).isEqualTo(4);
    }
}
