package io.grpc.xds;

import com.wosai.middleware.hera.rpc.LoadBalancer;
import com.wosai.middleware.hera.rpc.LoadBalancerProvider;
import com.wosai.middleware.hera.rpc.LoadBalancerRegistry;
import io.grpc.NameResolver;
import io.grpc.SynchronizationContext;
import io.grpc.internal.JsonParser;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.io.IOException;
import java.util.Map;

import static com.google.common.truth.Truth.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link ProxylessCdsLoadBalancerProvider}.
 */
@RunWith(JUnit4.class)
public class ProxylessCdsLoadBalancerProviderTest {

    @Test
    public void parseCdsLoadBalancingPolicyConfig() throws IOException {
        ProxylessCdsLoadBalancerProvider provider = new ProxylessCdsLoadBalancerProvider();
        String rawCdsLbConfig = "{\n"
                + "  \"cluster\": \"cluster-foo.googleapis.com\"\n"
                + "}";

        @SuppressWarnings("unchecked")
        Map<String, ?> rawLbConfigMap = (Map<String, ?>) JsonParser.parse(rawCdsLbConfig);
        NameResolver.ConfigOrError result = provider.parseLoadBalancingPolicyConfig(rawLbConfigMap);
        assertThat(result.getConfig()).isNotNull();
        CdsLoadBalancerProvider.CdsConfig config = (CdsLoadBalancerProvider.CdsConfig) result.getConfig();
        assertThat(config.name).isEqualTo("cluster-foo.googleapis.com");
    }

    @Test
    public void provided() {
        LoadBalancerProvider provider =
                LoadBalancerRegistry.getDefaultRegistry().getProvider(XdsLbPolicies.CDS_POLICY_NAME);
        assertThat(provider).isInstanceOf(ProxylessCdsLoadBalancerProvider.class);
    }

    @Test
    public void providesLoadBalancer() {
        LoadBalancer.Helper helper = mock(LoadBalancer.Helper.class);

        SynchronizationContext syncContext = new SynchronizationContext(
                new Thread.UncaughtExceptionHandler() {
                    @Override
                    public void uncaughtException(Thread t, Throwable e) {
                        throw new AssertionError(e);
                    }
                });
        when(helper.getSynchronizationContext()).thenReturn(syncContext);
        LoadBalancerProvider provider = new ProxylessCdsLoadBalancerProvider();
        LoadBalancer loadBalancer = provider.newLoadBalancer(helper);
        assertThat(loadBalancer).isInstanceOf(ProxylessCdsLoadBalancer.class);
    }
}
