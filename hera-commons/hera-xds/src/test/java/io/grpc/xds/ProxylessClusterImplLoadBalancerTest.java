/*
 * Copyright 2020 The gRPC Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.grpc.xds;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Iterables;
import com.wosai.middleware.hera.rpc.LoadBalancer;
import com.wosai.middleware.hera.rpc.LoadBalancerProvider;
import io.grpc.Attributes;
import io.grpc.ConnectivityState;
import io.grpc.EquivalentAddressGroup;
import io.grpc.InsecureChannelCredentials;
import io.grpc.LoadBalancer.ResolvedAddresses;
import io.grpc.Status;
import io.grpc.Status.Code;
import io.grpc.SynchronizationContext;
import io.grpc.internal.FakeClock;
import io.grpc.internal.ObjectPool;
import io.grpc.internal.ProxylessServiceConfigUtil;
import io.grpc.internal.ProxylessServiceConfigUtil.PolicySelection;
import io.grpc.xds.Bootstrapper.ServerInfo;
import io.grpc.xds.Endpoints.DropOverload;
import io.grpc.xds.EnvoyServerProtoData.DownstreamTlsContext;
import io.grpc.xds.EnvoyServerProtoData.UpstreamTlsContext;
import io.grpc.xds.LoadStatsManager2.ClusterDropStats;
import io.grpc.xds.LoadStatsManager2.ClusterLocalityStats;
import io.grpc.xds.ProxylessClusterImplLoadBalancerProvider.ClusterImplConfig;
import io.grpc.xds.ProxylessWeightedTargetLoadBalancerProvider.WeightedPolicySelection;
import io.grpc.xds.ProxylessWeightedTargetLoadBalancerProvider.WeightedTargetConfig;
import io.grpc.xds.XdsNameResolverProvider.CallCounterProvider;
import io.grpc.xds.internal.security.SslContextProvider;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.net.SocketAddress;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;

import static com.google.common.truth.Truth.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Tests for {@link ProxylessClusterImplLoadBalancer}.
 */
@RunWith(JUnit4.class)
public class ProxylessClusterImplLoadBalancerTest {
    private static final String AUTHORITY = "api.google.com";
    private static final String CLUSTER = "cluster-foo.googleapis.com";
    private static final String EDS_SERVICE_NAME = "service.googleapis.com";
    private static final ServerInfo LRS_SERVER_INFO =
            ServerInfo.create("api.google.com", InsecureChannelCredentials.create(), true);
    private final SynchronizationContext syncContext = new SynchronizationContext(
            new Thread.UncaughtExceptionHandler() {
                @Override
                public void uncaughtException(Thread t, Throwable e) {
                    throw new AssertionError(e);
                }
            });
    private final FakeClock fakeClock = new FakeClock();
    private final Locality locality =
            Locality.create("test-region", "test-zone", "test-subzone");
    private final PolicySelection roundRobin =
            new PolicySelection(new FakeLoadBalancerProvider("round_robin"), null);
    private final List<FakeLoadBalancer> downstreamBalancers = new ArrayList<>();
    private final FakeTlsContextManager tlsContextManager = new FakeTlsContextManager();
    private final LoadStatsManager2 loadStatsManager =
            new LoadStatsManager2(fakeClock.getStopwatchSupplier());
    private final FakeXdsClient xdsClient = new FakeXdsClient();
    private final ObjectPool<XdsClient> xdsClientPool = new ObjectPool<XdsClient>() {
        @Override
        public XdsClient getObject() {
            xdsClientRefs++;
            return xdsClient;
        }

        @Override
        public XdsClient returnObject(Object object) {
            xdsClientRefs--;
            return null;
        }
    };
    private final CallCounterProvider callCounterProvider = new CallCounterProvider() {
        @Override
        public AtomicLong getOrCreate(String cluster, @Nullable String edsServiceName) {
            return new AtomicLong();
        }
    };
    private final LoadBalancer.Helper helper = new FakeLbHelper();
    @Mock
    private ThreadSafeRandom mockRandom;
    private int xdsClientRefs;
    private ConnectivityState currentState;
    private LoadBalancer.ServerPicker currentPicker;
    private ProxylessClusterImplLoadBalancer loadBalancer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        loadBalancer = new ProxylessClusterImplLoadBalancer(helper, mockRandom);
    }

    @After
    public void tearDown() {
        if (loadBalancer != null) {
            loadBalancer.shutdown();
        }
        assertThat(xdsClientRefs).isEqualTo(0);
        assertThat(downstreamBalancers).isEmpty();
    }

    @Test
    public void handleResolvedAddresses_propagateToChildPolicy() {
        FakeLoadBalancerProvider weightedTargetProvider =
                new FakeLoadBalancerProvider(XdsLbPolicies.WEIGHTED_TARGET_POLICY_NAME);
        Object weightedTargetConfig = new Object();
        ClusterImplConfig config = new ClusterImplConfig(CLUSTER, EDS_SERVICE_NAME, LRS_SERVER_INFO,
                null, Collections.<DropOverload>emptyList(),
                new ProxylessServiceConfigUtil.PolicySelection(weightedTargetProvider, weightedTargetConfig), null);
        EquivalentAddressGroup endpoint = makeAddress("endpoint-addr", locality);
        deliverAddressesAndConfig(Collections.singletonList(endpoint), config);
        FakeLoadBalancer childBalancer = Iterables.getOnlyElement(downstreamBalancers);
        assertThat(Iterables.getOnlyElement(childBalancer.addresses)).isEqualTo(endpoint);
        assertThat(childBalancer.config).isSameInstanceAs(weightedTargetConfig);
        assertThat(childBalancer.attributes.get(InternalXdsAttributes.XDS_CLIENT_POOL))
                .isSameInstanceAs(xdsClientPool);
    }

    /**
     * If the control plane switches from using the legacy lb_policy field in the xDS Cluster proto
     * to the newer load_balancing_policy then the child policy can switch from weighted_target to
     * xds_wrr_locality (this could happen the opposite way as well). This test assures that this
     * results in the child LB changing if this were to happen. If this is not done correctly the new
     * configuration would be given to the old LB implementation which would cause a channel panic.
     */
    @Test
    public void handleResolvedAddresses_childPolicyChanges() {
        FakeLoadBalancerProvider weightedTargetProvider =
                new FakeLoadBalancerProvider(XdsLbPolicies.WEIGHTED_TARGET_POLICY_NAME);
        Object weightedTargetConfig = new Object();
        ClusterImplConfig configWithWeightedTarget = new ClusterImplConfig(CLUSTER, EDS_SERVICE_NAME,
                LRS_SERVER_INFO,
                null, Collections.<DropOverload>emptyList(),
                new PolicySelection(weightedTargetProvider, weightedTargetConfig), null);
        EquivalentAddressGroup endpoint = makeAddress("endpoint-addr", locality);
        deliverAddressesAndConfig(Collections.singletonList(endpoint), configWithWeightedTarget);
        FakeLoadBalancer childBalancer = Iterables.getOnlyElement(downstreamBalancers);
        assertThat(childBalancer.name).isEqualTo(XdsLbPolicies.WEIGHTED_TARGET_POLICY_NAME);
        assertThat(childBalancer.config).isSameInstanceAs(weightedTargetConfig);

        FakeLoadBalancerProvider wrrLocalityProvider =
                new FakeLoadBalancerProvider(XdsLbPolicies.WRR_LOCALITY_POLICY_NAME);
        Object wrrLocalityConfig = new Object();
        ClusterImplConfig configWithWrrLocality = new ClusterImplConfig(CLUSTER, EDS_SERVICE_NAME,
                LRS_SERVER_INFO,
                null, Collections.<DropOverload>emptyList(),
                new PolicySelection(wrrLocalityProvider, wrrLocalityConfig), null);
        deliverAddressesAndConfig(Collections.singletonList(endpoint), configWithWrrLocality);
        childBalancer = Iterables.getOnlyElement(downstreamBalancers);
        assertThat(childBalancer.name).isEqualTo(XdsLbPolicies.WRR_LOCALITY_POLICY_NAME);
        assertThat(childBalancer.config).isSameInstanceAs(wrrLocalityConfig);
    }

    @Test
    public void nameResolutionError_beforeChildPolicyInstantiated_returnErrorPickerToUpstream() {
        loadBalancer.handleNameResolutionError(Status.UNIMPLEMENTED.withDescription("not found"));
        assertThat(currentState).isEqualTo(ConnectivityState.TRANSIENT_FAILURE);
        LoadBalancer.PickResult result = currentPicker.pickServer(mock(LoadBalancer.PickServerArgs.class));
        assertThat(result.getStatus().isOk()).isFalse();
        assertThat(result.getStatus().getCode()).isEqualTo(Code.UNIMPLEMENTED);
        assertThat(result.getStatus().getDescription()).isEqualTo("not found");
    }

    @Test
    public void nameResolutionError_afterChildPolicyInstantiated_propagateToDownstream() {
        FakeLoadBalancerProvider weightedTargetProvider =
                new FakeLoadBalancerProvider(XdsLbPolicies.WEIGHTED_TARGET_POLICY_NAME);
        Object weightedTargetConfig = new Object();
        ClusterImplConfig config = new ClusterImplConfig(CLUSTER, EDS_SERVICE_NAME, LRS_SERVER_INFO,
                null, Collections.<DropOverload>emptyList(),
                new PolicySelection(weightedTargetProvider, weightedTargetConfig), null);
        EquivalentAddressGroup endpoint = makeAddress("endpoint-addr", locality);
        deliverAddressesAndConfig(Collections.singletonList(endpoint), config);
        FakeLoadBalancer childBalancer = Iterables.getOnlyElement(downstreamBalancers);

        loadBalancer.handleNameResolutionError(
                Status.UNAVAILABLE.withDescription("cannot reach server"));
        assertThat(childBalancer.upstreamError.getCode()).isEqualTo(Code.UNAVAILABLE);
        assertThat(childBalancer.upstreamError.getDescription())
                .isEqualTo("cannot reach server");
    }

    @Test
    public void endpointAddressesAttachedWithClusterName() {
        LoadBalancerProvider weightedTargetProvider = new ProxylessWeightedTargetLoadBalancerProvider();
        WeightedTargetConfig weightedTargetConfig =
                buildWeightedTargetConfig(ImmutableMap.of(locality, 10));
        ClusterImplConfig config = new ClusterImplConfig(CLUSTER, EDS_SERVICE_NAME, LRS_SERVER_INFO,
                null, Collections.<DropOverload>emptyList(),
                new PolicySelection(weightedTargetProvider, weightedTargetConfig), null);
        // One locality with two endpoints.
        EquivalentAddressGroup endpoint1 = makeAddress("endpoint-addr1", locality);
        EquivalentAddressGroup endpoint2 = makeAddress("endpoint-addr2", locality);
        deliverAddressesAndConfig(Arrays.asList(endpoint1, endpoint2), config);
        assertThat(downstreamBalancers).hasSize(1);  // one leaf balancer
        FakeLoadBalancer leafBalancer = Iterables.getOnlyElement(downstreamBalancers);
        assertThat(leafBalancer.name).isEqualTo("round_robin");
    }

    private void deliverAddressesAndConfig(List<EquivalentAddressGroup> addresses,
                                           ClusterImplConfig config) {
        loadBalancer.handleResolvedAddresses(
                ResolvedAddresses.newBuilder()
                        .setAddresses(addresses)
                        .setAttributes(
                                Attributes.newBuilder()
                                        .set(InternalXdsAttributes.XDS_CLIENT_POOL, xdsClientPool)
                                        .set(InternalXdsAttributes.CALL_COUNTER_PROVIDER, callCounterProvider)
                                        .build())
                        .setLoadBalancingPolicyConfig(config)
                        .build());
    }

    private WeightedTargetConfig buildWeightedTargetConfig(Map<Locality, Integer> localityWeights) {
        Map<String, WeightedPolicySelection> targets = new HashMap<>();
        for (Locality locality : localityWeights.keySet()) {
            int weight = localityWeights.get(locality);
            WeightedPolicySelection weightedLocalityLbPolicy =
                    new WeightedPolicySelection(weight, roundRobin);
            targets.put(locality.toString(), weightedLocalityLbPolicy);
        }
        return new WeightedTargetConfig(Collections.unmodifiableMap(targets));
    }

    /**
     * Create a locality-labeled address.
     */
    private static EquivalentAddressGroup makeAddress(final String name, Locality locality) {
        class FakeSocketAddress extends SocketAddress {
            private final String name;

            private FakeSocketAddress(String name) {
                this.name = name;
            }

            @Override
            public int hashCode() {
                return Objects.hash(name);
            }

            @Override
            public boolean equals(Object o) {
                if (this == o) {
                    return true;
                }
                if (!(o instanceof FakeSocketAddress)) {
                    return false;
                }
                FakeSocketAddress that = (FakeSocketAddress) o;
                return Objects.equals(name, that.name);
            }

            @Override
            public String toString() {
                return name;
            }
        }

        EquivalentAddressGroup eag = new EquivalentAddressGroup(new FakeSocketAddress(name),
                Attributes.newBuilder().set(InternalXdsAttributes.ATTR_LOCALITY, locality).build());
        return AddressFilter.setPathFilter(eag, Collections.singletonList(locality.toString()));
    }

    private final class FakeLoadBalancerProvider extends LoadBalancerProvider {
        private final String policyName;

        FakeLoadBalancerProvider(String policyName) {
            this.policyName = policyName;
        }

        @Override
        public LoadBalancer newLoadBalancer(LoadBalancer.Helper helper) {
            FakeLoadBalancer balancer = new FakeLoadBalancer(policyName, helper);
            downstreamBalancers.add(balancer);
            return balancer;
        }

        @Override
        public boolean isAvailable() {
            return true;
        }

        @Override
        public int getPriority() {
            return 0;  // doesn't matter
        }

        @Override
        public String getPolicyName() {
            return policyName;
        }
    }

    private final class FakeLoadBalancer extends LoadBalancer {
        private final String name;
        private final Helper helper;
        private List<EquivalentAddressGroup> addresses;
        private Object config;
        private Attributes attributes;
        private Status upstreamError;

        FakeLoadBalancer(String name, Helper helper) {
            this.name = name;
            this.helper = helper;
        }

        @Override
        public void handleResolvedAddresses(ResolvedAddresses resolvedAddresses) {
            addresses = resolvedAddresses.getAddresses();
            config = resolvedAddresses.getLoadBalancingPolicyConfig();
            attributes = resolvedAddresses.getAttributes();
        }

        @Override
        public void handleNameResolutionError(Status error) {
            upstreamError = error;
        }

        @Override
        public void shutdown() {
            downstreamBalancers.remove(this);
        }

        void deliverServerState(final Server server, ConnectivityState state) {
            ServerPicker picker = new ServerPicker() {
                @Override
                public PickResult pickServer(PickServerArgs args) {
                    return PickResult.withServer(server);
                }
            };
            helper.updateBalancingState(state, picker);
        }
    }

    private final class FakeLbHelper extends LoadBalancer.Helper {

        @Override
        public SynchronizationContext getSynchronizationContext() {
            return syncContext;
        }

        @Override
        public void updateBalancingState(
                @Nonnull ConnectivityState newState, @Nonnull LoadBalancer.ServerPicker newPicker) {
            currentState = newState;
            currentPicker = newPicker;
        }

        @Override
        public String getAuthority() {
            return AUTHORITY;
        }
    }

    private final class FakeXdsClient extends XdsClient {
        @Override
        ClusterDropStats addClusterDropStats(
                ServerInfo lrsServerInfo, String clusterName, @Nullable String edsServiceName) {
            return loadStatsManager.getClusterDropStats(clusterName, edsServiceName);
        }

        @Override
        ClusterLocalityStats addClusterLocalityStats(
                ServerInfo lrsServerInfo, String clusterName, @Nullable String edsServiceName,
                Locality locality) {
            return loadStatsManager.getClusterLocalityStats(clusterName, edsServiceName, locality);
        }

        @Override
        TlsContextManager getTlsContextManager() {
            return tlsContextManager;
        }
    }

    private static final class FakeTlsContextManager implements TlsContextManager {
        @Override
        public SslContextProvider findOrCreateClientSslContextProvider(
                UpstreamTlsContext upstreamTlsContext) {
            SslContextProvider sslContextProvider = mock(SslContextProvider.class);
            when(sslContextProvider.getUpstreamTlsContext()).thenReturn(upstreamTlsContext);
            return sslContextProvider;
        }

        @Override
        public SslContextProvider releaseClientSslContextProvider(
                SslContextProvider sslContextProvider) {
            // no-op
            return null;
        }

        @Override
        public SslContextProvider findOrCreateServerSslContextProvider(
                DownstreamTlsContext downstreamTlsContext) {
            throw new UnsupportedOperationException("should not be called");
        }

        @Override
        public SslContextProvider releaseServerSslContextProvider(
                SslContextProvider sslContextProvider) {
            throw new UnsupportedOperationException("should not be called");
        }
    }
}
