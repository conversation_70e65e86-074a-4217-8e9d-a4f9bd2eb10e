<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.shouqianba.middleware</groupId>
        <artifactId>dubbo-jsonrpc-demo</artifactId>
        <version>1.1.4-SNAPSHOT</version>
    </parent>

    <groupId>com.shouqianba.middleware</groupId>
    <artifactId>dubbo-jsonrpc-demo-consumer</artifactId>
    <packaging>jar</packaging>
    <name>dubbo-jsonrpc-demo-consumer</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- dubbo -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>

        <!-- spring boot starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shouqianba.middleware</groupId>
            <artifactId>dubbo-jsonrpc-demo-api</artifactId>
            <version>1.1.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>dubbo-rpc-http</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo.extensions</groupId>
            <artifactId>dubbo-remoting-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shouqianba.middleware</groupId>
            <artifactId>service-governance-demo-api</artifactId>
            <version>1.1.2</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>6.6</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>dubbo-jsonrpc-demo-consumer</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
