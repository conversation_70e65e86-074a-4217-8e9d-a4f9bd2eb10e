package com.shouqianba.middleware.dubbo.demo.consumer.dubbo;

import com.shouqianba.middleware.dubbo.demo.EchoService;
import com.shouqianba.middleware.dubbo.demo.JsonrpcService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/dubbo")
public class DubboController {
    @DubboReference(id = "jsonrpcService")
    private JsonrpcService jsonrpcService;

    @DubboReference()
    private EchoService echoService;

    @RequestMapping("/echo/{str}")
    public String dubboecho(@PathVariable String str) {
        String result = "from jsonrpc service: " + jsonrpcService.echo(str) + " \nfrom dubbo service: " + echoService.echo(str) + "";
        return result;
    }
}
