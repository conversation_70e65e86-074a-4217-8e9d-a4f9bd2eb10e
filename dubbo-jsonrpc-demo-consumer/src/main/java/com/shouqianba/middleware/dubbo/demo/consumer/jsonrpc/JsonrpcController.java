package com.shouqianba.middleware.dubbo.demo.consumer.jsonrpc;

import com.shouqianba.middleware.dubbo.demo.JsonrpcService;
import com.shouqianba.middleware.service.HostService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/jsonrpc")
public class JsonrpcController {

    // http 直连
    @DubboReference(id = "hostService", url = "http://service-governance-demo.beta.iwosai.com/rpc/host")
    private HostService hostService;

    @Autowired()
    @Qualifier(value = "jsonrpcBeanService")
    private JsonrpcService jsonrpcBeanService;

    @RequestMapping("/echo/{str}")
    public String dubboecho(@PathVariable String str) {
        return jsonrpcBeanService.echo(str);
    }


    @RequestMapping("/getHost")
    public String getHost() {
        return hostService.getHost();
    }
}