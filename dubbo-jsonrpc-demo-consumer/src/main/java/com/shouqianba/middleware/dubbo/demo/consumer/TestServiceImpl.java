package com.shouqianba.middleware.dubbo.demo.consumer;

import com.shouqianba.middleware.dubbo.demo.TestService;
import org.apache.dubbo.config.annotation.DubboService;

@DubboService(interfaceClass = TestService.class, path = "/rpc/test", version = "1.0.0", group = "dev")
public class TestServiceImpl implements TestService {
    @Override
    public String test(String str) {
        return "consumer:" + str;
    }
}
