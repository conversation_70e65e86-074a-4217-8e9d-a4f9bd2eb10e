package com.shouqianba.middleware.dubbo.demo.consumer;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.googlecode.jsonrpc4j.*;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter;
import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.shouqianba.middleware.dubbo.demo.JsonrpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.validation.ConstraintViolationException;
import java.lang.reflect.Method;
import java.util.List;

import static net.logstash.logback.argument.StructuredArguments.value;

@Slf4j
@Configuration
public class JsonrpcConfig {

    @Bean
    public static AutoJsonRpcServiceImplExporter autoJsonRpcServiceImplExporter() {
        final AutoJsonRpcServiceImplExporter exporter = new AutoJsonRpcServiceImplExporter();
        exporter.setInvocationListener(new InvocationListener() {
            @Override
            public void willInvoke(Method method, List<JsonNode> arguments) {
                log.info("jsonrpc-server,method:{},arguments:{}",
                        method.getName(),
                        arguments);
            }

            @Override
            public void didInvoke(Method method, List<JsonNode> arguments, Object result, Throwable t, long duration) {
                if (t == null) {
                    log.info("jsonrpc-server,method:{},arguments:{},result:{},duration:{}",
                            method.getName(),
                            arguments,
                            result,
                            duration);
                } else {
                    log.warn("jsonrpc-server,method:{},arguments:{},error_message:{},dutation:{}",
                            method.getName(),
                            arguments,
                            t.getMessage(),
                            duration);
                }
            }
        });
        exporter.setErrorResolver((t, method, arguments) -> {
            int code = 500;
            String msg = t.getMessage();
            if (t instanceof ConstraintViolationException) {
                code = 400;
                StringBuilder sb = new StringBuilder();
                ((ConstraintViolationException) t).getConstraintViolations().forEach(violation -> sb.append(violation.getMessage()).append(";"));
                msg = sb.toString();
            } else if (t instanceof JsonMappingException) {
                code = 400;
                msg = "无法识别的参数";
            }
            ErrorData errorData = new ErrorData(t.getClass().getName(), msg);
            return new ErrorResolver.JsonError(code, msg, errorData);
        });
        return exporter;
    }


    private static final JsonRpcClient.RequestListener requestListener = new JsonRpcClient.RequestListener() {
        private ThreadLocal<Long> startTime = new ThreadLocal<>();
        private ThreadLocal<ObjectNode> req = new ThreadLocal<>();

        @Override
        public void onBeforeRequestSent(JsonRpcClient client, ObjectNode request) {
            startTime.set(System.currentTimeMillis());
            req.set(request);
        }

        @Override
        public void onBeforeResponseProcessed(JsonRpcClient client, ObjectNode response) {
            String url = (client instanceof JsonRpcHttpClient)
                    ? ((JsonRpcHttpClient) client).getServiceUrl().toString() : null;
            log.info("jsonrpc-client",
                    value("url", url),
                    value("request", req.get()),
                    value("response", response),
                    value("duration", System.currentTimeMillis() - startTime.get()));
        }
    };

    private JsonProxyFactoryBean createJsonBean(Class serviceInterface, String serviceUrl, String serverName) {
        JsonProxyFactoryBean bean = new JsonProxyFactoryBean();
        bean.setServiceInterface(serviceInterface);
        bean.setServiceUrl(serviceUrl);
        bean.setRequestListener(requestListener);
        bean.setServerName(serverName);
        return bean;
    }

    @Bean(name = "jsonrpcBeanService")
    public JsonProxyFactoryBean jsonrpcBeanService() {
        return createJsonBean(JsonrpcService.class, "http://dubbo-jsonrpc-demo-service.beta.iwosai.com" + "/rpc/jsonrpc", "jsonrpc4j-wosai");
    }

}
