package io.grpc;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import javax.annotation.concurrent.GuardedBy;
import javax.annotation.concurrent.ThreadSafe;
import java.net.URI;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.google.common.base.Preconditions.checkArgument;

@ThreadSafe
@Slf4j
public final class ProxylessNameResolverRegistry {
    private static final List<String> HARD_CODED = Lists.newArrayList("io.grpc.xds.ProxylessXdsNameResolverProvider");
    private static ProxylessNameResolverRegistry INSTANCE;
    private final NameResolver.Factory factory = new NameResolverFactory();
    private static final String UNKNOWN_SCHEME = "unknown";
    @GuardedBy("this")
    private String defaultScheme = UNKNOWN_SCHEME;

    @GuardedBy("this")
    private final LinkedHashSet<NameResolverProvider> allProviders = new LinkedHashSet<>();
    /**
     * Generated from {@code allProviders}. Is mapping from scheme key to the highest priority
     * {@link NameResolverProvider}. Is replaced instead of mutating.
     */
    @GuardedBy("this")
    private ImmutableMap<String, NameResolverProvider> effectiveProviders = ImmutableMap.of();

    /**
     * Register a provider.
     *
     * <p>If the provider's {@link NameResolverProvider#isAvailable isAvailable()} returns
     * {@code false}, this method will throw {@link IllegalArgumentException}.
     *
     * <p>Providers will be used in priority order. In case of ties, providers are used in
     * registration order.
     */
    public synchronized void register(NameResolverProvider provider) {
        addProvider(provider);
        refreshProviders();
    }

    private synchronized void addProvider(NameResolverProvider provider) {
        checkArgument(provider.isAvailable(), "isAvailable() returned false");
        allProviders.add(provider);
    }

    /**
     * Deregisters a provider.  No-op if the provider is not in the registry.
     *
     * @param provider the provider that was added to the register via {@link #register}.
     */
    public synchronized void deregister(NameResolverProvider provider) {
        allProviders.remove(provider);
        refreshProviders();
    }

    private synchronized void refreshProviders() {
        Map<String, NameResolverProvider> refreshedProviders = new HashMap<>();
        int maxPriority = Integer.MIN_VALUE;
        String refreshedDefaultScheme = UNKNOWN_SCHEME;
        // We prefer first-registered providers
        for (NameResolverProvider provider : allProviders) {
            String scheme = provider.getScheme();
            NameResolverProvider existing = refreshedProviders.get(scheme);
            if (existing == null || existing.priority() < provider.priority()) {
                refreshedProviders.put(scheme, provider);
            }
            if (maxPriority < provider.priority()) {
                maxPriority = provider.priority();
                refreshedDefaultScheme = provider.getScheme();
            }
        }
        effectiveProviders = ImmutableMap.copyOf(refreshedProviders);
        defaultScheme = refreshedDefaultScheme;
    }

    /**
     * Returns the default registry that loads providers via the Java service loader mechanism.
     */
    public static synchronized ProxylessNameResolverRegistry getDefaultRegistry() {
        if (INSTANCE == null) {
            // we only load hard-coded providers
            List<NameResolverProvider> providerList = getHardCodedProviders().stream()
                    .filter(NameResolverProvider::isAvailable)
                    .sorted(Comparator.comparingInt(NameResolverProvider::priority).reversed())
                    .collect(Collectors.toList());
            if (providerList.isEmpty()) {
                log.warn("No NameResolverProviders found via ServiceLoader, including for DNS. This "
                        + "is probably due to a broken build. If using ProGuard, check your configuration");
            }
            INSTANCE = new ProxylessNameResolverRegistry();
            for (NameResolverProvider provider : providerList) {
                log.debug("Service loader found {}", provider);
                if (provider.isAvailable()) {
                    INSTANCE.addProvider(provider);
                }
            }
            INSTANCE.refreshProviders();
        }
        return INSTANCE;
    }

    /**
     * Returns effective providers map from scheme to the highest priority NameResolverProvider of
     * that scheme.
     */
    @VisibleForTesting
    synchronized Map<String, NameResolverProvider> providers() {
        return effectiveProviders;
    }

    public NameResolver.Factory asFactory() {
        return factory;
    }

    @VisibleForTesting
    static List<NameResolverProvider> getHardCodedProviders() {
        // Class.forName(String) is used to remove the need for ProGuard configuration. Note that
        // ProGuard does not detect usages of Class.forName(String, boolean, ClassLoader):
        // https://sourceforge.net/p/proguard/bugs/418/
        ArrayList<NameResolverProvider> list = new ArrayList<>();
        for (final String className : HARD_CODED) {
            try {
                list.add((NameResolverProvider) Class.forName(className).newInstance());
            } catch (ClassNotFoundException e) {
                log.error("Unable to find class", e);
            } catch (InstantiationException | IllegalAccessException e) {
                log.error("Unable to create an instance", e);
            }
        }
        return Collections.unmodifiableList(list);
    }

    private final class NameResolverFactory extends NameResolver.Factory {
        @Override
        @Nullable
        public NameResolver newNameResolver(URI targetUri, NameResolver.Args args) {
            NameResolverProvider provider = providers().get(targetUri.getScheme());
            return provider == null ? null : provider.newNameResolver(targetUri, args);
        }

        @Override
        public String getDefaultScheme() {
            synchronized (ProxylessNameResolverRegistry.this) {
                return defaultScheme;
            }
        }
    }
}
