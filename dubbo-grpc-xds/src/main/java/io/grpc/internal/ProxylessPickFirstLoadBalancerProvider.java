package io.grpc.internal;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.LoadBalancerProvider;
import io.grpc.NameResolver;
import io.grpc.Status;

import java.util.Map;

public class ProxylessPickFirstLoadBalancerProvider extends LoadBalancerProvider {
    private static final String NO_CONFIG = "no service config";
    private static final String SHUFFLE_ADDRESS_LIST_KEY = "shuffleAddressList";
    private static final String CONFIG_FLAG_NAME = "GRPC_EXPERIMENTAL_PICKFIRST_LB_CONFIG";
    @VisibleForTesting
    static boolean ENABLE_PICK_FIRST_CONFIG = !Strings.isNullOrEmpty(System.getenv(CONFIG_FLAG_NAME));

    @Override
    public LoadBalancer newLoadBalancer(LoadBalancer.Helper helper) {
        return new ProxylessPickFirstLoadBalancer(helper);
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 5;
    }

    @Override
    public String getPolicyName() {
        return "pick_first";
    }

    @Override
    public NameResolver.ConfigOrError parseLoadBalancingPolicyConfig(
            Map<String, ?> rawLoadBalancingPolicyConfig) {
        if (ENABLE_PICK_FIRST_CONFIG) {
            try {
                return NameResolver.ConfigOrError.fromConfig(
                        new PickFirstLoadBalancer.PickFirstLoadBalancerConfig(JsonUtil.getBoolean(rawLoadBalancingPolicyConfig,
                                SHUFFLE_ADDRESS_LIST_KEY)));
            } catch (RuntimeException e) {
                return NameResolver.ConfigOrError.fromError(
                        Status.UNAVAILABLE.withCause(e).withDescription(
                                "Failed parsing configuration for " + getPolicyName()));
            }
        } else {
            return NameResolver.ConfigOrError.fromConfig(NO_CONFIG);
        }
    }
}
