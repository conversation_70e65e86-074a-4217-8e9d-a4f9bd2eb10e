package io.grpc.internal;

import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import com.wosai.middleware.LoadBalancerProvider;
import com.wosai.middleware.LoadBalancerRegistry;
import io.grpc.NameResolver;
import io.grpc.Status;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;

import static com.google.common.base.Preconditions.checkNotNull;

@Slf4j
public class ProxylessServiceConfigUtil {
    /**
     * Parses and selects a load balancing policy from a non-empty list of raw configs. If selection
     * is successful, the returned ConfigOrError object will include a {@link
     * PolicySelection} as its config value.
     */
    public static NameResolver.ConfigOrError selectLbPolicyFromList(
            List<ServiceConfigUtil.LbConfig> lbConfigs, LoadBalancerRegistry lbRegistry) {
        List<String> policiesTried = new ArrayList<>();
        for (ServiceConfigUtil.LbConfig lbConfig : lbConfigs) {
            String policy = lbConfig.getPolicyName();
            LoadBalancerProvider provider = lbRegistry.getProvider(policy);
            if (provider == null) {
                policiesTried.add(policy);
            } else {
                if (!policiesTried.isEmpty()) {
                    log.trace("{} specified by Service Config are not available", policiesTried);
                }
                NameResolver.ConfigOrError parsedLbPolicyConfig =
                        provider.parseLoadBalancingPolicyConfig(lbConfig.getRawConfigValue());
                if (parsedLbPolicyConfig.getError() != null) {
                    return parsedLbPolicyConfig;
                }
                return NameResolver.ConfigOrError.fromConfig(
                        new PolicySelection(provider, parsedLbPolicyConfig.getConfig()));
            }
        }
        return NameResolver.ConfigOrError.fromError(
                Status.UNKNOWN.withDescription(
                        "None of " + policiesTried + " specified by Service Config are available."));
    }

    @Getter
    public static final class PolicySelection {
        final LoadBalancerProvider provider;
        @Nullable
        final Object config;

        /**
         * Constructs a PolicySelection with selected LB provider and the deeply parsed LB config.
         */
        public PolicySelection(
                LoadBalancerProvider provider,
                @Nullable Object config) {
            this.provider = checkNotNull(provider, "provider");
            this.config = config;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            PolicySelection that = (PolicySelection) o;
            return Objects.equal(provider, that.provider)
                    && Objects.equal(config, that.config);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(provider, config);
        }

        @Override
        public String toString() {
            return MoreObjects.toStringHelper(this)
                    .add("provider", provider)
                    .add("config", config)
                    .toString();
        }
    }
}
