package io.grpc.xds;

import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.LoadBalancerProvider;
import com.wosai.middleware.LoadBalancerRegistry;
import io.grpc.NameResolver;
import io.grpc.Status;
import io.grpc.internal.JsonUtil;
import io.grpc.internal.ProxylessServiceConfigUtil;
import io.grpc.internal.ServiceConfigUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import static io.grpc.xds.LoadBalancerConfigFactory.CHILD_POLICY_FIELD;

@Slf4j
public class ProxylessWrrLocalityLoadBalancerProvider extends LoadBalancerProvider {
    @Override
    public LoadBalancer newLoadBalancer(LoadBalancer.Helper helper) {
        return new ProxylessWrrLocalityLoadBalancer(helper);
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 5;
    }

    @Override
    public String getPolicyName() {
        return XdsLbPolicies.WRR_LOCALITY_POLICY_NAME;
    }

    @Override
    public NameResolver.ConfigOrError parseLoadBalancingPolicyConfig(Map<String, ?> rawConfig) {
        try {
            List<ServiceConfigUtil.LbConfig> childConfigCandidates = ServiceConfigUtil.unwrapLoadBalancingConfigList(
                    JsonUtil.getListOfObjects(rawConfig, CHILD_POLICY_FIELD));
            if (childConfigCandidates == null || childConfigCandidates.isEmpty()) {
                return NameResolver.ConfigOrError.fromError(Status.INTERNAL.withDescription(
                        "No child policy in wrr_locality LB policy: "
                                + rawConfig));
            }
            log.debug("childConfigCandidates: {}", childConfigCandidates);
            // At this stage, we are selecting child policy for the WrrLocalityLoadBalancer,
            // e.g. round_robin, weighted_round_robin_experimental
            NameResolver.ConfigOrError selectedConfig =
                    ProxylessServiceConfigUtil.selectLbPolicyFromList(childConfigCandidates,
                            LoadBalancerRegistry.getDefaultRegistry());
            if (selectedConfig.getError() != null) {
                return selectedConfig;
            }
            ProxylessServiceConfigUtil.PolicySelection policySelection = (ProxylessServiceConfigUtil.PolicySelection) selectedConfig.getConfig();
            return NameResolver.ConfigOrError.fromConfig(new ProxylessWrrLocalityLoadBalancer.WrrLocalityConfig(policySelection));
        } catch (RuntimeException e) {
            return NameResolver.ConfigOrError.fromError(Status.INTERNAL.withCause(e)
                    .withDescription("Failed to parse wrr_locality LB config: " + rawConfig));
        }
    }
}
