package io.grpc.xds;

import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.LoadBalancerProvider;
import io.grpc.LoadBalancerRegistry;
import io.grpc.NameResolver;
import io.grpc.Status;
import io.grpc.internal.ProxylessServiceConfigUtil;
import lombok.ToString;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.google.common.base.Preconditions.checkNotNull;

/**
 * The provider for the cluster_impl load balancing policy. This class should not be directly
 * referenced in code.  The policy should be accessed through
 * {@link LoadBalancerRegistry#getProvider} with the name "cluster_impl_experimental".
 */
public class ProxylessClusterImplLoadBalancerProvider extends LoadBalancerProvider {
    @Override
    public LoadBalancer newLoadBalancer(LoadBalancer.Helper helper) {
        return new ProxylessClusterImplLoadBalancer(helper);
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 5;
    }

    @Override
    public String getPolicyName() {
        return XdsLbPolicies.CLUSTER_IMPL_POLICY_NAME;
    }

    @Override
    public NameResolver.ConfigOrError parseLoadBalancingPolicyConfig(Map<String, ?> rawLoadBalancingPolicyConfig) {
        return NameResolver.ConfigOrError.fromError(
                Status.INTERNAL.withDescription(getPolicyName() + " cannot be used from service config"));
    }

    @ToString
    static final class ClusterImplConfig {
        // Name of the cluster.
        final String cluster;
        // Resource name used in discovering endpoints via EDS. Only valid for EDS clusters.
        @Nullable
        final String edsServiceName;
        // Load report server info. Null if load reporting is disabled.
        @Nullable
        final Bootstrapper.ServerInfo lrsServerInfo;
        // Cluster-level max concurrent request threshold. Null if not specified.
        @Nullable
        final Long maxConcurrentRequests;
        // TLS context for connections to endpoints.
        @Nullable
        final EnvoyServerProtoData.UpstreamTlsContext tlsContext;
        // Drop configurations.
        final List<Endpoints.DropOverload> dropCategories;
        // Provides the direct child policy and its config.
        final ProxylessServiceConfigUtil.PolicySelection childPolicy;

        ClusterImplConfig(String cluster, @Nullable String edsServiceName,
                          @Nullable Bootstrapper.ServerInfo lrsServerInfo, @Nullable Long maxConcurrentRequests,
                          List<Endpoints.DropOverload> dropCategories, ProxylessServiceConfigUtil.PolicySelection childPolicy,
                          @Nullable EnvoyServerProtoData.UpstreamTlsContext tlsContext) {
            this.cluster = checkNotNull(cluster, "cluster");
            this.edsServiceName = edsServiceName;
            this.lrsServerInfo = lrsServerInfo;
            this.maxConcurrentRequests = maxConcurrentRequests;
            this.tlsContext = tlsContext;
            this.dropCategories = Collections.unmodifiableList(
                    new ArrayList<>(checkNotNull(dropCategories, "dropCategories")));
            this.childPolicy = checkNotNull(childPolicy, "childPolicy");
        }
    }
}
