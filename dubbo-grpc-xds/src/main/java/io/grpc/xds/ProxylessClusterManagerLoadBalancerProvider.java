package io.grpc.xds;

import com.google.common.annotations.VisibleForTesting;
import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.LoadBalancerProvider;
import com.wosai.middleware.LoadBalancerRegistry;
import io.grpc.NameResolver;
import io.grpc.Status;
import io.grpc.internal.JsonUtil;
import io.grpc.internal.ProxylessServiceConfigUtil;
import io.grpc.internal.ServiceConfigUtil;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * The provider for the cluster_manager load balancing policy. This class should not be directly
 * referenced in code.  The policy should be accessed through
 * {@link LoadBalancerRegistry#getProvider} with the name "cluster_manager_experimental".
 */
public class ProxylessClusterManagerLoadBalancerProvider extends LoadBalancerProvider {
    @Nullable
    private final LoadBalancerRegistry lbRegistry;

    public ProxylessClusterManagerLoadBalancerProvider() {
        this(null);
    }

    @VisibleForTesting
    ProxylessClusterManagerLoadBalancerProvider(@Nullable LoadBalancerRegistry lbRegistry) {
        this.lbRegistry = lbRegistry;
    }

    @Override
    public LoadBalancer newLoadBalancer(LoadBalancer.Helper helper) {
        return new ProxylessClusterManagerLoadBalancer(helper);
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 5;
    }

    @Override
    public String getPolicyName() {
        return XdsLbPolicies.CLUSTER_MANAGER_POLICY_NAME;
    }

    @Override
    public NameResolver.ConfigOrError parseLoadBalancingPolicyConfig(Map<String, ?> rawConfig) {
        Map<String, ProxylessServiceConfigUtil.PolicySelection> parsedChildPolicies = new LinkedHashMap<>();
        try {
            Map<String, ?> childPolicies = JsonUtil.getObject(rawConfig, "childPolicy");
            if (childPolicies == null || childPolicies.isEmpty()) {
                return NameResolver.ConfigOrError.fromError(Status.INTERNAL.withDescription(
                        "No child policy provided for cluster_manager LB policy: " + rawConfig));
            }
            for (String name : childPolicies.keySet()) {
                Map<String, ?> childPolicy = JsonUtil.getObject(childPolicies, name);
                if (childPolicy == null) {
                    return NameResolver.ConfigOrError.fromError(Status.INTERNAL.withDescription(
                            "No config for child " + name + " in cluster_manager LB policy: " + rawConfig));
                }
                List<ServiceConfigUtil.LbConfig> childConfigCandidates =
                        ServiceConfigUtil.unwrapLoadBalancingConfigList(
                                JsonUtil.getListOfObjects(childPolicy, "lbPolicy"));
                if (childConfigCandidates == null || childConfigCandidates.isEmpty()) {
                    return NameResolver.ConfigOrError.fromError(Status.INTERNAL.withDescription(
                            "No config specified for child " + name + " in cluster_manager Lb policy: "
                                    + rawConfig));
                }
                LoadBalancerRegistry registry =
                        lbRegistry != null ? lbRegistry : LoadBalancerRegistry.getDefaultRegistry();
                NameResolver.ConfigOrError selectedConfig =
                        ProxylessServiceConfigUtil.selectLbPolicyFromList(childConfigCandidates, registry);
                if (selectedConfig.getError() != null) {
                    Status error = selectedConfig.getError();
                    return NameResolver.ConfigOrError.fromError(
                            Status.INTERNAL
                                    .withCause(error.getCause())
                                    .withDescription(error.getDescription())
                                    .augmentDescription("Failed to select config for child " + name));
                }
                parsedChildPolicies.put(name, (ProxylessServiceConfigUtil.PolicySelection) selectedConfig.getConfig());
            }
        } catch (RuntimeException e) {
            return NameResolver.ConfigOrError.fromError(
                    Status.INTERNAL.withCause(e).withDescription(
                            "Failed to parse cluster_manager LB config: " + rawConfig));
        }
        return NameResolver.ConfigOrError.fromConfig(new ClusterManagerConfig(parsedChildPolicies));
    }

    @ToString
    @EqualsAndHashCode
    static class ClusterManagerConfig {
        final Map<String, ProxylessServiceConfigUtil.PolicySelection> childPolicies;

        ClusterManagerConfig(Map<String, ProxylessServiceConfigUtil.PolicySelection> childPolicies) {
            this.childPolicies = Collections.unmodifiableMap(childPolicies);
        }
    }
}
