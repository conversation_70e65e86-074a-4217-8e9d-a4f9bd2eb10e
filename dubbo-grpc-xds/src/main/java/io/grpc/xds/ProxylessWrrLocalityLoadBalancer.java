package io.grpc.xds;

import com.google.common.base.MoreObjects;
import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.LoadBalancerRegistry;
import com.wosai.middleware.util.GracefulSwitchLoadBalancer;
import io.grpc.Attributes;
import io.grpc.EquivalentAddressGroup;
import io.grpc.InternalLogId;
import io.grpc.Status;
import io.grpc.internal.ProxylessServiceConfigUtil;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.google.common.base.Preconditions.checkNotNull;
import static io.grpc.ConnectivityState.TRANSIENT_FAILURE;
import static io.grpc.xds.XdsLbPolicies.WEIGHTED_TARGET_POLICY_NAME;

final class ProxylessWrrLocalityLoadBalancer extends LoadBalancer {
    private final XdsSlf4jLogger logger;
    private final LoadBalancer.Helper helper;
    private final GracefulSwitchLoadBalancer switchLb;
    private final LoadBalancerRegistry lbRegistry;

    ProxylessWrrLocalityLoadBalancer(LoadBalancer.Helper helper) {
        this(helper, LoadBalancerRegistry.getDefaultRegistry());
    }

    ProxylessWrrLocalityLoadBalancer(LoadBalancer.Helper helper, LoadBalancerRegistry lbRegistry) {
        this.helper = checkNotNull(helper, "helper");
        this.lbRegistry = lbRegistry;
        switchLb = new GracefulSwitchLoadBalancer(helper);
        logger = XdsSlf4jLogger.withLogId(ProxylessWrrLocalityLoadBalancer.class,
                InternalLogId.allocate("xds-wrr-locality-lb", helper.getAuthority()));
        logger.debug("Created");
    }

    @Override
    public boolean acceptResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses) {
        // ResolvedAddresses{
        //   addresses=[
        //     [[/**********:8080]/{org.apache.skywalking.apm.dependencies.io.grpc.xds.AddressFilter.PATH_CHAIN_KEY=Locality{region=, zone=, subZone=}, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.localityWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.serverWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.locality=Locality{region=, zone=, subZone=}}],
        //     [[/**********:8080]/{org.apache.skywalking.apm.dependencies.io.grpc.xds.AddressFilter.PATH_CHAIN_KEY=Locality{region=, zone=, subZone=}, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.localityWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.serverWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.locality=Locality{region=, zone=, subZone=}}],
        //     [[/**********:8080]/{org.apache.skywalking.apm.dependencies.io.grpc.xds.AddressFilter.PATH_CHAIN_KEY=Locality{region=, zone=, subZone=}, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.localityWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.serverWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.locality=Locality{region=, zone=, subZone=}}]
        //   ],
        //   attributes={org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.xdsClientPool=org.apache.skywalking.apm.dependencies.io.grpc.xds.SharedXdsClientPoolProvider$RefCountedXdsClientObjectPool@4c5f2f57, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.callCounterProvider=org.apache.skywalking.apm.dependencies.io.grpc.xds.SharedCallCounterMap@3c3ec68e},
        //   loadBalancingPolicyConfig=ProxylessWrrLocalityLoadBalancer.WrrLocalityConfig(childPolicy=ProxylessServiceConfigUtil.PolicySelection(provider=Provider{policy=round_robin, priority=5, available=true}, config=no service config))
        // }
        logger.trace("Received resolution result: {}", resolvedAddresses);

        // The configuration with the child policy is combined with the locality weights
        // to produce the weighted target LB config.
        WrrLocalityConfig wrrLocalityConfig
                = (WrrLocalityConfig) resolvedAddresses.getLoadBalancingPolicyConfig();

        // A map of locality weights is built up from the locality weight attributes in each address.
        Map<Locality, Integer> localityWeights = new HashMap<>();
        for (EquivalentAddressGroup eag : resolvedAddresses.getAddresses()) {
            Attributes eagAttrs = eag.getAttributes();
            Locality locality = eagAttrs.get(InternalXdsAttributes.ATTR_LOCALITY);
            Integer localityWeight = eagAttrs.get(InternalXdsAttributes.ATTR_LOCALITY_WEIGHT);

            if (locality == null) {
                helper.updateBalancingState(TRANSIENT_FAILURE, new ProxylessXdsServerPickers.ErrorPicker(
                        Status.UNAVAILABLE.withDescription("wrr_locality error: no locality provided")));
                return false;
            }
            if (localityWeight == null) {
                helper.updateBalancingState(TRANSIENT_FAILURE, new ProxylessXdsServerPickers.ErrorPicker(
                        Status.UNAVAILABLE.withDescription(
                                "wrr_locality error: no weight provided for locality " + locality)));
                return false;
            }

            if (!localityWeights.containsKey(locality)) {
                localityWeights.put(locality, localityWeight);
            } else if (!localityWeights.get(locality).equals(localityWeight)) {
                logger.warn("Locality {} has both weights {} and {}, using the former one", locality,
                        localityWeights.get(locality), localityWeight);
            }
        }

        // Weighted target LB expects a WeightedPolicySelection for each locality as it will create a
        // child LB for each.
        Map<String, ProxylessWeightedTargetLoadBalancerProvider.WeightedPolicySelection> weightedPolicySelections = new HashMap<>();
        for (Locality locality : localityWeights.keySet()) {
            weightedPolicySelections.put(locality.toString(),
                    new ProxylessWeightedTargetLoadBalancerProvider.WeightedPolicySelection(localityWeights.get(locality),
                            wrrLocalityConfig.childPolicy));
        }

        switchLb.switchTo(lbRegistry.getProvider(WEIGHTED_TARGET_POLICY_NAME));
        switchLb.handleResolvedAddresses(
                resolvedAddresses.toBuilder()
                        .setLoadBalancingPolicyConfig(new ProxylessWeightedTargetLoadBalancerProvider.WeightedTargetConfig(weightedPolicySelections))
                        .build());

        return true;
    }

    @Override
    public void handleNameResolutionError(Status error) {
        logger.warn("Received name resolution error: {}", error);
        switchLb.handleNameResolutionError(error);
    }

    @Override
    public void shutdown() {
        switchLb.shutdown();
    }

    /**
     * The LB config for {@link ProxylessWrrLocalityLoadBalancer}.
     */
    @RequiredArgsConstructor
    static final class WrrLocalityConfig {
        final ProxylessServiceConfigUtil.PolicySelection childPolicy;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            WrrLocalityConfig that = (WrrLocalityConfig) o;
            return Objects.equals(childPolicy, that.childPolicy);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(childPolicy);
        }

        @Override
        public String toString() {
            return MoreObjects.toStringHelper(this).add("childPolicy", childPolicy).toString();
        }
    }
}
