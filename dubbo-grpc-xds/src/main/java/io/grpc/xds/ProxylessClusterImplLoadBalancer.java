package io.grpc.xds;

import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.util.ForwardingLoadBalancerHelper;
import com.wosai.middleware.util.GracefulSwitchLoadBalancer;
import io.grpc.Attributes;
import io.grpc.ConnectivityState;
import io.grpc.InternalLogId;
import io.grpc.Status;
import io.grpc.internal.ObjectPool;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

import static com.google.common.base.Preconditions.checkNotNull;
import static io.grpc.xds.ProxylessXdsServerPickers.BUFFER_PICKER;

final class ProxylessClusterImplLoadBalancer extends LoadBalancer {
    private final XdsSlf4jLogger logger;
    private final LoadBalancer.Helper helper;
    private final ThreadSafeRandom random;
    // The following fields are effectively final.
    private String cluster;
    @Nullable
    private String edsServiceName;
    private ObjectPool<XdsClient> xdsClientPool;
    private XdsClient xdsClient;
    private XdsNameResolverProvider.CallCounterProvider callCounterProvider;
    private LoadStatsManager2.ClusterDropStats dropStats;
    private ClusterImplLbHelper childLbHelper;
    private GracefulSwitchLoadBalancer childSwitchLb;

    ProxylessClusterImplLoadBalancer(Helper helper) {
        this(helper, ThreadSafeRandom.ThreadSafeRandomImpl.instance);
    }

    ProxylessClusterImplLoadBalancer(LoadBalancer.Helper helper, ThreadSafeRandom random) {
        this.helper = checkNotNull(helper, "helper");
        this.random = checkNotNull(random, "random");
        InternalLogId logId = InternalLogId.allocate("cluster-impl-lb", helper.getAuthority());
        logger = XdsSlf4jLogger.withLogId(ProxylessClusterImplLoadBalancer.class, logId);
        logger.debug("Created");
    }

    @Override
    public boolean acceptResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses) {
        // ResolvedAddresses{
        //   addresses=[
        //     [[/**********:8080]/{org.apache.skywalking.apm.dependencies.io.grpc.xds.AddressFilter.PATH_CHAIN_KEY=Locality{region=, zone=, subZone=}, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.localityWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.serverWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.locality=Locality{region=, zone=, subZone=}}],
        //     [[/**********:8080]/{org.apache.skywalking.apm.dependencies.io.grpc.xds.AddressFilter.PATH_CHAIN_KEY=Locality{region=, zone=, subZone=}, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.localityWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.serverWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.locality=Locality{region=, zone=, subZone=}}],
        //     [[/**********:8080]/{org.apache.skywalking.apm.dependencies.io.grpc.xds.AddressFilter.PATH_CHAIN_KEY=Locality{region=, zone=, subZone=}, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.localityWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.serverWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.locality=Locality{region=, zone=, subZone=}}]
        //   ],
        //   attributes={org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.xdsClientPool=org.apache.skywalking.apm.dependencies.io.grpc.xds.SharedXdsClientPoolProvider$RefCountedXdsClientObjectPool@4c5f2f57, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.callCounterProvider=org.apache.skywalking.apm.dependencies.io.grpc.xds.SharedCallCounterMap@3c3ec68e},
        //   loadBalancingPolicyConfig=ProxylessClusterImplLoadBalancerProvider.ClusterImplConfig(cluster=outbound|80|e2e-service-provider-red|e2e-service-provider.default.svc.cluster.local, edsServiceName=outbound|80|e2e-service-provider-red|e2e-service-provider.default.svc.cluster.local, lrsServerInfo=null, maxConcurrentRequests=null, tlsContext=null, dropCategories=[], childPolicy=ProxylessServiceConfigUtil.PolicySelection(provider=ProxylessWrrLocalityLoadBalancerProvider{policy=wrr_locality_experimental, priority=5, available=true}, config=ProxylessWrrLocalityLoadBalancer.WrrLocalityConfig(childPolicy=ProxylessServiceConfigUtil.PolicySelection(provider=Provider{policy=round_robin, priority=5, available=true}, config=no service config))))
        // }
        logger.trace("Received resolution result: {}", resolvedAddresses);
        Attributes attributes = resolvedAddresses.getAttributes();
        if (xdsClientPool == null) {
            xdsClientPool = attributes.get(InternalXdsAttributes.XDS_CLIENT_POOL);
            xdsClient = xdsClientPool.getObject();
        }
        if (callCounterProvider == null) {
            callCounterProvider = attributes.get(InternalXdsAttributes.CALL_COUNTER_PROVIDER);
        }
        ProxylessClusterImplLoadBalancerProvider.ClusterImplConfig config =
                (ProxylessClusterImplLoadBalancerProvider.ClusterImplConfig) resolvedAddresses.getLoadBalancingPolicyConfig();
        if (cluster == null) {
            cluster = config.cluster;
            edsServiceName = config.edsServiceName;
            childLbHelper = new ClusterImplLbHelper(
                    callCounterProvider.getOrCreate(config.cluster, config.edsServiceName),
                    config.lrsServerInfo);
            childSwitchLb = new GracefulSwitchLoadBalancer(childLbHelper);
            // Assume load report server does not change throughout cluster lifetime.
            if (config.lrsServerInfo != null) {
                dropStats = xdsClient.addClusterDropStats(config.lrsServerInfo, cluster, edsServiceName);
            }
        }
        childLbHelper.updateDropPolicies(config.dropCategories);

        childSwitchLb.switchTo(config.childPolicy.getProvider());
        childSwitchLb.handleResolvedAddresses(
                resolvedAddresses.toBuilder()
                        .setAttributes(attributes)
                        .setLoadBalancingPolicyConfig(config.childPolicy.getConfig())
                        .build());
        return true;
    }

    @Override
    public void handleNameResolutionError(Status error) {
        if (childSwitchLb != null) {
            childSwitchLb.handleNameResolutionError(error);
        } else {
            helper.updateBalancingState(ConnectivityState.TRANSIENT_FAILURE, new ProxylessXdsServerPickers.ErrorPicker(error));
        }
    }

    @Override
    public void shutdown() {
        if (dropStats != null) {
            dropStats.release();
        }
        if (childSwitchLb != null) {
            childSwitchLb.shutdown();
            if (childLbHelper != null) {
                childLbHelper = null;
            }
        }
        if (xdsClient != null) {
            xdsClient = xdsClientPool.returnObject(xdsClient);
        }
    }

    /**
     * A decorated {@link LoadBalancer.Helper} that applies configurations for connections
     * or requests to endpoints in the cluster.
     */
    private final class ClusterImplLbHelper extends ForwardingLoadBalancerHelper {
        private final AtomicLong inFlights;
        private ConnectivityState currentState = ConnectivityState.IDLE;
        private LoadBalancer.ServerPicker currentPicker = BUFFER_PICKER;
        private List<Endpoints.DropOverload> dropPolicies = Collections.emptyList();
        @Nullable
        private final Bootstrapper.ServerInfo lrsServerInfo;

        private ClusterImplLbHelper(AtomicLong inFlights, @Nullable Bootstrapper.ServerInfo lrsServerInfo) {
            this.inFlights = checkNotNull(inFlights, "inFlights");
            this.lrsServerInfo = lrsServerInfo;
        }

        @Override
        public void updateBalancingState(ConnectivityState newState, LoadBalancer.ServerPicker newPicker) {
            currentState = newState;
            currentPicker = newPicker;
            delegate().updateBalancingState(newState, newPicker);
        }

        @Override
        protected LoadBalancer.Helper delegate() {
            return helper;
        }

        private void updateDropPolicies(List<Endpoints.DropOverload> dropOverloads) {
            if (!dropPolicies.equals(dropOverloads)) {
                dropPolicies = dropOverloads;
                updateBalancingState(currentState, currentPicker);
            }
        }
    }
}
