package io.grpc.xds;

import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.LoadBalancerProvider;
import io.grpc.NameResolver;
import io.grpc.Status;
import io.grpc.internal.ProxylessServiceConfigUtil;
import lombok.ToString;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.google.common.base.Preconditions.checkNotNull;

public class ProxylessClusterResolverLoadBalancerProvider extends LoadBalancerProvider {
    @Override
    public LoadBalancer newLoadBalancer(LoadBalancer.Helper helper) {
        return new ProxylessClusterResolverLoadBalancer(helper);
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 5;
    }

    @Override
    public String getPolicyName() {
        return XdsLbPolicies.CLUSTER_RESOLVER_POLICY_NAME;
    }

    @Override
    public NameResolver.ConfigOrError parseLoadBalancingPolicyConfig(Map<String, ?> rawLoadBalancingPolicyConfig) {
        return NameResolver.ConfigOrError.fromError(
                Status.INTERNAL.withDescription(getPolicyName() + " cannot be used from service config"));
    }

    @ToString
    static final class ClusterResolverConfig {
        // Ordered list of clusters to be resolved.
        final List<ClusterResolverLoadBalancerProvider.ClusterResolverConfig.DiscoveryMechanism> discoveryMechanisms;
        // Endpoint-level load balancing policy with config
        // (round_robin, least_request_experimental or ring_hash_experimental).
        final ProxylessServiceConfigUtil.PolicySelection lbPolicy;

        ClusterResolverConfig(List<ClusterResolverLoadBalancerProvider.ClusterResolverConfig.DiscoveryMechanism> discoveryMechanisms,
                              ProxylessServiceConfigUtil.PolicySelection lbPolicy) {
            this.discoveryMechanisms = checkNotNull(discoveryMechanisms, "discoveryMechanisms");
            this.lbPolicy = checkNotNull(lbPolicy, "lbPolicy");
        }

        @Override
        public int hashCode() {
            return Objects.hash(discoveryMechanisms, lbPolicy);
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            ClusterResolverConfig that = (ClusterResolverConfig) o;
            return discoveryMechanisms.equals(that.discoveryMechanisms)
                    && lbPolicy.equals(that.lbPolicy);
        }
    }
}
