package io.grpc.xds;

import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.LoadBalancerProvider;
import io.grpc.NameResolver;

import java.util.Map;

/**
 * The provider for the "cds" balancing policy.  This class should not be directly referenced in
 * code. The policy should be accessed through {@link com.wosai.middleware.LoadBalancerRegistry#getProvider}
 * with the name "cds" (currently "cds_experimental").
 */
public class ProxylessCdsLoadBalancerProvider extends LoadBalancerProvider {
    @Override
    public LoadBalancer newLoadBalancer(LoadBalancer.Helper helper) {
        return new ProxylessCdsLoadBalancer(helper);
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 5;
    }

    @Override
    public String getPolicyName() {
        return XdsLbPolicies.CDS_POLICY_NAME;
    }

    @Override
    public NameResolver.ConfigOrError parseLoadBalancingPolicyConfig(
            Map<String, ?> rawLoadBalancingPolicyConfig) {
        return CdsLoadBalancerProvider.parseLoadBalancingConfigPolicy(rawLoadBalancingPolicyConfig);
    }
}
