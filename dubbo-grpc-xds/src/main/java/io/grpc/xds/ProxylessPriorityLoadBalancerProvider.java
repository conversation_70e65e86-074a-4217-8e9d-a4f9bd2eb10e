package io.grpc.xds;

import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.LoadBalancerProvider;
import io.grpc.NameResolver;
import io.grpc.Status;
import io.grpc.internal.ProxylessServiceConfigUtil;
import lombok.ToString;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkNotNull;

/**
 * Provider for priority load balancing policy.
 */
public final class ProxylessPriorityLoadBalancerProvider extends LoadBalancerProvider {

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public int getPriority() {
        return 5;
    }

    @Override
    public String getPolicyName() {
        return XdsLbPolicies.PRIORITY_POLICY_NAME;
    }

    @Override
    public LoadBalancer newLoadBalancer(LoadBalancer.Helper helper) {
        return new ProxylessPriorityLoadBalancer(helper);
    }

    @Override
    public NameResolver.ConfigOrError parseLoadBalancingPolicyConfig(Map<String, ?> rawConfig) {
        return NameResolver.ConfigOrError.fromError(
                Status.INTERNAL.withDescription(getPolicyName() + " cannot be used from service config"));
    }

    @ToString
    static final class PriorityLbConfig {
        final Map<String, PriorityChildConfig> childConfigs;
        final List<String> priorities;

        PriorityLbConfig(Map<String, PriorityChildConfig> childConfigs, List<String> priorities) {
            this.childConfigs = Collections.unmodifiableMap(checkNotNull(childConfigs, "childConfigs"));
            this.priorities = Collections.unmodifiableList(checkNotNull(priorities, "priorities"));
            checkArgument(!priorities.isEmpty(), "priority list is empty");
            checkArgument(
                    childConfigs.keySet().containsAll(priorities),
                    "missing child config for at lease one of the priorities");
            checkArgument(
                    priorities.size() == new HashSet<>(priorities).size(),
                    "duplicate names in priorities");
            checkArgument(
                    priorities.size() == childConfigs.keySet().size(),
                    "some names in childConfigs are not referenced by priorities");
        }

        @ToString
        static final class PriorityChildConfig {
            final ProxylessServiceConfigUtil.PolicySelection policySelection;
            final boolean ignoreReresolution;

            PriorityChildConfig(ProxylessServiceConfigUtil.PolicySelection policySelection, boolean ignoreReresolution) {
                this.policySelection = checkNotNull(policySelection, "policySelection");
                this.ignoreReresolution = ignoreReresolution;
            }
        }
    }
}
