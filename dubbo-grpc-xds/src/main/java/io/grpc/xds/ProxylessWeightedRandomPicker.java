package io.grpc.xds;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.primitives.UnsignedInteger;
import com.wosai.middleware.LoadBalancer;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkNotNull;

@ToString
final class ProxylessWeightedRandomPicker extends LoadBalancer.ServerPicker {
    @VisibleForTesting
    final List<WeightedChildPicker> weightedChildPickers;

    private final ThreadSafeRandom random;
    private final long totalWeight;

    @ToString
    @EqualsAndHashCode
    static final class WeightedChildPicker {
        private final long weight;
        private final LoadBalancer.ServerPicker childPicker;

        WeightedChildPicker(long weight, LoadBalancer.ServerPicker childPicker) {
            checkArgument(weight >= 0, "weight is negative");
            checkArgument(weight <= UnsignedInteger.MAX_VALUE.longValue(), "weight is too large");
            checkNotNull(childPicker, "childPicker is null");

            this.weight = weight;
            this.childPicker = childPicker;
        }

        long getWeight() {
            return weight;
        }

        LoadBalancer.ServerPicker getPicker() {
            return childPicker;
        }
    }

    ProxylessWeightedRandomPicker(List<WeightedChildPicker> weightedChildPickers) {
        this(weightedChildPickers, ThreadSafeRandom.ThreadSafeRandomImpl.instance);
    }

    @VisibleForTesting
    ProxylessWeightedRandomPicker(List<WeightedChildPicker> weightedChildPickers, ThreadSafeRandom random) {
        checkNotNull(weightedChildPickers, "weightedChildPickers in null");
        checkArgument(!weightedChildPickers.isEmpty(), "weightedChildPickers is empty");

        this.weightedChildPickers = Collections.unmodifiableList(weightedChildPickers);

        long totalWeight = 0;
        for (WeightedChildPicker weightedChildPicker : weightedChildPickers) {
            long weight = weightedChildPicker.getWeight();
            checkArgument(weight >= 0, "weight is negative");
            checkNotNull(weightedChildPicker.getPicker(), "childPicker is null");
            totalWeight += weight;
        }
        this.totalWeight = totalWeight;
        checkArgument(totalWeight <= UnsignedInteger.MAX_VALUE.longValue(),
                "total weight greater than unsigned int can hold");

        this.random = random;
    }

    @Override
    public final LoadBalancer.PickResult pickServer(LoadBalancer.PickServerArgs args) {
        LoadBalancer.ServerPicker childPicker = null;

        if (totalWeight == 0) {
            childPicker =
                    weightedChildPickers.get(random.nextInt(weightedChildPickers.size())).getPicker();
        } else {
            long rand = random.nextLong(totalWeight);

            // Find the first idx such that rand < accumulatedWeights[idx]
            // Not using Arrays.binarySearch for better readability.
            long accumulatedWeight = 0;
            for (int idx = 0; idx < weightedChildPickers.size(); idx++) {
                accumulatedWeight += weightedChildPickers.get(idx).getWeight();
                if (rand < accumulatedWeight) {
                    childPicker = weightedChildPickers.get(idx).getPicker();
                    break;
                }
            }
            checkNotNull(childPicker, "childPicker not found");
        }

        return childPicker.pickServer(args);
    }

    @Override
    public List<LoadBalancer.Server> getServers() {
        return weightedChildPickers.stream()
                .map(picker -> picker.getPicker().getServers())
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }
}

