package io.grpc.xds;

import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.LoadBalancerProvider;
import com.wosai.middleware.util.ForwardingLoadBalancerHelper;
import com.wosai.middleware.util.GracefulSwitchLoadBalancer;
import io.grpc.ConnectivityState;
import io.grpc.InternalLogId;
import io.grpc.Status;
import io.grpc.SynchronizationContext;
import io.grpc.internal.ProxylessServiceConfigUtil;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.google.common.base.Preconditions.checkNotNull;
import static io.grpc.ConnectivityState.CONNECTING;
import static io.grpc.ConnectivityState.IDLE;
import static io.grpc.ConnectivityState.READY;
import static io.grpc.ConnectivityState.TRANSIENT_FAILURE;
import static io.grpc.xds.ProxylessXdsServerPickers.BUFFER_PICKER;

/**
 * Load balancer for priority policy. A <em>priority</em> represents a logical entity within a
 * cluster for load balancing purposes.
 */
public class ProxylessPriorityLoadBalancer extends LoadBalancer {
    private final LoadBalancer.Helper helper;
    private final SynchronizationContext syncContext;
    private final ScheduledExecutorService executor;
    private final XdsSlf4jLogger logger;
    // Includes all active and deactivated children. Mutable. New entries are only added from priority
    // 0 up to the selected priority. An entry is only deleted 15 minutes after its deactivation.
    // Note that calling into a child can cause the child to call back into the LB policy and modify
    // the map. Therefore, copy values before looping over them.
    private final Map<String, ChildLbState> children = new HashMap<>();

    // Following fields are only null initially.
    private io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses;
    // List of priority names in order.
    private List<String> priorityNames;
    // Config for each priority.
    private Map<String, ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig> priorityConfigs;
    @Nullable
    private String currentPriority;
    private ConnectivityState currentConnectivityState;
    private LoadBalancer.ServerPicker currentPicker;
    // Set to true if currently in the process of handling resolved addresses.
    private boolean handlingResolvedAddresses;

    ProxylessPriorityLoadBalancer(LoadBalancer.Helper helper) {
        this.helper = checkNotNull(helper, "helper");
        syncContext = helper.getSynchronizationContext();
        executor = helper.getScheduledExecutorService();
        InternalLogId logId = InternalLogId.allocate("priority-lb", helper.getAuthority());
        logger = XdsSlf4jLogger.withLogId(ProxylessPriorityLoadBalancer.class, logId);
        logger.debug("Created");
    }

    @Override
    public boolean acceptResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses) {
        // ResolvedAddresses contains a list of EAG in which a group of SocketAddresses are included.
        // ResolvedAddresses{
        //   addresses=[
        //     0: [[/**********:8080]/{org.apache.skywalking.apm.dependencies.io.grpc.xds.AddressFilter.PATH_CHAIN_KEY=outbound|80|e2e-service-provider-red|e2e-service-provider.default.svc.cluster.local[child1], Locality{region=, zone=, subZone=}, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.localityWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.serverWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.locality=Locality{region=, zone=, subZone=}}],
        //     1: [[/**********:8080]/{org.apache.skywalking.apm.dependencies.io.grpc.xds.AddressFilter.PATH_CHAIN_KEY=outbound|80|e2e-service-provider-red|e2e-service-provider.default.svc.cluster.local[child1], Locality{region=, zone=, subZone=}, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.localityWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.serverWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.locality=Locality{region=, zone=, subZone=}}],
        //     2: [[/**********:8080]/{org.apache.skywalking.apm.dependencies.io.grpc.xds.AddressFilter.PATH_CHAIN_KEY=outbound|80|e2e-service-provider-red|e2e-service-provider.default.svc.cluster.local[child1], Locality{region=, zone=, subZone=}, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.localityWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.serverWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.locality=Locality{region=, zone=, subZone=}}]
        //   ],
        //   attributes={org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.xdsClientPool=org.apache.skywalking.apm.dependencies.io.grpc.xds.SharedXdsClientPoolProvider$RefCountedXdsClientObjectPool@4c5f2f57, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.callCounterProvider=org.apache.skywalking.apm.dependencies.io.grpc.xds.SharedCallCounterMap@3c3ec68e},
        //   loadBalancingPolicyConfig=ProxylessPriorityLoadBalancerProvider.PriorityLbConfig(
        //     childConfigs={outbound|80|e2e-service-provider-red|e2e-service-provider.default.svc.cluster.local[child1]=ProxylessPriorityLoadBalancerProvider.PriorityLbConfig.PriorityChildConfig(policySelection=ProxylessServiceConfigUtil.PolicySelection(provider=ProxylessClusterImplLoadBalancerProvider{policy=cluster_impl_experimental, priority=5, available=true}, config=ProxylessClusterImplLoadBalancerProvider.ClusterImplConfig(cluster=outbound|80|e2e-service-provider-red|e2e-service-provider.default.svc.cluster.local, edsServiceName=outbound|80|e2e-service-provider-red|e2e-service-provider.default.svc.cluster.local, lrsServerInfo=null, maxConcurrentRequests=null, tlsContext=null, dropCategories=[], childPolicy=ProxylessServiceConfigUtil.PolicySelection(provider=ProxylessWrrLocalityLoadBalancerProvider{policy=wrr_locality_experimental, priority=5, available=true}, config=ProxylessWrrLocalityLoadBalancer.WrrLocalityConfig(childPolicy=ProxylessServiceConfigUtil.PolicySelection(provider=Provider{policy=round_robin, priority=5, available=true}, config=no service config))))), ignoreReresolution=true)},
        //     priorities=[outbound|80|e2e-service-provider-red|e2e-service-provider.default.svc.cluster.local[child1]]
        //   )
        // }
        // TODO: in which cases, we would have multiple priorities?
        logger.trace("Received resolution result: {}", resolvedAddresses);
        this.resolvedAddresses = resolvedAddresses;
        ProxylessPriorityLoadBalancerProvider.PriorityLbConfig config = (ProxylessPriorityLoadBalancerProvider.PriorityLbConfig) resolvedAddresses.getLoadBalancingPolicyConfig();
        checkNotNull(config, "missing priority lb config");
        // update the current state with new priorities
        priorityNames = config.priorities;
        priorityConfigs = config.childConfigs;
        Set<String> prioritySet = new HashSet<>(config.priorities);
        ArrayList<String> childKeys = new ArrayList<>(children.keySet());
        // deactivate non-existing childLbs
        for (String priority : childKeys) {
            if (!prioritySet.contains(priority)) {
                ChildLbState childLbState = children.get(priority);
                if (childLbState != null) {
                    childLbState.deactivate();
                }
            }
        }
        handlingResolvedAddresses = true;
        // update existing childLbs
        for (String priority : priorityNames) {
            ChildLbState childLbState = children.get(priority);
            if (childLbState != null) {
                childLbState.updateResolvedAddresses();
            }
        }
        handlingResolvedAddresses = false;
        tryNextPriority();
        return true;
    }

    @Override
    public void handleNameResolutionError(Status error) {
        logger.warn("Received name resolution error: {}", error);
        boolean gotoTransientFailure = true;
        Collection<ChildLbState> childValues = new ArrayList<>(children.values());
        for (ChildLbState child : childValues) {
            if (priorityNames.contains(child.priority)) {
                child.lb.handleNameResolutionError(error);
                gotoTransientFailure = false;
            }
        }
        if (gotoTransientFailure) {
            updateOverallState(null, TRANSIENT_FAILURE, new ProxylessXdsServerPickers.ErrorPicker(error));
        }
    }

    @Override
    public void shutdown() {
        logger.debug("Shutdown");
        Collection<ChildLbState> childValues = new ArrayList<>(children.values());
        for (ChildLbState child : childValues) {
            child.tearDown();
        }
        children.clear();
    }

    private void tryNextPriority() {
        for (int i = 0; i < priorityNames.size(); i++) {
            String priority = priorityNames.get(i);
            if (!children.containsKey(priority)) { // create a new ChildLbState if not exists now
                ChildLbState child =
                        new ChildLbState(priority, priorityConfigs.get(priority).ignoreReresolution);
                children.put(priority, child);
                updateOverallState(priority, CONNECTING, BUFFER_PICKER);
                // Calling the child's updateResolvedAddresses() can result in tryNextPriority() being
                // called recursively. We need to be sure to be done with processing here before it is
                // called.
                child.updateResolvedAddresses();
                return; // Give priority i time to connect.
            }
            ChildLbState child = children.get(priority);
            child.reactivate();
            if (child.connectivityState.equals(READY) || child.connectivityState.equals(IDLE)) {
                logger.trace("Shifted to priority {}", priority);
                updateOverallState(priority, child.connectivityState, child.picker);
                for (int j = i + 1; j < priorityNames.size(); j++) {
                    String p = priorityNames.get(j);
                    if (children.containsKey(p)) {
                        children.get(p).deactivate();
                    }
                }
                return;
            }
            if (child.failOverTimer != null && child.failOverTimer.isPending()) {
                updateOverallState(priority, CONNECTING, BUFFER_PICKER);
                return; // Give priority i time to connect.
            }
            if (priority.equals(currentPriority) && child.connectivityState != TRANSIENT_FAILURE) {
                // If the current priority is not changed into TRANSIENT_FAILURE, keep using it.
                updateOverallState(priority, child.connectivityState, child.picker);
                return;
            }
        }
        // TODO(zdapeng): Include error details of each priority.
        logger.trace("All priority failed");
        String lastPriority = priorityNames.get(priorityNames.size() - 1);
        LoadBalancer.ServerPicker errorPicker = children.get(lastPriority).picker;
        updateOverallState(lastPriority, TRANSIENT_FAILURE, errorPicker);
    }

    private void updateOverallState(
            @Nullable String priority, ConnectivityState state, LoadBalancer.ServerPicker picker) {
        if (!Objects.equals(priority, currentPriority) || !state.equals(currentConnectivityState)
                || !picker.equals(currentPicker)) {
            currentPriority = priority;
            currentConnectivityState = state;
            currentPicker = picker;
            helper.updateBalancingState(state, picker);
        }
    }

    private final class ChildLbState {
        final String priority;
        final ChildHelper childHelper;
        final GracefulSwitchLoadBalancer lb;
        // Timer to fail over to the next priority if not connected in 10 sec. Scheduled only once at
        // child initialization.
        SynchronizationContext.ScheduledHandle failOverTimer;
        boolean seenReadyOrIdleSinceTransientFailure = false;
        // Timer to delay shutdown and deletion of the priority. Scheduled whenever the child is
        // deactivated.
        @Nullable
        SynchronizationContext.ScheduledHandle deletionTimer;
        @Nullable
        String policy;
        ConnectivityState connectivityState = CONNECTING;
        LoadBalancer.ServerPicker picker = BUFFER_PICKER;

        ChildLbState(final String priority, boolean ignoreReresolution) {
            this.priority = priority;
            childHelper = new ChildHelper(ignoreReresolution);
            lb = new GracefulSwitchLoadBalancer(childHelper);
            failOverTimer = syncContext.schedule(new FailOverTask(), 10, TimeUnit.SECONDS, executor);
            logger.trace("Priority created: {}", priority);
        }

        final class FailOverTask implements Runnable {
            @Override
            public void run() {
                if (deletionTimer != null && deletionTimer.isPending()) {
                    // The child is deactivated.
                    return;
                }
                picker = new ProxylessXdsServerPickers.ErrorPicker(
                        Status.UNAVAILABLE.withDescription("Connection timeout for priority " + priority));
                logger.trace("Priority {} failed over to next", priority);
                currentPriority = null; // reset currentPriority to guarantee failover happen
                tryNextPriority();
            }
        }

        /**
         * Called when the child becomes a priority that is or appears before the first READY one in the
         * {@code priorities} list, due to either config update or balancing state update.
         */
        void reactivate() {
            if (deletionTimer != null && deletionTimer.isPending()) {
                deletionTimer.cancel();
                logger.trace("Priority reactivated: {}", priority);
            }
        }

        /**
         * Called when either the child is removed by config update, or a higher priority becomes READY.
         */
        void deactivate() {
            if (deletionTimer != null && deletionTimer.isPending()) {
                return;
            }

            class DeletionTask implements Runnable {
                @Override
                public void run() {
                    tearDown();
                    children.remove(priority);
                }
            }

            deletionTimer = syncContext.schedule(new DeletionTask(), 15, TimeUnit.MINUTES, executor);
            logger.trace("Priority deactivated: {}", priority);
        }

        void tearDown() {
            if (failOverTimer.isPending()) {
                failOverTimer.cancel();
            }
            if (deletionTimer != null && deletionTimer.isPending()) {
                deletionTimer.cancel();
            }
            lb.shutdown();
            logger.trace("Priority deleted: {}", priority);
        }

        /**
         * Called either when the child is just created and in this case updated with the cached {@code
         * resolvedAddresses}, or when priority lb receives a new resolved addresses while the child
         * already exists.
         */
        void updateResolvedAddresses() {
            ProxylessPriorityLoadBalancerProvider.PriorityLbConfig config =
                    (ProxylessPriorityLoadBalancerProvider.PriorityLbConfig) resolvedAddresses.getLoadBalancingPolicyConfig();
            ProxylessServiceConfigUtil.PolicySelection childPolicySelection = config.childConfigs.get(priority).policySelection;
            // ProxylessClusterImplLoadBalancerProvider
            LoadBalancerProvider lbProvider = childPolicySelection.getProvider();
            String newPolicy = lbProvider.getPolicyName();
            if (!newPolicy.equals(policy)) {
                policy = newPolicy;
                lb.switchTo(lbProvider);
            }
            lb.handleResolvedAddresses(
                    resolvedAddresses.toBuilder()
                            .setAddresses(AddressFilter.filter(resolvedAddresses.getAddresses(), priority))
                            .setLoadBalancingPolicyConfig(childPolicySelection.getConfig())
                            .build());
        }

        final class ChildHelper extends ForwardingLoadBalancerHelper {
            private final boolean ignoreReresolution;

            ChildHelper(boolean ignoreReresolution) {
                this.ignoreReresolution = ignoreReresolution;
            }

            @Override
            public void refreshNameResolution() {
                if (!ignoreReresolution) {
                    delegate().refreshNameResolution();
                }
            }

            @Override
            public void updateBalancingState(final ConnectivityState newState,
                                             final LoadBalancer.ServerPicker newPicker) {
                if (!children.containsKey(priority)) {
                    return;
                }
                connectivityState = newState;
                picker = newPicker;

                if (deletionTimer != null && deletionTimer.isPending()) {
                    return;
                }
                if (newState.equals(CONNECTING)) {
                    if (!failOverTimer.isPending() && seenReadyOrIdleSinceTransientFailure) {
                        failOverTimer = syncContext.schedule(new FailOverTask(), 10, TimeUnit.SECONDS,
                                executor);
                    }
                } else if (newState.equals(READY) || newState.equals(IDLE)) {
                    seenReadyOrIdleSinceTransientFailure = true;
                    failOverTimer.cancel();
                } else if (newState.equals(TRANSIENT_FAILURE)) {
                    seenReadyOrIdleSinceTransientFailure = false;
                    failOverTimer.cancel();
                }

                // If we are currently handling newly resolved addresses, let's not try to reconfigure as
                // the address handling process will take care of that to provide an atomic config update.
                if (!handlingResolvedAddresses) {
                    tryNextPriority();
                }
            }

            @Override
            protected LoadBalancer.Helper delegate() {
                return helper;
            }
        }
    }
}
