package io.grpc.xds;

import com.google.common.collect.ImmutableMap;
import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.util.ForwardingLoadBalancerHelper;
import com.wosai.middleware.util.GracefulSwitchLoadBalancer;
import io.grpc.ConnectivityState;
import io.grpc.InternalLogId;
import io.grpc.Status;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.google.common.base.Preconditions.checkNotNull;
import static io.grpc.ConnectivityState.CONNECTING;
import static io.grpc.ConnectivityState.IDLE;
import static io.grpc.ConnectivityState.READY;
import static io.grpc.ConnectivityState.TRANSIENT_FAILURE;
import static io.grpc.xds.ProxylessXdsServerPickers.BUFFER_PICKER;

final class ProxylessWeightedTargetLoadBalancer extends LoadBalancer {
    private final XdsSlf4jLogger logger;
    private final Map<String, GracefulSwitchLoadBalancer> childBalancers = new HashMap<>();
    private final Map<String, ChildHelper> childHelpers = new HashMap<>();
    private final LoadBalancer.Helper helper;

    private Map<String, ProxylessWeightedTargetLoadBalancerProvider.WeightedPolicySelection> targets = ImmutableMap.of();
    // Set to true if currently in the process of handling resolved addresses.
    private boolean resolvingAddresses;

    public ProxylessWeightedTargetLoadBalancer(LoadBalancer.Helper helper) {
        this.helper = checkNotNull(helper, "helper");
        logger = XdsSlf4jLogger.withLogId(ProxylessWeightedTargetLoadBalancer.class,
                InternalLogId.allocate("weighted-target-lb", helper.getAuthority()));
        logger.debug("Created");
    }

    @Override
    public boolean acceptResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses) {
        try {
            resolvingAddresses = true;
            return acceptResolvedAddressesInternal(resolvedAddresses);
        } finally {
            resolvingAddresses = false;
        }
    }

    public boolean acceptResolvedAddressesInternal(io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses) {
        // ResolvedAddresses{
        //   addresses=[
        //     [[/**********:8080]/{org.apache.skywalking.apm.dependencies.io.grpc.xds.AddressFilter.PATH_CHAIN_KEY=Locality{region=, zone=, subZone=}, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.localityWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.serverWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.locality=Locality{region=, zone=, subZone=}}],
        //     [[/**********:8080]/{org.apache.skywalking.apm.dependencies.io.grpc.xds.AddressFilter.PATH_CHAIN_KEY=Locality{region=, zone=, subZone=}, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.localityWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.serverWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.locality=Locality{region=, zone=, subZone=}}],
        //     [[/**********:8080]/{org.apache.skywalking.apm.dependencies.io.grpc.xds.AddressFilter.PATH_CHAIN_KEY=Locality{region=, zone=, subZone=}, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.localityWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.serverWeight=3, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.locality=Locality{region=, zone=, subZone=}}]
        //   ],
        //   attributes={org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.xdsClientPool=org.apache.skywalking.apm.dependencies.io.grpc.xds.SharedXdsClientPoolProvider$RefCountedXdsClientObjectPool@4c5f2f57, org.apache.skywalking.apm.dependencies.io.grpc.xds.InternalXdsAttributes.callCounterProvider=org.apache.skywalking.apm.dependencies.io.grpc.xds.SharedCallCounterMap@3c3ec68e},
        //   loadBalancingPolicyConfig=ProxylessWeightedTargetLoadBalancerProvider.WeightedTargetConfig(targets={Locality{region=, zone=, subZone=}=ProxylessWeightedTargetLoadBalancerProvider.WeightedPolicySelection(weight=3, policySelection=ProxylessServiceConfigUtil.PolicySelection(provider=Provider{policy=round_robin, priority=5, available=true}, config=no service config))})
        // }
        logger.trace("Received resolution result: {}", resolvedAddresses);
        Object lbConfig = resolvedAddresses.getLoadBalancingPolicyConfig();
        checkNotNull(lbConfig, "missing weighted_target lb config");
        ProxylessWeightedTargetLoadBalancerProvider.WeightedTargetConfig weightedTargetConfig =
                (ProxylessWeightedTargetLoadBalancerProvider.WeightedTargetConfig) lbConfig;
        Map<String, ProxylessWeightedTargetLoadBalancerProvider.WeightedPolicySelection> newTargets =
                weightedTargetConfig.targets;
        for (String targetName : newTargets.keySet()) {
            ProxylessWeightedTargetLoadBalancerProvider.WeightedPolicySelection weightedChildLbConfig = newTargets.get(targetName);
            if (!targets.containsKey(targetName)) {
                ChildHelper childHelper = new ChildHelper(targetName);
                GracefulSwitchLoadBalancer childBalancer = new GracefulSwitchLoadBalancer(childHelper);
                childBalancer.switchTo(weightedChildLbConfig.policySelection.getProvider());
                childHelpers.put(targetName, childHelper);
                childBalancers.put(targetName, childBalancer);
            } else if (!weightedChildLbConfig.policySelection.getProvider().equals( // Provider may be rr
                    targets.get(targetName).policySelection.getProvider())) {
                childBalancers.get(targetName)
                        .switchTo(weightedChildLbConfig.policySelection.getProvider());
            }
        }
        targets = newTargets;
        for (String targetName : targets.keySet()) {
            childBalancers.get(targetName).handleResolvedAddresses(
                    resolvedAddresses.toBuilder()
                            .setAddresses(AddressFilter.filter(resolvedAddresses.getAddresses(), targetName))
                            .setLoadBalancingPolicyConfig(targets.get(targetName).policySelection.getConfig())
                            .build());
        }

        // Cleanup removed targets.
        // TODO(zdapeng): cache removed target for 15 minutes.
        for (String targetName : childBalancers.keySet()) {
            if (!targets.containsKey(targetName)) {
                childBalancers.get(targetName).shutdown();
            }
        }
        childBalancers.keySet().retainAll(targets.keySet());
        childHelpers.keySet().retainAll(targets.keySet());
        updateOverallBalancingState();
        return true;
    }

    @Override
    public void shutdown() {
        logger.debug("Shutdown");
        for (LoadBalancer childBalancer : childBalancers.values()) {
            childBalancer.shutdown();
        }
        childBalancers.clear();
    }

    private void updateOverallBalancingState() {
        List<ProxylessWeightedRandomPicker.WeightedChildPicker> childPickers = new ArrayList<>();

        ConnectivityState overallState = null;
        List<ProxylessWeightedRandomPicker.WeightedChildPicker> errorPickers = new ArrayList<>();
        for (String name : targets.keySet()) {
            ChildHelper childHelper = childHelpers.get(name);
            ConnectivityState childState = childHelper.currentState;
            overallState = aggregateState(overallState, childState);
            int weight = targets.get(name).weight;
            if (READY == childState) {
                childPickers.add(new ProxylessWeightedRandomPicker.WeightedChildPicker(weight, childHelper.currentPicker));
            } else if (TRANSIENT_FAILURE == childState) {
                errorPickers.add(new ProxylessWeightedRandomPicker.WeightedChildPicker(weight, childHelper.currentPicker));
            }
        }

        LoadBalancer.ServerPicker picker;
        if (childPickers.isEmpty()) {
            if (overallState == TRANSIENT_FAILURE) {
                picker = new ProxylessWeightedRandomPicker(errorPickers);
            } else {
                picker = BUFFER_PICKER;
            }
        } else {
            picker = new ProxylessWeightedRandomPicker(childPickers);
        }

        if (overallState != null) {
            helper.updateBalancingState(overallState, picker);
        }
    }

    @Override
    public void handleNameResolutionError(Status error) {
        logger.warn("Received name resolution error: {}", error);
        if (childBalancers.isEmpty()) {
            helper.updateBalancingState(TRANSIENT_FAILURE, new ProxylessXdsServerPickers.ErrorPicker(error));
        }
        for (LoadBalancer childBalancer : childBalancers.values()) {
            childBalancer.handleNameResolutionError(error);
        }
    }

    @Nullable
    private static ConnectivityState aggregateState(
            @Nullable ConnectivityState overallState, ConnectivityState childState) {
        if (overallState == null) {
            return childState;
        }
        if (overallState == READY || childState == READY) {
            return READY;
        }
        if (overallState == CONNECTING || childState == CONNECTING) {
            return CONNECTING;
        }
        if (overallState == IDLE || childState == IDLE) {
            return IDLE;
        }
        return overallState;
    }

    @Override
    public boolean canHandleEmptyAddressListFromNameResolution() {
        return true;
    }

    private final class ChildHelper extends ForwardingLoadBalancerHelper {
        String name;
        ConnectivityState currentState = CONNECTING;
        LoadBalancer.ServerPicker currentPicker = BUFFER_PICKER;

        private ChildHelper(String name) {
            this.name = name;
        }

        @Override
        public void updateBalancingState(final ConnectivityState newState,
                                         final LoadBalancer.ServerPicker newPicker) {
            currentState = newState;
            currentPicker = newPicker;

            // If we are already in the process of resolving addresses, the overall balancing state
            // will be updated at the end of it, and we don't need to trigger that update here.
            if (!resolvingAddresses && childBalancers.containsKey(name)) {
                updateOverallBalancingState();
            }
        }

        @Override
        protected LoadBalancer.Helper delegate() {
            return helper;
        }
    }
}
