/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.wosai.middleware.dubbo;

import com.google.common.collect.Maps;
import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.proxyless.ManagedProxylessService;
import com.wosai.middleware.proxyless.ManagedServiceBuilder;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.logger.ErrorTypeAwareLogger;
import org.apache.dubbo.common.logger.LoggerFactory;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Protocol;
import org.apache.dubbo.rpc.cluster.Directory;
import org.apache.dubbo.rpc.cluster.SingleRouterChain;
import org.apache.dubbo.rpc.cluster.directory.AbstractDirectory;
import org.apache.dubbo.rpc.cluster.router.state.BitList;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.apache.dubbo.common.constants.CommonConstants.REMOTE_APPLICATION_KEY;

public class XdsDirectory<T> extends AbstractDirectory<T> {

    private final URL oriUrl;

    private final Class<T> serviceType;

    private final String[] applicationNames;

    private final String protocolName;

    private Protocol protocol;

    private int port = 50051;

    private final Map<String, Map<String, Invoker<T>>> invokerCache = new ConcurrentHashMap<>();

    private final Map<String, ManagedProxylessService> managedProxylessServiceMap = new ConcurrentHashMap<>();

    private static final ErrorTypeAwareLogger logger = LoggerFactory.getErrorTypeAwareLogger(XdsDirectory.class);

    public XdsDirectory(Directory<T> directory) {
        super(directory.getUrl().putAttribute("refer",
                        Maps.immutableEntry("CONSUMER_URL", directory.getConsumerUrl().toString())),
                null, true);
        this.serviceType = directory.getInterface();
        this.oriUrl = directory.getConsumerUrl();
        this.applicationNames = oriUrl.getParameter("provided-by").split(",");
        this.protocolName = oriUrl.getParameter("protocol", "tri");
        this.protocol = oriUrl.getOrDefaultApplicationModel()
                .getExtensionLoader(Protocol.class)
                .getExtension(protocolName);
        if (Objects.equals(protocolName, "http")) {
            port = 80;
        }
        super.routerChain = directory.getRouterChain();
        if (!XdsManager.xdsAvailable()) {
            // 走直连的模式
            List<Invoker<T>> invokers = new ArrayList<>();
            for (String applicationName : applicationNames) {
                invokers.add(getDirectDnsInvoker(applicationName, port));
            }
            BitList<Invoker<T>> result = new BitList<>(invokers);
            setInvokers(result);
            refreshRouter(result.clone(), () -> setInvokers(result));
            return;
        }
        for (String applicationName : applicationNames) {
            final String key = "xds:///" + applicationName + ":" + port;
            final ManagedProxylessService service = XdsManager.getOrRegistryManagedService(key,
                    () -> {
                        ManagedProxylessService instance = ManagedServiceBuilder.forAddress("xds:///" + applicationName, port).build();
                        try {
                            boolean isReady = instance.awaitReady(
                                    LoadBalancer.PickServerArgs.create("/", null, new HashMap<>(), Collections.emptyMap()),
                                    20_000L, 500L, TimeUnit.MILLISECONDS);

                            logger.info("{} is ready = {}", key, isReady);
                            if (isReady) {
                                registerWatcher(instance, applicationName); // 注册 Watcher
                                updateAndSetInvokers(instance.getServers(), applicationName); // 初始加载
                            } else {
                                logger.warn("xDS not ready for {}, falling back to DNS", applicationName);
                                updateAndSetInvokers(null, applicationName); // 初始加载
                            }
                        } catch (InterruptedException ex) {
                            logger.error("sleep interrupted", ex);
                        }
                        return instance;
                    });
            managedProxylessServiceMap.put(key, service);
        }
    }

    private void registerWatcher(ManagedProxylessService instance, String appName) {
        instance.addWatcher(servers -> {
            logger.info("EDS update received for {}, updating invokers size = {}", appName, servers.size());
            updateAndSetInvokers(servers, appName);
        });
    }

    // Noteycj : RegistryDirectory.refreshInvoker(urls), 对于已下线的需要销毁
    private void updateAndSetInvokers(List<LoadBalancer.Server> servers, String appName) {
        List<Invoker<T>> invokers = new ArrayList<>();
        if (CollectionUtils.isEmpty(servers)) {
            logger.warn("EDS update received empty server list for {}, falling back to DNS", appName);
            invokers.add(getDirectDnsInvoker(appName, port));
        } else {
            // 获取或初始化该应用的缓存
            invokerCache.putIfAbsent(appName, new ConcurrentHashMap<>());
            Map<String, Invoker<T>> appCache = invokerCache.get(appName);

            Map<String, String> params = new HashMap<>(oriUrl.getParameters());
            params.put(REMOTE_APPLICATION_KEY, appName);

            // 新 server keys
            List<String> newKeys = new ArrayList<>();
            servers.forEach(server -> {
                String key = server.addr() + ":" + server.port();
                newKeys.add(key);
                appCache.computeIfAbsent(key, k -> {
                    URL url = new URL(protocolName, server.addr(), server.port(), serviceType.getName(), params);
                    return protocol.refer(serviceType, url);
                });
                invokers.add(appCache.get(key));
            });
            // 找出需要删除的旧 keys
            List<String> oldKeys = new ArrayList<>(appCache.keySet());
            oldKeys.removeAll(newKeys);

            // 销毁不再需要的 Invoker
            oldKeys.forEach(key -> {
                Invoker<T> invoker = appCache.remove(key);
                if (invoker != null && invoker.isAvailable()) {
                    invoker.destroy();
                    logger.info("Destroyed invoker for {}", key);
                }
            });
        }
        List<Invoker<T>> allInvokers = new ArrayList<>();
        for (String name : applicationNames) {
            Map<String, Invoker<T>> cache = invokerCache.get(name);
            if (cache != null) {
                allInvokers.addAll(cache.values());
            }
        }
        BitList<Invoker<T>> result = new BitList<>(allInvokers);
        setInvokers(result);
        refreshRouter(result.clone(), () -> setInvokers(result));
    }

    private Invoker<T> getDirectDnsInvoker(String appName, int port) {
        String dnsName = appName + ".sqb.svc.cluster.local";
        invokerCache.putIfAbsent(appName, new ConcurrentHashMap<>());
        Map<String, Invoker<T>> appCache = invokerCache.get(appName);
        appCache.computeIfAbsent(dnsName, k -> {
            URL url = new URL(protocolName, dnsName, port, serviceType.getName(), oriUrl.getParameters());
            return protocol.refer(serviceType, url);
        });
        return appCache.get(dnsName);
    }

    public Protocol getProtocol() {
        return protocol;
    }

    public void setProtocol(Protocol protocol) {
        this.protocol = protocol;
    }

    @Override
    public Class<T> getInterface() {
        return serviceType;
    }

    public List<Invoker<T>> doList(
            SingleRouterChain<T> singleRouterChain, BitList<Invoker<T>> invokers, Invocation invocation) {
        Collection<ManagedProxylessService> values = managedProxylessServiceMap.values();
        List<Invoker<T>> result = new ArrayList<>();
        if (!values.isEmpty()) {
            int randomIndex = ThreadLocalRandom.current().nextInt(values.size());
            ManagedProxylessService randomValue = (ManagedProxylessService) values.toArray()[randomIndex];
            // Noteycj: 这里没办法获取不到@JsonRpcService注解定义的path路径
            final LoadBalancer.PickResult  pickResult = randomValue.selectServer(LoadBalancer.PickServerArgs.create("/", invocation.getMethodName(), invocation.getAttachments(), Collections.emptyMap()));
            if (pickResult.getStatus().isOk() && pickResult.getServer() != null) {
                LoadBalancer.Server server = pickResult.getServer();
                // TODO invokerCache扁平化
                result.addAll(invokerCache.values().stream()
                        .map(map -> map.get(server.addr() + ":" + server.port()))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isEmpty(result)) {
            result = singleRouterChain.route(this.getConsumerUrl(), invokers, invocation);
        }
        return result == null ? BitList.emptyList() : result;
    }

    @Override
    public List<Invoker<T>> getAllInvokers() {
        return super.getInvokers();
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public void destroy() {
        super.destroy();
    }

    @Override
    public void setInvokers(BitList<Invoker<T>> invokers) {
        super.setInvokers(invokers);
    }

    public Invoker<T> initInvoker(String addr, int port, String appName) {
        Map<String, String> params = new HashMap<>(oriUrl.getParameters());
        params.put(REMOTE_APPLICATION_KEY, appName);
        URL url = new URL(protocolName, addr, port, serviceType.getName(), params);
        return protocol.refer(serviceType, url);
    }
}
