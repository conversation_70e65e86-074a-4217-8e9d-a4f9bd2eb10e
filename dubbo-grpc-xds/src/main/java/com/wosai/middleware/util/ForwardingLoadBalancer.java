package com.wosai.middleware.util;

import com.wosai.middleware.LoadBalancer;
import io.grpc.Status;

public abstract class ForwardingLoadBalancer extends LoadBalancer {
    protected abstract LoadBalancer delegate();

    @Override
    public void handleResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses) {
        delegate().handleResolvedAddresses(resolvedAddresses);
    }

    @Override
    public void handleNameResolutionError(Status error) {
        delegate().handleNameResolutionError(error);
    }

    @Override
    public void shutdown() {
        delegate().shutdown();
    }

    @Override
    public boolean canHandleEmptyAddressListFromNameResolution() {
        return delegate().canHandleEmptyAddressListFromNameResolution();
    }
}
