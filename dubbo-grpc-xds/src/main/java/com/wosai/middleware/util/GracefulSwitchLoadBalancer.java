package com.wosai.middleware.util;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import com.wosai.middleware.LoadBalancer;
import io.grpc.ConnectivityState;
import io.grpc.Status;

import javax.annotation.Nullable;

import static com.google.common.base.Preconditions.checkNotNull;
import static com.google.common.base.Preconditions.checkState;

/**
 * A load balancer that gracefully swaps to a new lb policy. If the channel is currently in a state
 * other than READY, the new policy will be swapped into place immediately.  Otherwise, the channel
 * will keep using the old policy until the new policy reports READY or the old policy exits READY.
 *
 * <p>The balancer must {@link #switchTo(io.grpc.LoadBalancer.Factory) switch to} a policy prior to {@link
 * io.grpc.LoadBalancer#handleResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses) handling resolved addresses} for the
 * first time.
 */
public class GracefulSwitchLoadBalancer extends ForwardingLoadBalancer {
    private final LoadBalancer defaultBalancer = new LoadBalancer() {
        @Override
        public void handleResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses) {
            //  Most LB policies using this class will receive child policy configuration within the
            //  service config, so they are naturally calling switchTo() just before
            //  handleResolvedAddresses(), within their own handleResolvedAddresses(). If switchTo() is
            //  not called immediately after construction that does open up potential for bugs in the
            //  parent policies, where they fail to call switchTo(). So we will use the exception to try
            //  to notice those bugs quickly, as it will fail very loudly.
            throw new IllegalStateException(
                    "GracefulSwitchLoadBalancer must switch to a load balancing policy before handling"
                            + " ResolvedAddresses");
        }

        @Override
        public void handleNameResolutionError(final Status error) {
            class ErrorPicker extends ServerPicker {
                @Override
                public PickResult pickServer(PickServerArgs args) {
                    return PickResult.withError(error);
                }

                @Override
                public String toString() {
                    return MoreObjects.toStringHelper(ErrorPicker.class).add("error", error).toString();
                }
            }

            helper.updateBalancingState(
                    ConnectivityState.TRANSIENT_FAILURE,
                    new ErrorPicker());
        }

        @Override
        public void shutdown() {
        }
    };

    @VisibleForTesting
    static final ServerPicker BUFFER_PICKER = new ServerPicker() {
        @Override
        public PickResult pickServer(PickServerArgs args) {
            return PickResult.withNoResult();
        }

        @Override
        public String toString() {
            return "BUFFER_PICKER";
        }
    };

    private final Helper helper;
    @Nullable
    private Factory currentBalancerFactory;
    private LoadBalancer currentLb = defaultBalancer;
    @Nullable
    private Factory pendingBalancerFactory;
    private LoadBalancer pendingLb = defaultBalancer;
    private ConnectivityState pendingState;
    private ServerPicker pendingPicker;
    private boolean currentLbIsReady;

    public GracefulSwitchLoadBalancer(Helper helper) {
        this.helper = checkNotNull(helper, "helper");
    }

    /**
     * Gracefully switch to a new policy defined by the given factory, if the given factory isn't
     * equal to the current one.
     */
    public void switchTo(Factory newBalancerFactory) {
        checkNotNull(newBalancerFactory, "newBalancerFactory");

        if (newBalancerFactory.equals(pendingBalancerFactory)) {
            return;
        }
        pendingLb.shutdown();
        pendingLb = defaultBalancer;
        pendingBalancerFactory = null;
        pendingState = ConnectivityState.CONNECTING;
        pendingPicker = BUFFER_PICKER;

        if (newBalancerFactory.equals(currentBalancerFactory)) {
            return;
        }

        class PendingHelper extends ForwardingLoadBalancerHelper {
            LoadBalancer lb;

            @Override
            protected Helper delegate() {
                return helper;
            }

            @Override
            public void updateBalancingState(ConnectivityState newState, ServerPicker newPicker) {
                if (lb == pendingLb) {
                    checkState(currentLbIsReady, "there's pending lb while current lb has been out of READY");
                    pendingState = newState;
                    pendingPicker = newPicker;
                    if (newState == ConnectivityState.READY) {
                        swap();
                    }
                } else if (lb == currentLb) {
                    currentLbIsReady = newState == ConnectivityState.READY;
                    if (!currentLbIsReady && pendingLb != defaultBalancer) {
                        swap(); // current policy exits READY, so swap
                    } else {
                        helper.updateBalancingState(newState, newPicker);
                    }
                }
            }
        }

        PendingHelper pendingHelper = new PendingHelper();
        pendingHelper.lb = newBalancerFactory.newLoadBalancer(pendingHelper);
        pendingLb = pendingHelper.lb;
        pendingBalancerFactory = newBalancerFactory;
        if (!currentLbIsReady) {
            swap(); // the old policy is not READY at the moment, so swap to the new one right now
        }
    }

    private void swap() {
        helper.updateBalancingState(pendingState, pendingPicker);
        currentLb.shutdown();
        currentLb = pendingLb;
        currentBalancerFactory = pendingBalancerFactory;
        pendingLb = defaultBalancer;
        pendingBalancerFactory = null;
    }

    @Override
    protected LoadBalancer delegate() {
        return pendingLb == defaultBalancer ? currentLb : pendingLb;
    }

    @Override
    public void shutdown() {
        pendingLb.shutdown();
        currentLb.shutdown();
    }
}
