package com.wosai.middleware.util;

import com.wosai.middleware.LoadBalancer;
import com.wosai.middleware.LoadBalancerProvider;
import io.grpc.NameResolver;

import java.util.Map;

public class SecretRoundRobinLoadBalancerProvider {
    private SecretRoundRobinLoadBalancerProvider() {
    }

    public static final class Provider extends LoadBalancerProvider {
        private static final String NO_CONFIG = "no service config";

        @Override
        public LoadBalancer newLoadBalancer(LoadBalancer.Helper helper) {
            return new RoundRobinLoadBalancer(helper);
        }

        @Override
        public boolean isAvailable() {
            return true;
        }

        @Override
        public int getPriority() {
            return 5;
        }

        @Override
        public String getPolicyName() {
            return "round_robin";
        }

        @Override
        public NameResolver.ConfigOrError parseLoadBalancingPolicyConfig(Map<String, ?> rawLoadBalancingPolicyConfig) {
            return NameResolver.ConfigOrError.fromConfig(NO_CONFIG);
        }
    }
}
