package com.wosai.middleware;

import com.google.auto.value.AutoValue;
import com.google.common.base.Preconditions;
import io.grpc.CallOptions;
import io.grpc.ConnectivityState;
import io.grpc.EquivalentAddressGroup;
import io.grpc.ExperimentalApi;
import io.grpc.NameResolver;
import io.grpc.ProxylessNameResolverRegistry;
import io.grpc.Status;
import io.grpc.SynchronizationContext;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.concurrent.Immutable;
import java.net.InetSocketAddress;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;

import static com.google.common.base.Preconditions.checkNotNull;

public abstract class LoadBalancer {
    private int recursionCount;

    public void handleResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses) {
        if (recursionCount++ == 0) {
            // Note that the information about the addresses actually being accepted will be lost
            // if you rely on this method for backward compatibility.
            acceptResolvedAddresses(resolvedAddresses);
        }
        recursionCount = 0;
    }

    /**
     * Accepts newly resolved addresses from the name resolution system. The {@link
     * EquivalentAddressGroup} addresses should be considered equivalent but may be flattened into a
     * single list if needed.
     *
     * <p>Implementations can choose to reject the given addresses by returning {@code false}.
     *
     * <p>Implementations should not modify the given {@code addresses}.
     *
     * @param resolvedAddresses the resolved server addresses, attributes, and config.
     * @return {@code true} if the resolved addresses were accepted. {@code false} if rejected.
     * @since 1.49.0
     */
    public boolean acceptResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses resolvedAddresses) {
        if (resolvedAddresses.getAddresses().isEmpty()
                && !canHandleEmptyAddressListFromNameResolution()) {
            handleNameResolutionError(Status.UNAVAILABLE.withDescription(
                    "NameResolver returned no usable address. addrs=" + resolvedAddresses.getAddresses()
                            + ", attrs=" + resolvedAddresses.getAttributes()));
            return false;
        } else {
            if (recursionCount++ == 0) {
                handleResolvedAddresses(resolvedAddresses);
            }
            recursionCount = 0;

            return true;
        }
    }

    /**
     * The channel asks the load-balancer to shutdown.  No more methods on this class will be called
     * after this method.  The implementation should shutdown all Subchannels and OOB channels, and do
     * any other cleanup as necessary.
     *
     * @since 1.2.0
     */
    public abstract void shutdown();

    /**
     * Handles an error from the name resolution system.
     *
     * @param error a non-OK status
     * @since 1.2.0
     */
    public abstract void handleNameResolutionError(Status error);

    /**
     * Whether this LoadBalancer can handle empty address group list to be passed to {@link
     * #handleResolvedAddresses(io.grpc.LoadBalancer.ResolvedAddresses)}.  The default implementation returns
     * {@code false}, meaning that if the NameResolver returns an empty list, the Channel will turn
     * that into an error and call {@link #handleNameResolutionError}.  LoadBalancers that want to
     * accept empty lists should override this method and return {@code true}.
     *
     * <p>This method should always return a constant value.  It's not specified when this will be
     * called.
     */
    public boolean canHandleEmptyAddressListFromNameResolution() {
        return false;
    }

    public abstract static class Factory {
        public Factory() {
        }

        public abstract LoadBalancer newLoadBalancer(Helper helper);
    }

    public abstract static class Helper {
        public Helper() {
        }

        public Server createServer(io.grpc.LoadBalancer.CreateSubchannelArgs args) {
            throw new UnsupportedOperationException();
        }

        public void refreshNameResolution() {
            throw new UnsupportedOperationException();
        }

        @ExperimentalApi("https://github.com/grpc/grpc-java/issues/8088")
        public void ignoreRefreshNameResolutionCheck() {
        }

        public SynchronizationContext getSynchronizationContext() {
            throw new UnsupportedOperationException();
        }

        public ScheduledExecutorService getScheduledExecutorService() {
            throw new UnsupportedOperationException();
        }

        /**
         * Set a new state with a new picker to the channel.
         *
         * <p>When a new picker is provided via {@code updateBalancingState()}, the channel will apply
         * the picker on all buffered RPCs, by calling {@link io.grpc.LoadBalancer.SubchannelPicker#pickSubchannel(
         *io.grpc.LoadBalancer.PickSubchannelArgs)}.
         *
         * <p>The channel will hold the picker and use it for all RPCs, until {@code
         * updateBalancingState()} is called again and a new picker replaces the old one.  If {@code
         * updateBalancingState()} has never been called, the channel will buffer all RPCs until a
         * picker is provided.
         *
         * <p>It should be called from the Synchronization Context.  Currently will log a warning if
         * violated.  It will become an exception eventually.  See <a
         * href="https://github.com/grpc/grpc-java/issues/5015">#5015</a> for the background.
         *
         * <p>The passed state will be the channel's new state. The SHUTDOWN state should not be passed
         * and its behavior is undefined.
         */
        public abstract void updateBalancingState(
                @Nonnull ConnectivityState newState, @Nonnull ServerPicker newPicker);

        public abstract String getAuthority();

        public NameResolver.Args getNameResolverArgs() {
            throw new UnsupportedOperationException();
        }

        public ProxylessNameResolverRegistry getNameResolverRegistry() {
            throw new UnsupportedOperationException();
        }
    }

    public static abstract class ServerPicker {
        public abstract PickResult pickServer(PickServerArgs args);
        // Noteycj add getServers()
        public  List<Server> getServers() {
            return Collections.emptyList();
        }
    }

    @Immutable
    @Getter
    @ToString
    @EqualsAndHashCode
    public static final class PickResult {
        private static final PickResult NO_RESULT = new PickResult(null, Status.OK, false);

        @Nullable
        private final Server server;
        // An error to be propagated to the application
        // Or OK if there is no error.
        private final Status status;
        // True if the result is created by withDrop()
        private final boolean drop;

        private PickResult(@Nullable Server server, Status status, boolean drop) {
            this.server = server;
            this.status = checkNotNull(status, "status");
            this.drop = drop;
        }

        public static PickResult withServer(Server server) {
            return new PickResult(server, Status.OK, false);
        }

        /**
         * A decision to report a connectivity error to the RPC.  If the RPC is {@link
         * CallOptions#withWaitForReady wait-for-ready}, it will stay buffered.  Otherwise, it will fail
         * with the given error.
         *
         * @param error the error status.  Must not be OK.
         */
        public static PickResult withError(Status error) {
            Preconditions.checkArgument(!error.isOk(), "error status shouldn't be OK");
            return new PickResult(null, error, false);
        }

        /**
         * A decision to fail an RPC immediately.  This is a final decision and will ignore retry
         * policy.
         *
         * @param status the status with which the RPC will fail.  Must not be OK.
         */
        public static PickResult withDrop(Status status) {
            Preconditions.checkArgument(!status.isOk(), "drop status shouldn't be OK");
            return new PickResult(null, status, true);
        }

        public static PickResult withNoResult() {
            return NO_RESULT;
        }
    }

    @AutoValue
    public abstract static class PickServerArgs {
        /**
         * HTTP Path in URI to be used for request routing.
         *
         * @return path as a string
         */
        public abstract String path();

        /**
         * RPC Method is additional information for the runtime protocol.
         *
         * @return name of the RPC method.
         * For JSON-RPC, it is the value of the "method" key in the payload.
         */
        @Nullable
        public abstract String rpcMethod();

        /**
         * HTTP headers to be used for request routing.
         * For example, we use `x-env-flag` to route requests to different version of services.
         */
        public abstract Map<String, String> getHeaders();

        public abstract Map<String, String> getParams();

        public abstract CallOptions getCallOptions();

        public static PickServerArgs create(String path, @Nullable String rpcMethod, Map<String, String> headers, Map<String, String> params) {
            return new AutoValue_LoadBalancer_PickServerArgs(path, rpcMethod, headers, params, CallOptions.DEFAULT);
        }

        public static PickServerArgs create(String path, @Nullable String rpcMethod, Map<String, String> headers, Map<String, String> params, CallOptions callOptions) {
            return new AutoValue_LoadBalancer_PickServerArgs(path, rpcMethod, headers, params, callOptions);
        }

        public PickServerArgs withNewCallOptions(CallOptions newCallOptions) {
            return new AutoValue_LoadBalancer_PickServerArgs(this.path(), this.rpcMethod(), this.getHeaders(), this.getParams(), newCallOptions);
        }
    }

    /**
     * Server is the representation of a server instance.
     */
    @AutoValue
    public abstract static class Server {
        /**
         * Scheme of the server. Either `http` or `https`.
         * For RPC scenarios, it is usually an `http` protocol.
         */
        public abstract String scheme();

        /**
         * Host of the server.
         * It is possible an ipv4 endpoint or a DNS name.
         */
        public abstract String addr();

        /**
         * Port of the server.
         */
        public abstract int port();

        public String authority() {
            return addr() + ":" + port();
        }

        public static Builder builder() {
            return new AutoValue_LoadBalancer_Server.Builder().setPort(80).setScheme("http");
        }

        protected abstract Builder toBuilder();

        public final Server withHost(String host) {
            return toBuilder().setAddr(host).build();
        }

        @AutoValue.Builder
        public abstract static class Builder {
            public abstract Builder setScheme(String scheme);

            public abstract Builder setAddr(String addr);

            public abstract Builder setPort(int port);

            public abstract Server build();
        }

        public static Server createHttpServer(String addr, int port) {
            return builder().setAddr(addr).setScheme("http").setPort(port).build();
        }

        public static Server createHttpFromTarget(String target, int defaultPort) {
            try {
                URI uri = new URI(null, target, null, null, null);
                int port = uri.getPort() == -1 ? defaultPort : uri.getPort();
                return builder()
                        .setAddr(uri.getHost())
                        .setScheme("http")
                        .setPort(port).build();
            } catch (URISyntaxException ex) {
                throw new IllegalArgumentException("fail to parse target", ex);
            }
        }

        public static Server createHttpFromSocketAddress(InetSocketAddress socketAddress) {
            if (socketAddress.isUnresolved()) {
                return builder().setScheme("http")
                        .setAddr(socketAddress.getHostName())
                        .setPort(socketAddress.getPort()).build();
            }
            return builder().setScheme("http")
                    .setAddr(socketAddress.getAddress().getHostAddress())
                    .setPort(socketAddress.getPort()).build();
        }
    }
}
