<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hera</artifactId>
        <groupId>com.wosai.middleware</groupId>
        <version>1.14.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hera-plugins</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>tomcat-7.x-8.x-plugin</module>
        <module>tomcat-10x-plugin</module>
        <module>mysql-common</module>
        <module>jdbc-commons</module>
        <module>mysql-8.x-plugin</module>
        <module>jsonrpc4j-plugin</module>
        <module>httpclient-4.x-plugin</module>
        <module>jedis-plugins</module>
        <module>lettuce-5.x-plugin</module>
        <module>jetty-plugin</module>
        <module>mysql-5.x-plugin</module>
        <module>spring-plugins</module>
        <module>kafka-plugin</module>
        <module>kafka-commons</module>
        <module>mongodb-3.x-plugin</module>
        <module>mongodb-4.x-plugin</module>
        <module>feign-default-http-9.x-plugin</module>
        <module>sharding-sphere-4.1.0-plugin</module>
        <module>hikaricp-plugin</module>
        <module>alibaba-druid-plugin</module>
        <module>okhttp-common</module>
        <module>okhttp-4.x-plugin</module>
        <module>okhttp-3.x-plugin</module>
        <module>sharding-sphere-4.0.x-plugin</module>
        <module>ehcache-2.x-plugin</module>
        <module>ehcache-3.x-plugin</module>
        <module>caffeine-2.x-plugin</module>
        <module>elasticsearch-common</module>
        <module>elasticsearch-6.x-plugin</module>
        <module>elasticsearch-7.x-plugin</module>
        <module>log4j-2.x-plugin</module>
        <module>redisson-3.26-plus-metrics-plugin</module>
        <module>redisson-3.18-pre-metrics-plugin</module>
        <module>redisson-3.x-plugin</module>
        <module>httpasyncclient-4.x-plugin</module>
        <module>tomcat-jdbc-plugin</module>
        <module>dbcp-2.x-plugin</module>
        <module>guava-eventbus-plugin</module>
        <module>httpclient-commons</module>
        <module>netty-socketio-plugin</module>
        <module>commons-pool-2.x-plugin</module>
        <module>dubbo-3.x-plugin</module>
        <module>lettuce-metrics-plugin</module>
        <module>dubbo-xds-plugin</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sdk.plugin.related.dir/>
        <agent.package.dest.dir>${project.build.directory}${sdk.plugin.related.dir}/../../target/hera-agent
        </agent.package.dest.dir>
        <plugin.dest.dir>${agent.package.dest.dir}/plugins</plugin.dest.dir>
        <apache-httpclient.version>4.3</apache-httpclient.version>
        <mongo-java-driver.version>3.11.0</mongo-java-driver.version>
        <mongodb-driver-sync.version>4.1.0</mongodb-driver-sync.version>
        <jetty-server.version>9.4.25.v20191220</jetty-server.version>
        <javax-servlet-api.version>3.0.1</javax-servlet-api.version>
        <kafka-clients.version>3.2.3</kafka-clients.version>
        <redisson.version>3.20.0</redisson.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-agent-core</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-agent-core</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>java-agent-util</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-util</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-test-tools</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- test suite -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-test-toolkit</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <shadedArtifactAttached>false</shadedArtifactAttached>
                            <createDependencyReducedPom>true</createDependencyReducedPom>
                            <createSourcesJar>true</createSourcesJar>
                            <shadeSourcesContent>true</shadeSourcesContent>
                            <relocations>
                                <relocation>
                                    <pattern>${shade.net.bytebuddy.source}</pattern>
                                    <shadedPattern>${shade.net.bytebuddy.target}</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>${shade.org.slf4j.source}</pattern>
                                    <shadedPattern>${shade.org.slf4j.target}</shadedPattern>
                                </relocation>
                            </relocations>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <tasks>
                                <taskdef resource="net/sf/antcontrib/antcontrib.properties"
                                         classpathref="maven.runtime.classpath"/>
                                <if>
                                    <equals arg1="${project.packaging}" arg2="jar"/>
                                    <then>
                                        <mkdir dir="${plugin.dest.dir}"/>
                                        <copy file="${project.build.directory}/${project.artifactId}-${project.version}.jar"
                                              tofile="${plugin.dest.dir}/${project.artifactId}-${project.version}.jar"
                                              overwrite="true"/>
                                    </then>
                                </if>
                            </tasks>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>ant-contrib</groupId>
                        <artifactId>ant-contrib</artifactId>
                        <version>${ant-contrib.version}</version>
                        <exclusions>
                            <exclusion>
                                <groupId>ant</groupId>
                                <artifactId>ant</artifactId>
                            </exclusion>
                        </exclusions>
                    </dependency>
                    <dependency>
                        <groupId>org.apache.ant</groupId>
                        <artifactId>ant-nodeps</artifactId>
                        <version>${ant-nodeps.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.0.0</version>
                <dependencies>
                    <dependency>
                        <groupId>de.skuzzle.enforcer</groupId>
                        <artifactId>restrict-imports-enforcer-rule</artifactId>
                        <version>2.0.0</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>check-logging-imports</id> <!-- put an explanatory ID here -->
                        <phase>process-sources</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <RestrictImports>
                                    <bannedImports>
                                        <bannedImport>io.opentracing.**</bannedImport>
                                        <bannedImport>io.micrometer.**</bannedImport>
                                    </bannedImports>
                                </RestrictImports>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>