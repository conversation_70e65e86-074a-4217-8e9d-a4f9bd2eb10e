package com.wosai.middleware.hera.plugin.datasource.hikaricp;

import com.wosai.middleware.hera.agent.metrics.MeterBinder;
import com.wosai.middleware.hera.agent.metrics.api.Gauge;
import com.zaxxer.hikari.HikariConfigMXBean;
import com.zaxxer.hikari.HikariPoolMXBean;

public class HikariMeterBinder implements MeterBinder {
    private final HikariPoolMXBean hikariPoolMXBean;
    private final HikariConfigMXBean hikariConfigMXBean;
    private final String poolName;

    public HikariMeterBinder(HikariConfigMXBean hikariConfigMXBean, HikariPoolMXBean hikariPoolMXBean) {
        this.hikariPoolMXBean = hikariPoolMXBean;
        this.hikariConfigMXBean = hikariConfigMXBean;
        this.poolName = this.hikariConfigMXBean.getPoolName();
    }

    @Override
    public void bindTo() {
        registerGauge();
    }

    private void registerGauge() {
        Gauge.builder("hikaricp.connections", this.hikariPoolMXBean, HikariPoolMXBean::getTotalConnections).description("Total connections").tags(new String[]{"pool", poolName}).build();
        Gauge.builder("hikaricp.connections.idle", this.hikariPoolMXBean, HikariPoolMXBean::getIdleConnections).description("Idle connections").tags(new String[]{"pool", poolName}).build();
        Gauge.builder("hikaricp.connections.active", this.hikariPoolMXBean, HikariPoolMXBean::getActiveConnections).description("Active connections").tags(new String[]{"pool", poolName}).build();
        Gauge.builder("hikaricp.connections.pending", this.hikariPoolMXBean, HikariPoolMXBean::getThreadsAwaitingConnection).description("Pending threads").tags(new String[]{"pool", poolName}).build();
        Gauge.builder("hikaricp.connections.max", this.hikariConfigMXBean, HikariConfigMXBean::getMaximumPoolSize).description("Max connections").tags(new String[]{"pool", poolName}).build();
        Gauge.builder("hikaricp.connections.min", this.hikariConfigMXBean, HikariConfigMXBean::getMinimumIdle).description("Min connections").tags(new String[]{"pool", poolName}).build();
    }
}
