package com.wosai.middleware.hera.plugin.datasource.hikaricp.define;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.WitnessMethod;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.InstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.ClassInstanceMethodsEnhancePluginDefine;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;

import java.util.Collections;
import java.util.List;

import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.takesNoArguments;
import static org.apache.skywalking.apm.agent.core.plugin.match.NameMatch.byName;

public class HikariDataSourceInstrumentation extends ClassInstanceMethodsEnhancePluginDefine {
    private static final String ENHANCE_CLASS = "com.zaxxer.hikari.HikariConfig";
    private static final String INTERCEPTOR_CLASS = "com.wosai.middleware.hera.plugin.datasource.hikaricp.HikariConfigSealInterceptor";
    private static final String METRICS_TRACER_INTERCEPTOR_CLASS = "com.wosai.middleware.hera.plugin.datasource.hikaricp.HikariConfigGetMetricsTrackerFactoryInterceptor";

    @Override
    protected ClassMatch enhanceClass() {
        return byName(ENHANCE_CLASS);
    }

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[0];
    }

    @Override
    public InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints() {
        return new InstanceMethodsInterceptPoint[]{
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("seal").and(takesNoArguments());
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return INTERCEPTOR_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("getMetricsTrackerFactory").and(takesNoArguments());
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return METRICS_TRACER_INTERCEPTOR_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }

    /**
     * {@link com.zaxxer.hikari.HikariConfigMXBean} and {@link com.zaxxer.hikari.HikariPoolMXBean}
     * are introduced @since 2.4.0
     */
    @Override
    protected String[] witnessClasses() {
        return new String[]{"com.zaxxer.hikari.HikariConfigMXBean", "com.zaxxer.hikari.HikariPoolMXBean"};
    }

    /**
     * {@link com.zaxxer.hikari.HikariConfig#seal()} is introduced @since 3.0.0
     */
    @Override
    protected List<WitnessMethod> witnessMethods() {
        return Collections.singletonList(new WitnessMethod(ENHANCE_CLASS, named("seal")));
    }
}
