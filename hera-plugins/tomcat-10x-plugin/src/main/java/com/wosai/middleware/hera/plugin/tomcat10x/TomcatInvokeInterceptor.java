package com.wosai.middleware.hera.plugin.tomcat10x;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import com.wosai.middleware.hera.tracing.tag.Tags;
import com.wosai.middleware.hera.util.ExtraTags;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.catalina.connector.Request;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.agent.core.util.CollectionUtil;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.tomcat10x.Constants;
import org.apache.skywalking.apm.plugin.tomcat10x.TomcatPluginConfig;
import org.apache.skywalking.apm.util.StringUtil;
import org.apache.tomcat.util.http.Parameters;

import java.lang.reflect.Method;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

public class TomcatInvokeInterceptor implements InstanceMethodsAroundInterceptor {

    private static final String SERVLET_RESPONSE_CLASS = "jakarta.servlet.http.HttpServletResponse";
    private static final String GET_STATUS_METHOD = "getStatus";

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Throwable {
        Request request = (Request) allArguments[0];

        HeraSpanContext parentSpan = ContextManager.extract(new TomcatHeadersExtractAdapter(request));

        String operationName = String.join(":", request.getMethod(), request.getRequestURI());
        AbstractHeraSpan span = ContextManager.createEntrySpan(operationName, parentSpan);
        Tags.HTTP_URL.set(span, request.getRequestURL().toString());
        Tags.HTTP_METHOD.set(span, request.getMethod());
        Tags.COMPONENT.set(span, ComponentsDefine.TOMCAT.getName());

        if (TomcatPluginConfig.Plugin.Tomcat.COLLECT_HTTP_PARAMS) {
            final Map<String, String[]> parameterMap = new HashMap<>();
            final org.apache.coyote.Request coyoteRequest = request.getCoyoteRequest();
            final Parameters parameters = coyoteRequest.getParameters();
            for (final Enumeration<String> names = parameters.getParameterNames(); names.hasMoreElements(); ) {
                final String name = names.nextElement();
                parameterMap.put(name, parameters.getParameterValues(name));
            }

            if (!parameterMap.isEmpty()) {
                String tagValue = CollectionUtil.toString(parameterMap);
                tagValue = TomcatPluginConfig.Plugin.Http.HTTP_PARAMS_LENGTH_THRESHOLD > 0 ?
                        StringUtil.cut(tagValue, TomcatPluginConfig.Plugin.Http.HTTP_PARAMS_LENGTH_THRESHOLD) :
                        tagValue;
                span.setTag(ExtraTags.HTTP_PARAMS, tagValue);
            }
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments,
                              Class<?>[] argumentsTypes, Object ret) throws Throwable {
        HttpServletResponse response = (HttpServletResponse) allArguments[1];

        AbstractHeraSpan span = ContextManager.activeSpan();

        Tags.HTTP_STATUS.set(span, response.getStatus());
        if (response.getStatus() >= 400) {
            ContextManager.markError(span);
        }

        ContextManager.getRuntimeContext().remove(Constants.FORWARD_REQUEST_FLAG);
        ContextManager.stopSpan();
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {
        ContextManager.logError(t);
    }
}
