package com.wosai.middleware.hera.plugin.shardingsphere.v40.define;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.InstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;
import org.apache.skywalking.apm.agent.core.plugin.match.NameMatch;
import org.apache.skywalking.apm.plugin.shardingsphere.v40.define.AbstractShardingSphere40Instrumentation;

import static net.bytebuddy.matcher.ElementMatchers.named;

/**
 * ProxyRootInvokeInstrumentation presents that skywalking intercepts. skywalking intercepts org.apache.shardingsphere.shardingproxy.frontend.command.CommandExecutorTask.
 */
public class ProxyRootInvokeInstrumentation extends AbstractShardingSphere40Instrumentation {
    private static final String ENHANCE_CLASS = "org.apache.shardingsphere.shardingproxy.frontend.command.CommandExecutorTask";

    private static final String PROXY_ROOT_INVOKE_INTERCEPTOR_CLASS = "com.wosai.middleware.hera.plugin.shardingsphere.v40.ProxyRootInvokeInterceptor";

    @Override
    protected ClassMatch enhanceClass() {
        return NameMatch.byName(ENHANCE_CLASS);
    }

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[0];
    }

    @Override
    public InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints() {
        return new InstanceMethodsInterceptPoint[]{
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("run");
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return PROXY_ROOT_INVOKE_INTERCEPTOR_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }
}
