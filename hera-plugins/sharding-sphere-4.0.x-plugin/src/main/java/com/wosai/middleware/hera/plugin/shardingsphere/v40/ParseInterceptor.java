package com.wosai.middleware.hera.plugin.shardingsphere.v40;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import org.apache.shardingsphere.core.execute.engine.ShardingExecuteDataMap;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;

import java.lang.reflect.Method;

/**
 * {@link ParseInterceptor} enhances {@link org.apache.shardingsphere.core.route.router.sharding.ShardingRouter}.
 * creating a local span that records the parse of sql.
 */
public class ParseInterceptor implements InstanceMethodsAroundInterceptor {

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) {
        AbstractHeraSpan span = ContextManager.createLocalSpan("/ShardingSphere/parseSQL/");
        Tags.COMPONENT.set(span, ComponentsDefine.SHARDING_SPHERE.getName());
        Tags.DB_STATEMENT.set(span, (String) allArguments[0]);
        ShardingExecuteDataMap.getDataMap().put(Constant.ORIGINAL_STATEMENT, allArguments[0]);
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) {
        ContextManager.stopSpan();
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {
        ContextManager.logError(t);
    }
}
