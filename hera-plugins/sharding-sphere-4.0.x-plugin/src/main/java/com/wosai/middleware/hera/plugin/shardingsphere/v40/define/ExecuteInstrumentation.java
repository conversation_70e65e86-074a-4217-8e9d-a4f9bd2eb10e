package com.wosai.middleware.hera.plugin.shardingsphere.v40.define;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.InstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;
import org.apache.skywalking.apm.agent.core.plugin.match.NameMatch;
import org.apache.skywalking.apm.plugin.shardingsphere.v40.define.AbstractShardingSphere40Instrumentation;

import static net.bytebuddy.matcher.ElementMatchers.named;

/**
 * {@link ExecuteInstrumentation} presents that skywalking intercepts. {@link org.apache.shardingsphere.core.execute.sql.execute.SQLExecuteCallback}.
 */
public class ExecuteInstrumentation extends AbstractShardingSphere40Instrumentation {
    private static final String ENHANCE_CLASS = "org.apache.shardingsphere.core.execute.sql.execute.SQLExecuteCallback";

    private static final String EXECUTE_INTERCEPTOR_CLASS = "com.wosai.middleware.hera.plugin.shardingsphere.v40.ExecuteInterceptor";

    @Override
    protected ClassMatch enhanceClass() {
        return NameMatch.byName(ENHANCE_CLASS);
    }

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[0];
    }

    @Override
    public InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints() {
        return new InstanceMethodsInterceptPoint[]{
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("execute0");
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return EXECUTE_INTERCEPTOR_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }
}
