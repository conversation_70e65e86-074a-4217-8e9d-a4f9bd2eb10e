/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.wosai.middleware.hera.plugin.shardingsphere.v41;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import com.wosai.middleware.hera.util.DbTypeConstant;
import com.wosai.middleware.hera.util.ExtraTags;
import org.apache.shardingsphere.sharding.execute.sql.StatementExecuteUnit;
import org.apache.shardingsphere.underlying.executor.engine.ExecutorDataMap;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.jdbc.trace.ConnectionInfo;
import org.apache.skywalking.apm.util.StringUtil;

import java.lang.reflect.Method;
import java.sql.Connection;
import java.util.Map;

/**
 * {@link ExecuteInterceptor} enhances. {@link org.apache.shardingsphere.sharding.execute.sql.execute.SQLExecuteCallback}
 * creating a local span that records the execution of sql.
 */
public class ExecuteInterceptor implements InstanceMethodsAroundInterceptor {
    /**
     * We do interception on the following method,
     * {@code private T execute0(final StatementExecuteUnit statementExecuteUnit, final boolean isTrunkThread, final Map<String, Object> dataMap) throws SQLException}
     */
    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Exception {
        final Map<String, Object> internalMap = (Map<String, Object>) allArguments[2];
        AbstractHeraSpan parentSpan = getFromMap(internalMap, Constant.CROSS_THREAD_SPAN);
        AbstractHeraSpan span = ContextManager.createLocalSpan("/ShardingSphere/executeSQL/", parentSpan == null ? null : parentSpan.context());
        Tags.COMPONENT.set(span, ComponentsDefine.SHARDING_SPHERE.getName());
        Tags.DB_TYPE.set(span, DbTypeConstant.SQL);
        String lowCardinalSQL = getFromMap(internalMap, Constant.ORIGINAL_STATEMENT);
        if (StringUtil.isNotEmpty(lowCardinalSQL)) {
            Tags.DB_STATEMENT.set(span, lowCardinalSQL);
        }
        StatementExecuteUnit statementExecuteUnit = (StatementExecuteUnit) allArguments[0];
        Connection conn = statementExecuteUnit.getStatement().getConnection().unwrap(Connection.class);
        if (conn instanceof EnhancedInstance) {
            final ConnectionInfo connectInfo = (ConnectionInfo) ((EnhancedInstance) conn).getSkyWalkingDynamicField();
            Tags.PEER_SERVICE.set(span, connectInfo.getDatabasePeer());
            Tags.DB_INSTANCE.set(span, connectInfo.getDatabaseName());
        }
        String highCardinalSQL = statementExecuteUnit.getExecutionUnit().getSqlUnit().getSql();
        if (StringUtil.isNotEmpty(highCardinalSQL)) {
            span.setTag(ExtraTags.DB_STATEMENT_RAW, highCardinalSQL);
        }
    }

    <T> T getFromMap(Map<String, Object> internalMap, String key) {
        // first, try to get Value from ExecutorDataMap, since for Serial executor, they may be running in the same thread
        T value = (T) ExecutorDataMap.getValue().get(key);
        if (null == value) {
            // otherwise, we can always get it from argument[2]
            value = (T) internalMap.get(key);
        }
        return value;
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) {
        ContextManager.stopSpan();
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {
        ContextManager.logError(t);
    }
}
