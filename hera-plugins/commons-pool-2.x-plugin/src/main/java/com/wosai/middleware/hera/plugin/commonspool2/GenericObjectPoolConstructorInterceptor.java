package com.wosai.middleware.hera.plugin.commonspool2;

import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;

public class GenericObjectPoolConstructorInterceptor implements InstanceConstructorInterceptor {
    private static final ILog LOGGER = LogManager.getLogger(GenericObjectPoolConstructorInterceptor.class);

    @Override
    public void onConstruct(EnhancedInstance enhancedInstance, Object[] objects) throws Throwable {
        GenericObjectPool<?> pool = (GenericObjectPool<?>) enhancedInstance;
        CommonsObjectPool2Metrics metrics = new CommonsObjectPool2Metrics(pool);
        metrics.bindTo();
        if (enhancedInstance.getSkyWalkingDynamicField() != null) {
            LOGGER.warn("SkyWalking Dynamic Field is already set: CommonPools2Metrics may not been closed properly");
            return;
        }
        enhancedInstance.setSkyWalkingDynamicField(metrics);
    }
}
