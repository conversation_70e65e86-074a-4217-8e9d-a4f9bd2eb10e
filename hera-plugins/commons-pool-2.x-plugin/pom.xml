<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wosai.middleware</groupId>
        <artifactId>hera-plugins</artifactId>
        <version>1.14.0-SNAPSHOT</version>
    </parent>

    <artifactId>hera-commons-pool-2.x-plugin</artifactId>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sdk.plugin.related.dir>/..</sdk.plugin.related.dir>
        <commons-pool.version>2.7.0</commons-pool.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>${commons-pool.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>