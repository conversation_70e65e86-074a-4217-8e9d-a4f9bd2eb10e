package com.wosai.middleware.hera.plugin.jsonrpc4j;

import com.wosai.middleware.hera.rpc.proxyless.ManagedProxylessService;

import java.net.URL;

public class JsonRpcPeerInfo {
    private URL serviceUrl;

    private String serviceUrlString;

    private ManagedProxylessService managedService;

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public void setManagedService(ManagedProxylessService managedService) {
        this.managedService = managedService;
    }

    public ManagedProxylessService getManagedService() {
        return this.managedService;
    }

    private int port;

    public URL getServiceUrl() {
        return serviceUrl;
    }

    public void setServiceUrl(URL serviceUrl) {
        this.serviceUrl = serviceUrl;
        this.serviceUrlString = serviceUrl.toString();
    }

    public String getServiceUrlString() {
        return serviceUrlString;
    }
}
