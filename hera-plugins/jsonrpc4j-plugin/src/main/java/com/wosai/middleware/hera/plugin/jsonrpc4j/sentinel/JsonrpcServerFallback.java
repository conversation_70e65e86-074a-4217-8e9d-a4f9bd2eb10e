package com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.middleware.hera.agent.sentinel.BlockException;
import com.wosai.middleware.hera.agent.sentinel.Entry;
import com.wosai.middleware.hera.agent.sentinel.EntryType;
import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.agent.services.SentinelContextManager;
import com.wosai.middleware.hera.plugin.jsonrpc4j.JsonRpcBasicContext;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.apache.skywalking.apm.util.StringUtil;

import java.io.IOException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.List;
import java.util.ServiceLoader;

import static com.wosai.middleware.hera.plugin.jsonrpc4j.define.Constants.REQUEST_APPLICATION;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine.Provider.SERVER;

public class JsonrpcServerFallback {

    private static final ILog LOGGER = LogManager.getLogger(JsonrpcServerFallback.class);

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    static {
        for (JsonRPCFallbackDefine fallback : ServiceLoader.load(JsonRPCFallbackDefine.class)) {
            if (SERVER == fallback.getProvider()) {
                ResourceMetadataRegistry.INSTANCE.initializeFallback(fallback);
                LOGGER.info("JSONRPC server fallback {} loaded successfully", fallback.getClass().getCanonicalName());
            }
        }
    }

    public static void handleBefore(Method method, Object[] allArguments, MethodInvocationContext context) {
        if (!SentinelContextManager.sentinelOpen()) {
            return;
        }
        String resourceName = SentinelContextManager.getRpcResourceName(method, EntryType.IN);
        FallbackWrapper fallbackWrapper = ResourceMetadataRegistry.INSTANCE.getServerFallback(resourceName, method);

        if (fallbackWrapper.getHandler() == null) {
            return;
        }

        Object[] argument = convertJsonToParameters(method, (List<JsonNode>) allArguments[2]);
        Entry methodEntry = null;
        try {
            String applicationName = ContextManager.getBaggageItem(REQUEST_APPLICATION, null);
            if (StringUtil.isNotBlank(applicationName)) {
                SentinelContextManager.enterApplication(applicationName, resourceName);
            }
            methodEntry = SentinelContextManager.entry(resourceName, 2, EntryType.IN, argument);
        } catch (BlockException wrappedBlockEx) {
            // convert "com.wosai.middleware.hera.agent.sentinel.BlockException" to "com.alibaba.csp.sentinel.slots.block.BlockException" declared in the toolkit
            com.alibaba.csp.sentinel.slots.block.BlockException exception = BlockExceptionHelper.parseFromCore(wrappedBlockEx);
            context.defineReturnValue(fallbackWrapper.getHandler().handleMethodBlockException(exception, method, argument));
        } finally {
            JsonRpcBasicContext sentinelContext = new JsonRpcBasicContext(methodEntry, argument,
                    fallbackWrapper.getHandler());
            context.setContext(sentinelContext);
        }
    }

    private static Object[] convertJsonToParameters(Method m, List<JsonNode> params) {
        Object[] convertedParams = new Object[params.size()];
        Type[] parameterTypes = m.getGenericParameterTypes();

        for (int i = 0; i < parameterTypes.length; i++) {
            JsonParser paramJsonParser = OBJECT_MAPPER.treeAsTokens(params.get(i));
            JavaType paramJavaType = OBJECT_MAPPER.getTypeFactory().constructType(parameterTypes[i]);

            try {
                convertedParams[i] = OBJECT_MAPPER.readerFor(paramJavaType)
                        .with(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
                        .readValue(paramJsonParser);
            } catch (IOException e) {
                // deserialization failed then do nothing
            }
        }
        return convertedParams;
    }

    public static void handleAfter(MethodInvocationContext context) {
        JsonRpcBasicContext sentinelContext = (JsonRpcBasicContext) context.getContext();
        if (sentinelContext != null) {
            SentinelContextManager.entryEnd(sentinelContext.getEntry(), sentinelContext.getArguments());
        }
    }
}
