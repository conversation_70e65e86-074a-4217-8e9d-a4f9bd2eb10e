package com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel;

import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.any;

public class DefaultJsonRPCServerFallbackDefine extends JsonRPCFallbackDefine {
    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        return any();
    }

    @Override
    public Provider getProvider() {
        return Provider.SERVER;
    }
}
