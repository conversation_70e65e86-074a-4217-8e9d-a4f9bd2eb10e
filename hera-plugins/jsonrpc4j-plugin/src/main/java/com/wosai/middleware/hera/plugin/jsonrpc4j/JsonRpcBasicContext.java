package com.wosai.middleware.hera.plugin.jsonrpc4j;

import com.wosai.middleware.hera.agent.sentinel.Entry;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public class JsonRpcBasicContext {
    private Entry entry;

    private Object[] arguments;

    private JsonRPCMethodFallbackHandler handler;
}
