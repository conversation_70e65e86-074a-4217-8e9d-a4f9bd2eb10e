package com.wosai.middleware.hera.plugin.jsonrpc4j;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import com.wosai.middleware.hera.tracing.tag.Tags;
import com.wosai.middleware.hera.util.ExtraComponent;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.agent.core.util.MethodUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

public class JsonServiceExporterInterceptor implements InstanceMethodsAroundInterceptor {
    private static boolean IS_SERVLET_GET_STATUS_METHOD_EXIST;
    private static final String SERVLET_RESPONSE_CLASS = "javax.servlet.http.HttpServletResponse";
    private static final String GET_STATUS_METHOD = "getStatus";

    static {
        IS_SERVLET_GET_STATUS_METHOD_EXIST = MethodUtil.isMethodExist(JsonServiceExporterInterceptor.class.getClassLoader(), SERVLET_RESPONSE_CLASS, GET_STATUS_METHOD);
    }

    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, MethodInterceptResult methodInterceptResult) throws Throwable {
        HttpServletRequest request = (HttpServletRequest) objects[0];
        HeraSpanContext parentSpan = ContextManager.extract(new HttpServletRequestExtractAdapter(request));

        String spanName = request.getRequestURI();
        if (spanName != null && spanName.length() >= 2 && spanName.charAt(1) == '/') {
            spanName = spanName.substring(1);
        }

        AbstractHeraSpan span = ContextManager.createEntrySpan(spanName, parentSpan);
        Tags.HTTP_URL.set(span, request.getRequestURL().toString());
        Tags.HTTP_METHOD.set(span, request.getMethod());
        Tags.COMPONENT.set(span, ExtraComponent.JSON_RPC_SERVER);
        // ContextManager.getRuntimeContext().put(REQUEST_KEY_IN_RUNTIME_CONTEXT, request);
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Object ret) throws Throwable {

        HttpServletResponse response = (HttpServletResponse) objects[1];
        AbstractHeraSpan span = ContextManager.activeSpan();

        if (IS_SERVLET_GET_STATUS_METHOD_EXIST) {
            Tags.HTTP_STATUS.set(span, response.getStatus());
            if (response.getStatus() > 400) {
                ContextManager.markError(span);
            }
        }
        // ContextManager.getRuntimeContext().remove(REQUEST_KEY_IN_RUNTIME_CONTEXT);
        ContextManager.stopSpan();
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {

    }
}
