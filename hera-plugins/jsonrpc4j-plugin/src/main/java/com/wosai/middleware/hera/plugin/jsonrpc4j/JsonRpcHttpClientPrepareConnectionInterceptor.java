package com.wosai.middleware.hera.plugin.jsonrpc4j;

import com.googlecode.jsonrpc4j.JsonRpcBasicServer;
import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.wosai.middleware.hera.agent.conf.HeraConfig;
import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.rpc.LoadBalancer;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import org.apache.skywalking.apm.agent.core.conf.Config;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.InstanceMethodsAroundInterceptorV2;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.apache.skywalking.apm.util.StringUtil;

import java.io.IOException;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static com.wosai.middleware.hera.plugin.jsonrpc4j.JsonRpcHttpClientInterceptor.RPC_METHOD_CONTEXT;
import static com.wosai.middleware.hera.plugin.jsonrpc4j.define.Constants.REQUEST_APPLICATION;
import static com.wosai.middleware.hera.plugin.jsonrpc4j.define.Constants.SOURCE_VERSION;

@SuppressWarnings("unused")
public class JsonRpcHttpClientPrepareConnectionInterceptor implements InstanceMethodsAroundInterceptorV2 {
    private static final ILog LOGGER = LogManager.getLogger(JsonRpcHttpClientPrepareConnectionInterceptor.class);

    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] arguments, Class<?>[] classes,
                             MethodInvocationContext methodInvocationContext) {
        final JsonRpcHttpClient client = (JsonRpcHttpClient) enhancedInstance;
        final JsonRpcPeerInfo peerInfo = (JsonRpcPeerInfo) enhancedInstance.getSkyWalkingDynamicField();

        // Extract headers
        // We moved span header extraction from afterMethod since 1.4.0,
        // in order to support better router decision.
        Map<String, String> spanHeaders = new HashMap<>();
        AbstractHeraSpan span = ContextManager.activeSpan();
        if (span != null) {
            ContextManager.inject(span, spanHeaders::put);
        }
        methodInvocationContext.setContext(spanHeaders);

        if (peerInfo != null && peerInfo.getManagedService() != null) {
            Map<String, String> headers = new HashMap<>(client.getHeaders());
            headers.putAll(spanHeaders);
            if (arguments[0] != null && !((Map<String, String>) arguments[0]).isEmpty()) {
                headers.putAll((Map<String, String>) arguments[0]);
            }
            // Sentinel: add source application
            if (!headers.containsKey(REQUEST_APPLICATION)) {
                headers.put(REQUEST_APPLICATION, Config.Agent.SERVICE_NAME);
            }
            if (!StringUtil.isEmpty(HeraConfig.Kubernetes.Pod.VERSION) && !headers.containsKey(SOURCE_VERSION)) {
                headers.put(SOURCE_VERSION, HeraConfig.Kubernetes.Pod.VERSION);
            }

            // TODO: xDS routing: add x-env-flag to route decision

            if (LOGGER.isDebugEnable()) {
                LOGGER.debug("select server with method {} and headers {}", RPC_METHOD_CONTEXT.get(), headers);
            }
            final LoadBalancer.PickResult result = peerInfo.getManagedService().selectServer(LoadBalancer.PickServerArgs.create(peerInfo.getServiceUrl().getPath(),
                    RPC_METHOD_CONTEXT.get(), headers, Collections.emptyMap()));
            if (result.getStatus().isOk() && result.getServer() != null) {
                LOGGER.debug("server selected {}", result.getServer());
                try {
                    final URL newServiceUrl = new URL(result.getServer().scheme() + "://" + result.getServer().authority() + peerInfo.getServiceUrl().getPath());

                    HttpURLConnection conn = prepareConnection(client, newServiceUrl, headers);
                    methodInvocationContext.defineReturnValue(conn);
                } catch (IOException ioEx) {
                    LOGGER.error(ioEx, "fail to create connection based on XDS result {}", result);
                }
            } else {
                LOGGER.debug("failed to select an available server, fallback to DNS resolver");
            }
        }
    }

    private HttpURLConnection prepareConnection(JsonRpcHttpClient client, URL serviceUrl, Map<String, String> allHeaders) throws IOException {
        // create URLConnection
        HttpURLConnection connection = (HttpURLConnection) serviceUrl.openConnection(client.getConnectionProxy());
        connection.setConnectTimeout(client.getConnectionTimeoutMillis());
        connection.setReadTimeout(client.getReadTimeoutMillis());
        connection.setAllowUserInteraction(false);
        connection.setDefaultUseCaches(false);
        connection.setDoInput(true);
        connection.setDoOutput(true);
        connection.setUseCaches(false);
        connection.setInstanceFollowRedirects(true);
        connection.setRequestMethod("POST");

        // TODO: since call within the cluster is always plain HTTP, we may ignore SSL here
        // setupSSL(connection)
        connection.setRequestProperty("Content-Type", JsonRpcBasicServer.JSONRPC_CONTENT_TYPE);
        for (Map.Entry<String, String> entry : allHeaders.entrySet()) {
            if (StringUtil.isEmpty(entry.getKey()) || StringUtil.isEmpty(entry.getValue())) {
                continue;
            }
            connection.setRequestProperty(entry.getKey(), entry.getValue());
        }
        return connection;
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Object retObj, MethodInvocationContext methodInvocationContext) {
        HttpURLConnection connection = (HttpURLConnection) retObj;
        Map<String, String> spanHeaders = (Map<String, String>) methodInvocationContext.getContext();
        if (!spanHeaders.isEmpty()) {
            for (final Map.Entry<String, String> e : spanHeaders.entrySet()) {
                connection.setRequestProperty(e.getKey(), e.getValue());
            }
        }

        return retObj;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable, MethodInvocationContext methodInvocationContext) {

    }
}
