package com.wosai.middleware.hera.plugin.jsonrpc4j;

import com.wosai.middleware.hera.agent.core.plugin.interceptor.enhance.v3.InstanceMethodsAroundInterceptorV3;
import com.wosai.middleware.hera.agent.sentinel.BlockException;
import com.wosai.middleware.hera.agent.sentinel.Entry;
import com.wosai.middleware.hera.agent.sentinel.EntryType;
import com.wosai.middleware.hera.agent.services.SentinelContextManager;
import com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.BlockExceptionHelper;
import com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.FallbackWrapper;
import com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.ResourceMetadataRegistry;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import org.aopalliance.intercept.MethodInvocation;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;

import java.lang.reflect.Method;
import java.util.ServiceLoader;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine.Provider.CLIENT;

public class JsonProxyFactoryBeanInvokeInterceptor implements InstanceMethodsAroundInterceptorV3 {

    private static final ILog LOGGER = LogManager.getLogger(JsonProxyFactoryBeanInvokeInterceptor.class);

    static {
        for (JsonRPCFallbackDefine fallback : ServiceLoader.load(JsonRPCFallbackDefine.class)) {
            if (CLIENT == fallback.getProvider()) {
                ResourceMetadataRegistry.INSTANCE.initializeFallback(fallback);
                LOGGER.warn("JSONRPC client fallback {} loaded successfully", fallback.getClass().getCanonicalName());
            }
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments,
                              Class<?>[] argumentsTypes, Object ret,
                              MethodInvocationContext context) throws Throwable {
        JsonRpcBasicContext sentinelContext = (JsonRpcBasicContext) context.getContext();
        if (sentinelContext != null) {
            SentinelContextManager.entryEnd(sentinelContext.getEntry(), sentinelContext.getArguments());
        }
        return ret;
    }

    @Override
    public Object handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                        Class<?>[] argumentsTypes, Throwable throwable,
                                        MethodInvocationContext context) throws Throwable {
        JsonRpcBasicContext sentinelContext = (JsonRpcBasicContext) context.getContext();
        if (sentinelContext != null) {
            SentinelContextManager.traceException(throwable);
        }
        throw throwable;
    }

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInvocationContext context) throws Throwable {
        if (!SentinelContextManager.sentinelOpen()) {
            return;
        }

        MethodInvocation invocation = (MethodInvocation) allArguments[0];
        Method invocationMethod = invocation.getMethod();
        String resourceName = SentinelContextManager.getRpcResourceName(invocationMethod, EntryType.OUT);

        FallbackWrapper fallbackWrapper = ResourceMetadataRegistry.INSTANCE.getClientFallback(resourceName,
                invocationMethod);
        if (fallbackWrapper.getHandler() == null) {
            return;
        }
        Entry methodEntry = null;
        try {
            methodEntry = SentinelContextManager.entry(resourceName, 2, EntryType.OUT, invocation.getArguments());
        } catch (BlockException wrappedBlockEx) {
            com.alibaba.csp.sentinel.slots.block.BlockException exception =
                    BlockExceptionHelper.parseFromCore(wrappedBlockEx);
            context.defineReturnValue(
                    fallbackWrapper.getHandler().handleMethodBlockException(exception, invocationMethod,
                            invocation.getArguments()));
        } finally {
            JsonRpcBasicContext sentinelContext = new JsonRpcBasicContext(methodEntry, invocation.getArguments(),
                    fallbackWrapper.getHandler());
            context.setContext(sentinelContext);
        }
    }
}
