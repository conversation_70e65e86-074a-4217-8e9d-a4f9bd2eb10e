package com.wosai.middleware.hera.plugin.jsonrpc4j;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.googlecode.jsonrpc4j.JsonRpcClientException;
import com.wosai.middleware.hera.agent.conf.HeraConfig;
import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.agent.services.MeshContextManager;
import com.wosai.middleware.hera.rpc.LoadBalancer;
import com.wosai.middleware.hera.rpc.proxyless.ManagedProxylessService;
import com.wosai.middleware.hera.rpc.proxyless.ManagedServiceBuilder;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import com.wosai.middleware.hera.util.ExtraComponent;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.InstanceMethodsAroundInterceptorV2;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.apache.skywalking.apm.util.StringUtil;

import java.lang.reflect.Method;
import java.net.URL;
import java.util.Collections;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

@SuppressWarnings("unused")
public class JsonRpcHttpClientInterceptor implements InstanceMethodsAroundInterceptorV2, InstanceConstructorInterceptor {
    private static final String EXCEPTION_TYPE_NAME = "exceptionTypeName";
    private static final ILog LOGGER = LogManager.getLogger(JsonRpcHttpClientInterceptor.class);
    static final ThreadLocal<String> RPC_METHOD_CONTEXT = new ThreadLocal<>();

    /**
     * Parse service name in FQDN format.
     *
     * @param host serviceUrl defined in the {@link com.googlecode.jsonrpc4j.JsonRpcHttpClient}
     * @return service name if parsed. Null string if failed.
     */
    static String parseFQDNServiceName(String host) {
        if (StringUtil.isEmpty(host) || "localhost".equals(host)) {
            return null;
        }
        // 1. if FQDN (i.e. <service>.<namespace>.svc.cluster.local) is used,
        if (host.endsWith(".svc.cluster.local")) {
            return host;
        }

        // 2. if <service>.<namespace>.svc is used,
        if (host.endsWith(".svc")) {
            return host + ".cluster.local";
        }

        // 3. if <service>.<namespace> is used,
        final String[] hostParts = host.split("\\.");
        if (hostParts.length == 2) {
            return host + ".svc.cluster.local";
        }

        // 4. if only short service is used,
        if (hostParts.length == 1) {
            return host + "." + HeraConfig.Kubernetes.Pod.NAMESPACE + ".svc.cluster.local";
        }

        // otherwise
        return null;
    }

    @Override
    public void onConstruct(EnhancedInstance enhancedInstance, Object[] objects) {
        // if cache is already set
        if (enhancedInstance.getSkyWalkingDynamicField() != null) {
            return;
        }

        URL url = (URL) objects[1];
        URL newUrl = url;
        try {
            newUrl = url.toURI().normalize().toURL();
        } catch (Exception ex) {
            LOGGER.error(ex, "Fail to normalize url, file={}", url.getFile());
        }
        final JsonRpcPeerInfo clientDto = new JsonRpcPeerInfo();
        int port = url.getPort();
        if (port < 0) {
            if (isHttps(url)) {
                port = 443;
            } else {
                port = 80;
            }
        }

        // set clientDto and enhanceInstance first to avoid potential xDS failure.
        clientDto.setPort(port);
        clientDto.setServiceUrl(newUrl);
        enhancedInstance.setSkyWalkingDynamicField(clientDto);

        if (MeshContextManager.xdsAvailable() && JsonRpcPluginConfig.Plugin.Client.USE_XDS) {
            LOGGER.debug("try to bootstrap XDS for {} with port {}", url, port);
            String serviceName;
            if (StringUtil.isNotEmpty(serviceName = parseFQDNServiceName(url.getHost()))) {
                final String key = "xds:///" + serviceName + ":" + port;
                final ManagedProxylessService service = MeshContextManager.getOrRegistryManagedService(key,
                        () -> {
                            ManagedProxylessService instance = ManagedServiceBuilder.forAddress("xds:///" + serviceName, clientDto.getPort()).build();
                            if (HeraConfig.Mesh.XDS.WAIT_READY) {
                                try {
                                    boolean isReady = instance.awaitReady(
                                            LoadBalancer.PickServerArgs.create(url.getPath(), null, new HashMap<>(), Collections.emptyMap()),
                                            HeraConfig.Mesh.XDS.TIMEOUT_MILLIS, HeraConfig.Mesh.XDS.INTERVAL_MILLIS, TimeUnit.MILLISECONDS);
                                    LOGGER.info("{} is ready = {}", key, isReady);
                                } catch (InterruptedException ex) {
                                    LOGGER.error(ex, "sleep interrupted");
                                }
                            }
                            return instance;
                        });
                clientDto.setManagedService(service);
            }
        }
    }

    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes,
                             MethodInvocationContext methodInvocationContext) throws Throwable {
        JsonRpcPeerInfo clientDto = (JsonRpcPeerInfo) enhancedInstance.getSkyWalkingDynamicField();
        String methodName = objects[0].toString();
        RPC_METHOD_CONTEXT.set(methodName);
        String operationName = clientDto.getServiceUrl().getPath() + "." + methodName;
        AbstractHeraSpan span = ContextManager.createExitSpan(operationName, clientDto.getServiceUrl().getHost(), clientDto.getPort());
        Tags.COMPONENT.set(span, ExtraComponent.JSON_RPC_CLIENT);
        Tags.HTTP_METHOD.set(span, "POST");
        Tags.HTTP_URL.set(span, clientDto.getServiceUrlString() + "." + methodName);
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes,
                              Object o, MethodInvocationContext methodInvocationContext) throws Throwable {
        ContextManager.stopSpan();
        RPC_METHOD_CONTEXT.remove();
        return o;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects,
                                      Class<?>[] classes, Throwable throwable,
                                      MethodInvocationContext methodInvocationContext) {
        ContextManager.logError(getException(throwable));
    }

    static Throwable getException(final Throwable thrown) {
        if ("com.googlecode.jsonrpc4j.HttpException".equals(thrown.getClass().getName())) {
            Throwable cause = thrown.getCause();
            if (cause != null) {
                return cause;
            }
        } else if (thrown instanceof JsonRpcClientException) {
            JsonNode data = ((JsonRpcClientException) thrown).getData();
            if (data == null || !data.isObject()) {
                return thrown;
            }
            ObjectNode dataObject = ObjectNode.class.cast(data);
            if (hasNonNullTextualData(dataObject, EXCEPTION_TYPE_NAME)) {
                final String exceptionTypeName = dataObject.get(EXCEPTION_TYPE_NAME).asText();
                return new JsonRpcClientDelegateException(exceptionTypeName, thrown);
            }
        }
        return thrown;
    }

    static boolean hasNonNullTextualData(final ObjectNode node, final String key) {
        return hasNonNullData(node, key) && node.get(key).isTextual();
    }

    static boolean hasNonNullData(final ObjectNode node, final String key) {
        return node.has(key) && !node.get(key).isNull();
    }

    private boolean isHttps(URL url) {
        return url.toString().toLowerCase().startsWith("https");
    }
}
