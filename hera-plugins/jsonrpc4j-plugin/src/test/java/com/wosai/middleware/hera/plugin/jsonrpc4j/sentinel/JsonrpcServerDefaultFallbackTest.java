package com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel;

import com.wosai.middleware.hera.agent.sentinel.FlowRule;
import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.infra.HeraAgentServiceRule;
import com.wosai.middleware.hera.plugin.jsonrpc4j.JsonRpcBasicContext;
import com.wosai.middleware.hera.test.HeraSentinelInMemoryServer;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.baggage.BaggageField;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.lang.reflect.Method;
import java.util.ArrayList;

import static com.wosai.middleware.hera.plugin.jsonrpc4j.define.Constants.REQUEST_APPLICATION;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.catchRuntimeException;

public class JsonrpcServerDefaultFallbackTest {
    @Rule
    public MockitoRule rule = MockitoJUnit.rule();
    private Method method;

    private Method methodOrigin;

    private MethodInvocationContext context;

    @Rule
    public HeraSentinelInMemoryServer heraInMemoryServer = new HeraSentinelInMemoryServer("jsonrpc-test",
            BaggageField.create(REQUEST_APPLICATION));

    @Rule
    public HeraAgentServiceRule agentServiceRule = new HeraAgentServiceRule();

    @Before
    public void setup() throws NoSuchMethodException, ClassNotFoundException {
        heraInMemoryServer.loadRules(FlowRule.initFlowRule("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonrpcServerDefaultFallbackTest$SampleService:helloByOrign()", "serviceA", 0),
                FlowRule.initFlowRule("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonrpcServerDefaultFallbackTest$SampleService:hello()", null, 0)
        );
        Class<?> cache = Class.forName("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel." +
                "JsonrpcServerDefaultFallbackTest$SampleService");
        method = cache.getDeclaredMethod("hello");
        methodOrigin = cache.getDeclaredMethod("helloByOrign");
        context = new MethodInvocationContext();
    }

    @After
    public void after() {
        JsonrpcServerFallback.handleAfter(context);
        ResourceMetadataRegistry.INSTANCE.reset();
    }

    @Test
    public void test_withDefaultFallback() {
        Exception exception = catchRuntimeException(() ->
                JsonrpcServerFallback.handleBefore(method, new Object[]{null, null, new ArrayList<>()}, context));
        assertThat(exception).isNotNull().hasMessageContaining("SentinelBlockException: FlowException");
        assertThat(context.isContinue()).isTrue();
        JsonRpcBasicContext jsonRpcBasicContext = (JsonRpcBasicContext) context.getContext();
        assertThat(jsonRpcBasicContext).isNotNull().hasFieldOrPropertyWithValue("entry", null);
    }

    @Test
    public void test_visitByApplicationWithoutException() {
        AbstractHeraSpan span = ContextManager.createEntrySpan("asyncSpan");
        span.setBaggageItem(REQUEST_APPLICATION, "serviceB");
        assertThat(heraInMemoryServer.tracer().scopeManager().activeSpan()).isEqualTo(span);
        Exception exception = catchRuntimeException(() ->
                JsonrpcServerFallback.handleBefore(methodOrigin, new Object[]{null, null, new ArrayList<>()}, context));
        assertThat(exception).isNull();
        assertThat(context.isContinue()).isTrue();
        JsonRpcBasicContext jsonRpcBasicContext = (JsonRpcBasicContext) context.getContext();
        assertThat(jsonRpcBasicContext).isNotNull();
        assertThat(jsonRpcBasicContext.getEntry()).isNotNull();

    }

    @Test
    public void test_visitByApplicationWithException() {
        AbstractHeraSpan span = ContextManager.createEntrySpan("asyncSpan");
        span.setBaggageItem(REQUEST_APPLICATION, "serviceA");
        assertThat(heraInMemoryServer.tracer().scopeManager().activeSpan()).isEqualTo(span);
        Exception exception = catchRuntimeException(() ->
                JsonrpcServerFallback.handleBefore(methodOrigin, new Object[]{null, null, new ArrayList<>()}, context));
        assertThat(exception).isNotNull().hasMessageContaining("SentinelBlockException: FlowException");
        assertThat(context.isContinue()).isTrue();
    }

    public static class SampleService {
        public String hello() {
            return "world";
        }

        public String helloByOrign() {
            return "world";
        }
    }
}
