package com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.wosai.middleware.hera.agent.sentinel.FlowRule;
import com.wosai.middleware.hera.infra.HeraAgentServiceRule;
import com.wosai.middleware.hera.test.HeraSentinelInMemoryServer;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;
import com.wosai.middleware.hera.tracing.baggage.BaggageField;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import java.lang.reflect.Method;
import java.util.ArrayList;

import static com.wosai.middleware.hera.plugin.jsonrpc4j.define.Constants.REQUEST_APPLICATION;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.named;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.catchRuntimeException;

public class JsonrpcServerCustomizeFallbackTest {

    private Method method;

    private Method methodFalse;
    private MethodInvocationContext context;

    @Rule
    public HeraAgentServiceRule agentServiceRule = new HeraAgentServiceRule();

    @Rule
    public HeraSentinelInMemoryServer heraInMemoryServer = new HeraSentinelInMemoryServer("jsonrpc-test",
            BaggageField.create(REQUEST_APPLICATION));

    @Before
    public void setup() throws Exception {
        heraInMemoryServer.loadRules(FlowRule.initFlowRule("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonrpcServerCustomizeFallbackTest$SampleService:hello()", null, 0));

        Class<?> cache = Class.forName("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel." +
                "JsonrpcServerCustomizeFallbackTest$SampleService");
        method = cache.getDeclaredMethod("hello");
        methodFalse = cache.getDeclaredMethod("helloFalse");
        ResourceMetadataRegistry.INSTANCE.initializeFallback(new SampleService());
        context = new MethodInvocationContext();
    }

    @After
    public void after() {
        JsonrpcServerFallback.handleAfter(context);
        ResourceMetadataRegistry.INSTANCE.reset();
    }

    @Test
    public void test_withNotSentinelResource() {
        MethodInvocationContext context = new MethodInvocationContext();
        Exception exception = catchRuntimeException(() ->
                JsonrpcServerFallback.handleBefore(methodFalse, new Object[]{null, null, new ArrayList<>()}, context));
        assertThat(exception).isNull();
        assertThat(context.getContext()).isNotNull();
        assertThat(context.isContinue()).isTrue();
    }

    @Test
    public void test_withSentinelResource() {
        MethodInvocationContext context = new MethodInvocationContext();
        Exception exception = catchRuntimeException(() ->
                JsonrpcServerFallback.handleBefore(method, new Object[]{null, null, new ArrayList<>()}, context));
        assertThat(exception).isNull();
        assertThat(context.getContext()).isNotNull();
        assertThat(context.isContinue()).isFalse();
        assertThat(context._ret()).isEqualTo("aaa");
    }

    public static class SampleService extends JsonRPCFallbackDefine {
        public String hello() {
            return "world";
        }

        public String helloFalse() {
            return "world";
        }

        @Override
        public ElementMatcher<NamedElement.TypeElement> handleClass() {
            return named("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonrpcServerCustomizeFallbackTest$SampleService");
        }

        @Override
        public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
            return new JsonRPCMethodFallbackHandler[]{
                    new JsonRPCMethodFallbackHandler() {
                        @Override
                        public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                            return named("hello");
                        }

                        @Override
                        public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                            return "aaa";
                        }
                    }
            };
        }

        @Override
        public Provider getProvider() {
            return Provider.SERVER;
        }
    }
}
