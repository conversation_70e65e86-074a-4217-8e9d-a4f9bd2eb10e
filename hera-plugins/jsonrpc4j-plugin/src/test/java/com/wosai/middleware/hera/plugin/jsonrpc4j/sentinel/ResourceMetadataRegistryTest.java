package com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel;

import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;
import org.junit.Test;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.any;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.named;
import static org.assertj.core.api.Assertions.assertThat;

public class ResourceMetadataRegistryTest {
    @Test
    public void testSort_noAny() {
        JsonRPCMethodFallbackHandler[] handlers = new JsonRPCMethodFallbackHandler[]{
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("methodA");
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("methodB");
                    }
                }
        };
        assertThat(ResourceMetadataRegistry.INSTANCE.sortHandlers(handlers)).isTrue();
        assertThat(handlers[0].getMethodsMatcher()).isEqualTo(named("methodA"));
        assertThat(handlers[1].getMethodsMatcher()).isEqualTo(named("methodB"));
    }

    @Test
    public void testSort_whenTheFirstElementIsAny() {
        JsonRPCMethodFallbackHandler[] handlers = new JsonRPCMethodFallbackHandler[]{
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return any();
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("methodA");
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("methodB");
                    }
                }
        };
        assertThat(ResourceMetadataRegistry.INSTANCE.sortHandlers(handlers)).isTrue();
        assertThat(handlers[0].getMethodsMatcher()).isEqualTo(named("methodA"));
        assertThat(handlers[1].getMethodsMatcher()).isEqualTo(named("methodB"));
        assertThat(handlers[2].getMethodsMatcher()).isEqualTo(any());
    }

    @Test
    public void testSort_whenTheSecondElementIsAny() {
        JsonRPCMethodFallbackHandler[] handlers = new JsonRPCMethodFallbackHandler[]{
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("methodA");
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return any();
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("methodB");
                    }
                }
        };
        assertThat(ResourceMetadataRegistry.INSTANCE.sortHandlers(handlers)).isTrue();
        assertThat(handlers[0].getMethodsMatcher()).isEqualTo(named("methodA"));
        assertThat(handlers[1].getMethodsMatcher()).isEqualTo(named("methodB"));
        assertThat(handlers[2].getMethodsMatcher()).isEqualTo(any());
    }

    @Test
    public void testSort_whenTheLastElementIsAny() {
        JsonRPCMethodFallbackHandler[] handlers = new JsonRPCMethodFallbackHandler[]{
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("methodA");
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("methodB");
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return any();
                    }
                },
        };
        assertThat(ResourceMetadataRegistry.INSTANCE.sortHandlers(handlers)).isTrue();
        assertThat(handlers[0].getMethodsMatcher()).isEqualTo(named("methodA"));
        assertThat(handlers[1].getMethodsMatcher()).isEqualTo(named("methodB"));
        assertThat(handlers[2].getMethodsMatcher()).isEqualTo(any());
    }

    @Test
    public void testSort_whenMultipleAnyExist() {
        JsonRPCMethodFallbackHandler[] handlers = new JsonRPCMethodFallbackHandler[]{
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return any();
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("methodB");
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return any();
                    }
                },
        };
        assertThat(ResourceMetadataRegistry.INSTANCE.sortHandlers(handlers)).isFalse();
    }

    @Test
    public void testSort_whenMultipleAnyExist_noLastAny() {
        JsonRPCMethodFallbackHandler[] handlers = new JsonRPCMethodFallbackHandler[]{
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return any();
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return any();
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("methodB");
                    }
                },
        };
        assertThat(ResourceMetadataRegistry.INSTANCE.sortHandlers(handlers)).isFalse();
    }
}
