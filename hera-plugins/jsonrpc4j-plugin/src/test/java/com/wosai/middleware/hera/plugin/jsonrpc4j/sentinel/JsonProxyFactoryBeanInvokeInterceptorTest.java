package com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.wosai.middleware.hera.agent.sentinel.FlowRule;
import com.wosai.middleware.hera.infra.HeraAgentServiceRule;
import com.wosai.middleware.hera.plugin.jsonrpc4j.JsonProxyFactoryBeanInvokeInterceptor;
import com.wosai.middleware.hera.test.HeraSentinelInMemoryServer;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;
import com.wosai.middleware.hera.tracing.baggage.BaggageField;
import org.aopalliance.intercept.MethodInvocation;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.lang.reflect.Method;
import java.util.ArrayList;

import static com.wosai.middleware.hera.plugin.jsonrpc4j.define.Constants.REQUEST_APPLICATION;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.named;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.catchRuntimeException;
import static org.mockito.Mockito.when;

public class JsonProxyFactoryBeanInvokeInterceptorTest {
    @Rule
    public MockitoRule rule = MockitoJUnit.rule();
    private JsonProxyFactoryBeanInvokeInterceptor interceptor;

    private Enhanced enhanced;

    private MethodInvocationContext context;

    @Mock
    private MethodInvocation invocationFalse;

    @Mock
    private MethodInvocation invocation;

    @Rule
    public HeraAgentServiceRule agentServiceRule = new HeraAgentServiceRule();

    @Rule
    public HeraSentinelInMemoryServer heraInMemoryServer = new HeraSentinelInMemoryServer("jsonrpc-test",
            BaggageField.create(REQUEST_APPLICATION));

    @Before
    public void setup() throws Exception {
        heraInMemoryServer.loadRules(
                FlowRule.initFlowRule("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonProxyFactoryBeanInvokeInterceptorTest$SampleService:hello()", null, 0),
                FlowRule.initFlowRule("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonProxyFactoryBeanInvokeInterceptorTest:helloFalse()", null, 0)
        );
        enhanced = new Enhanced();
        interceptor = new JsonProxyFactoryBeanInvokeInterceptor();

        ResourceMetadataRegistry.INSTANCE.initializeFallback(new SampleService());
        context = new MethodInvocationContext();

        when(invocationFalse.getMethod()).thenReturn(SampleService.class.getMethod("helloFalse"));
        when(invocation.getMethod()).thenReturn(SampleService.class.getMethod("hello"));
        when(invocation.getArguments()).thenReturn(new Object[0]);
    }

    @After
    public void after() throws Throwable {
        interceptor.afterMethod(enhanced, null, null, null, null, context);
        ResourceMetadataRegistry.INSTANCE.reset();
    }

    @Test
    public void test_withNotSentinelResource() {

        MethodInvocationContext context = new MethodInvocationContext();

        Exception exception = catchRuntimeException(() ->
                interceptor.beforeMethod(enhanced, invocationFalse.getMethod(), new Object[]{invocationFalse, null, new ArrayList<>()}, null,
                        context));
        assertThat(exception).isNull();
        assertThat(context.getContext()).isNull();
        assertThat(context.isContinue()).isTrue();
    }

    @Test
    public void test_withSentinelResource() {
        MethodInvocationContext context = new MethodInvocationContext();
        Exception exception = catchRuntimeException(() ->
                interceptor.beforeMethod(enhanced, invocation.getMethod(), new Object[]{invocation, null, new ArrayList<>()}, null, context));
        assertThat(exception).isNull();
        assertThat(context.getContext()).isNotNull();
        assertThat(context.isContinue()).isFalse();
        assertThat(context._ret()).isEqualTo("aaa");
    }

    public static class SampleService extends JsonRPCFallbackDefine {

        public String hello() {
            return "world";
        }

        public String helloFalse() {
            return "world";
        }

        @Override
        public ElementMatcher<NamedElement.TypeElement> handleClass() {
            return named("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonProxyFactoryBeanInvokeInterceptorTest$SampleService");
        }

        @Override
        public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
            return new JsonRPCMethodFallbackHandler[]{
                    new JsonRPCMethodFallbackHandler() {
                        @Override
                        public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                            return named("hello");
                        }

                        @Override
                        public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                            return "aaa";
                        }
                    }
            };
        }

        @Override
        public Provider getProvider() {
            return Provider.CLIENT;
        }
    }

    public static class Enhanced implements EnhancedInstance {
        private Object obj;

        @Override
        public Object getSkyWalkingDynamicField() {
            return obj;
        }

        @Override
        public void setSkyWalkingDynamicField(Object o) {
            this.obj = o;
        }
    }
}