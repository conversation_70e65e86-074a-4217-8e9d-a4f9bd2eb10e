package com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.wosai.middleware.hera.agent.sentinel.FlowRule;
import com.wosai.middleware.hera.infra.HeraAgentServiceRule;
import com.wosai.middleware.hera.test.HeraSentinelInMemoryServer;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;
import com.wosai.middleware.hera.tracing.baggage.BaggageField;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import java.lang.reflect.Method;
import java.util.ArrayList;

import static com.wosai.middleware.hera.plugin.jsonrpc4j.define.Constants.REQUEST_APPLICATION;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.any;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.is;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.nameStartsWith;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.named;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.catchRuntimeException;

public class JsonrpcMultiFallbackTest {
    private Method method;

    private MethodInvocationContext context;

    @Rule
    public HeraAgentServiceRule agentServiceRule = new HeraAgentServiceRule();

    @Rule
    public HeraSentinelInMemoryServer heraInMemoryServer = new HeraSentinelInMemoryServer("jsonrpc-test",
            BaggageField.create(REQUEST_APPLICATION));

    @Before
    public void setup() throws Exception {
        context = new MethodInvocationContext();
        heraInMemoryServer.loadRules(
                FlowRule.initFlowRule("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonrpcMultiFallbackTest$Service:hello()", null, 0),
                FlowRule.initFlowRule("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonrpcMultiFallbackTest$Service:helloWorld()", null, 0),
                FlowRule.initFlowRule("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonrpcMultiFallbackTest$Service:world()", null, 0),
                FlowRule.initFlowRule("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonrpcMultiFallbackTest$Service:one()", null, 0),
                FlowRule.initFlowRule("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonrpcMultiFallbackTest$SampleService:hello()", null, 0),
                FlowRule.initFlowRule("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonrpcMultiFallbackTest$GlobalService:hello()", null, 0)
        );
    }

    @After
    public void after() {
        JsonrpcServerFallback.handleAfter(context);
        ResourceMetadataRegistry.INSTANCE.reset();
    }

    @Test
    public void test_withMethodName() throws ClassNotFoundException, NoSuchMethodException {
        ResourceMetadataRegistry.INSTANCE.initializeFallback(new FallbackWithMethodNameDefine());
        Class<?> cache = Class.forName("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel." +
                "JsonrpcMultiFallbackTest$Service");
        method = cache.getDeclaredMethod("helloWorld");
        MethodInvocationContext context = new MethodInvocationContext();
        Exception exception = catchRuntimeException(() ->
                JsonrpcServerFallback.handleBefore(method, new Object[]{null, null, new ArrayList<>()}, context));
        assertThat(exception).isNull();
        assertThat(context.getContext()).isNotNull();
        assertThat(context.isContinue()).isFalse();
        assertThat(context._ret()).isEqualTo("FallbackWithMethodName");
    }

    @Test
    public void test_withMethodNamedOneOf() throws ClassNotFoundException, NoSuchMethodException {
        ResourceMetadataRegistry.INSTANCE.initializeFallback(new FallbackWithMethodNamedOneOfDefine());

        Class<?> cache = Class.forName("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel." +
                "JsonrpcMultiFallbackTest$Service");
        method = cache.getDeclaredMethod("one");

        MethodInvocationContext context = new MethodInvocationContext();
        Exception exception = catchRuntimeException(() ->
                JsonrpcServerFallback.handleBefore(method, new Object[]{null, null, new ArrayList<>()}, context));
        assertThat(exception).isNull();
        assertThat(context.getContext()).isNotNull();
        assertThat(context.isContinue()).isFalse();
        assertThat(context._ret()).isEqualTo("FallbackWithMethodNamedOneOf");
    }

    @Test
    public void testService_withMultiClassDefine() throws ClassNotFoundException, NoSuchMethodException {
        ResourceMetadataRegistry.INSTANCE.initializeFallback(new FallbackWithMultiClassDefine());

        Class<?> cache = Class.forName("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel." +
                "JsonrpcMultiFallbackTest$Service");
        method = cache.getDeclaredMethod("world");

        MethodInvocationContext context = new MethodInvocationContext();
        Exception exception = catchRuntimeException(() ->
                JsonrpcServerFallback.handleBefore(method, new Object[]{null, null, new ArrayList<>()}, context));
        assertThat(exception).isNull();
        assertThat(context.getContext()).isNotNull();
        assertThat(context.isContinue()).isFalse();
        assertThat(context._ret()).isEqualTo("FallbackWithClass");
    }

    @Test
    public void testSampleService_withMultiClassDefine() throws ClassNotFoundException, NoSuchMethodException {
        ResourceMetadataRegistry.INSTANCE.initializeFallback(new FallbackWithMultiClassDefine());

        Class<?> cache = Class.forName("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel." +
                "JsonrpcMultiFallbackTest$SampleService");
        method = cache.getDeclaredMethod("hello");

        MethodInvocationContext context = new MethodInvocationContext();
        Exception exception = catchRuntimeException(() ->
                JsonrpcServerFallback.handleBefore(method, new Object[]{null, null, new ArrayList<>()}, context));
        assertThat(exception).isNull();
        assertThat(context.getContext()).isNotNull();
        assertThat(context.isContinue()).isFalse();
        assertThat(context._ret()).isEqualTo("FallbackWithClass");
    }

    @Test
    public void test_withGlobal() throws ClassNotFoundException, NoSuchMethodException {
        ResourceMetadataRegistry.INSTANCE.initializeFallback(new FallbackGlobalDefine());

        Class<?> cache = Class.forName("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel." +
                "JsonrpcMultiFallbackTest$GlobalService");
        method = cache.getDeclaredMethod("hello");

        MethodInvocationContext context = new MethodInvocationContext();
        Exception exception = catchRuntimeException(() ->
                JsonrpcServerFallback.handleBefore(method, new Object[]{null, null, new ArrayList<>()}, context));
        assertThat(exception).isNull();
        assertThat(context.getContext()).isNotNull();
        assertThat(context.isContinue()).isFalse();
        assertThat(context._ret()).isEqualTo("FallbackGlobal");
    }

    @Test
    public void test_withCloseGlobal() throws ClassNotFoundException, NoSuchMethodException {
        ResourceMetadataRegistry.INSTANCE.initializeFallback(new FallbackCloseGlobalDefine());

        Class<?> cache = Class.forName("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel." +
                "JsonrpcMultiFallbackTest$GlobalService");
        method = cache.getDeclaredMethod("hello");

        MethodInvocationContext context = new MethodInvocationContext();
        Exception exception = catchRuntimeException(() ->
                JsonrpcServerFallback.handleBefore(method, new Object[]{null, null, new ArrayList<>()}, context));
        assertThat(exception).isNull();
        assertThat(context.getContext()).isNull();
        assertThat(context.isContinue()).isTrue();
        assertThat(context._ret()).isNull();
    }

    public static class Service {
        public String hello() {
            return "world";
        }

        public String helloWorld() {
            return "hello world";
        }

        public String world() {

            return "hello";
        }

        public String one() {
            return "hello";
        }

    }

    public static class SampleService {
        public String hello() {
            return "world";
        }
    }

    public static class GlobalService {
        public String hello() {
            return "world";
        }
    }

    public static class FallbackCloseGlobalDefine extends JsonRPCFallbackDefine {
        @Override
        public ElementMatcher<NamedElement.TypeElement> handleClass() {
            return any();
        }

        @Override
        public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
            return new JsonRPCMethodFallbackHandler[]{
                    null
            };
        }

        @Override
        public Provider getProvider() {
            return Provider.SERVER;
        }
    }

    public static class FallbackGlobalDefine extends JsonRPCFallbackDefine {
        @Override
        public ElementMatcher<NamedElement.TypeElement> handleClass() {
            return any();
        }

        @Override
        public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
            return new JsonRPCMethodFallbackHandler[]{
                    new JsonRPCMethodFallbackHandler() {
                        @Override
                        public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                            return any();
                        }

                        @Override
                        public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                            return "FallbackGlobal";
                        }
                    }
            };
        }

        @Override
        public Provider getProvider() {
            return Provider.SERVER;
        }
    }

    public static class FallbackWithMultiClassDefine extends JsonRPCFallbackDefine {
        @Override
        public ElementMatcher<NamedElement.TypeElement> handleClass() {
            return is(Service.class).or(is(SampleService.class));
        }

        @Override
        public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
            return new JsonRPCMethodFallbackHandler[]{
                    new JsonRPCMethodFallbackHandler() {
                        @Override
                        public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                            return any();
                        }

                        @Override
                        public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                            return "FallbackWithClass";
                        }
                    }
            };
        }

        @Override
        public Provider getProvider() {
            return Provider.SERVER;
        }
    }

    public static class FallbackWithMethodNameDefine extends JsonRPCFallbackDefine {
        @Override
        public ElementMatcher<NamedElement.TypeElement> handleClass() {
            return named("com.wosai.middleware.hera.plugin.jsonrpc4j.sentinel.JsonrpcMultiFallbackTest$Service");
        }

        @Override
        public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
            return new JsonRPCMethodFallbackHandler[]{
                    new JsonRPCMethodFallbackHandler() {
                        @Override
                        public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                            return named("hello").or(nameStartsWith("hello"));
                        }

                        @Override
                        public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                            return "FallbackWithMethodName";
                        }
                    }
            };
        }

        @Override
        public Provider getProvider() {
            return Provider.SERVER;
        }
    }

    public static class FallbackWithMethodNamedOneOfDefine extends JsonRPCFallbackDefine {
        @Override
        public ElementMatcher<NamedElement.TypeElement> handleClass() {
            return is(Service.class);
        }

        @Override
        public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
            return new JsonRPCMethodFallbackHandler[]{
                    new JsonRPCMethodFallbackHandler() {
                        @Override
                        public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                            return named("one").or(named("two")).or(named("three"));
                        }

                        @Override
                        public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                            return "FallbackWithMethodNamedOneOf";
                        }
                    }
            };
        }

        @Override
        public JsonRPCFallbackDefine.Provider getProvider() {
            return JsonRPCFallbackDefine.Provider.SERVER;
        }
    }
}
