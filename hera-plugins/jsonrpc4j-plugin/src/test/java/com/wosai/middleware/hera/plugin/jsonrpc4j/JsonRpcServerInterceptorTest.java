package com.wosai.middleware.hera.plugin.jsonrpc4j;

import com.wosai.middleware.hera.agent.core.plugin.interceptor.enhance.v3.InstanceMethodsAroundInterceptorV3;
import com.wosai.middleware.hera.infra.HeraAgentServiceRule;
import com.wosai.middleware.hera.test.HeraInMemoryServer;
import com.wosai.middleware.hera.tracing.network.NetworkSpan;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

public class JsonRpcServerInterceptorTest {
    @Rule
    public MockitoRule rule = MockitoJUnit.rule();
    private InstanceMethodsAroundInterceptor serverInterceptor;
    private InstanceMethodsAroundInterceptorV3 methodInterceptor;

    @Rule
    public HeraInMemoryServer heraInMemoryServer = new HeraInMemoryServer("jsonrpc-test");

    @Rule
    public HeraAgentServiceRule agentServiceRule = new HeraAgentServiceRule();

    private Method method;

    @Mock
    HttpServletRequest req;
    
    @Mock
    HttpServletRequest requestForRepetitiveSlash;

    @Mock
    HttpServletResponse resp;

    @Before
    public void setup() throws NoSuchMethodException, ClassNotFoundException {
        // REQ
        when(req.getHeaderNames()).thenReturn(Collections.emptyEnumeration());
        when(req.getRequestURI()).thenReturn("/rpc");
        when(req.getRequestURL()).thenReturn(new StringBuffer("http://127.0.0.1:8080/rpc/example"));
        when(req.getMethod()).thenReturn("POST");

        // requestForRepetitiveSlash
        when(requestForRepetitiveSlash.getHeaderNames()).thenReturn(Collections.emptyEnumeration());
        when(requestForRepetitiveSlash.getRequestURI()).thenReturn("//rpc");
        when(requestForRepetitiveSlash.getRequestURL()).thenReturn(new StringBuffer("http://127.0.0.1:8080//rpc/example"));
        when(requestForRepetitiveSlash.getMethod()).thenReturn("POST");

        // RESP
        when(resp.getStatus()).thenReturn(200);

        serverInterceptor = new JsonServiceExporterInterceptor();
        methodInterceptor = new JsonRpcBasicServerInvokeInterceptor();

        Class<?> cache = Class.forName("com.wosai.middleware.hera.plugin.jsonrpc4j." +
                "JsonRpcServerInterceptorTest$SampleService");

        // methods
        method = cache.getDeclaredMethod("hello");
    }

    @Test
    public void testJsonRpcServer_whenHTTPOKIsReturned() throws Throwable {
        serverInterceptor.beforeMethod(null, method, new Object[]{req}, new Class<?>[]{HttpServletRequest.class}, null);
        methodInterceptor.beforeMethod(null, method, new Object[]{null, method}, new Class<?>[]{Object.class, Method.class}, null);
        methodInterceptor.afterMethod(null, method, new Object[0], new Class<?>[0], "world", new MethodInvocationContext());
        serverInterceptor.afterMethod(null, method, new Object[]{null, resp}, new Class<?>[0], "world");

        assertThat(heraInMemoryServer.getSpans()).hasSize(1);
        NetworkSpan span = heraInMemoryServer.getSpans().get(0);
        assertThat(span).extracting(NetworkSpan::getName, ns -> ns.getTags().get("http.url"))
                .containsExactly("/rpc.hello", "http://127.0.0.1:8080/rpc/example");
    }

    public static class SampleService {
        public String hello() {
            return "world";
        }
    }

    @Test
    public void testJsonRpcServer_removeRepetitiveSlash() throws Throwable {
        serverInterceptor.beforeMethod(null, method, new Object[]{requestForRepetitiveSlash}, new Class<?>[]{HttpServletRequest.class}, null);
        methodInterceptor.beforeMethod(null, method, new Object[]{null, method}, new Class<?>[]{Object.class, Method.class}, null);
        methodInterceptor.afterMethod(null, method, new Object[0], new Class<?>[0], "world", new MethodInvocationContext());
        serverInterceptor.afterMethod(null, method, new Object[]{null, resp}, new Class<?>[0], "world");

        assertThat(heraInMemoryServer.getSpans()).hasSize(1);
        NetworkSpan span = heraInMemoryServer.getSpans().get(0);
        assertThat(span).extracting(NetworkSpan::getName, ns -> ns.getTags().get("http.url"))
                .containsExactly("/rpc.hello", "http://127.0.0.1:8080//rpc/example");
    }
}
