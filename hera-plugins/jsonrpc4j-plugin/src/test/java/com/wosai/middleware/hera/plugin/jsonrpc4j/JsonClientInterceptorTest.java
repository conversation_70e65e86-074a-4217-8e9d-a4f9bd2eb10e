package com.wosai.middleware.hera.plugin.jsonrpc4j;

import com.wosai.middleware.hera.agent.conf.HeraConfig;
import com.wosai.middleware.hera.infra.HeraAgentServiceRule;
import com.wosai.middleware.hera.test.HeraInMemoryServer;
import com.wosai.middleware.hera.tracing.network.NetworkSpan;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import javax.servlet.http.HttpServletRequest;
import java.net.URL;
import java.util.HashMap;

import static org.assertj.core.api.Assertions.assertThat;

public class JsonClientInterceptorTest {
    @Rule
    public MockitoRule rule = MockitoJUnit.rule();
    private JsonRpcHttpClientInterceptor interceptor;
    @Rule
    public HeraInMemoryServer heraInMemoryServer = new HeraInMemoryServer("jsonrpc-client");
    @Rule
    public HeraAgentServiceRule agentServiceRule = new HeraAgentServiceRule();

    @Before
    public void setup() throws NoSuchFieldException, IllegalAccessException {
        interceptor = new JsonRpcHttpClientInterceptor();
    }

    @After
    public void after() throws Throwable {
        HeraConfig.Kubernetes.Pod.VERSION = "";
    }

    @Test
    public void testJsonRpcServer_whenHTTPOKIsReturned() throws Throwable {
        Enhanced e = new Enhanced();
        interceptor.onConstruct(e, new Object[]{null, new URL("http://127.0.0.1:8080/rpc/remote")});
        interceptor.beforeMethod(e, null, new Object[]{"hello", null, null, new HashMap<>()}, new Class<?>[]{HttpServletRequest.class}, null);
        interceptor.afterMethod(e, null, null, new Class<?>[0], "world", null);

        assertThat(heraInMemoryServer.getSpans()).hasSize(1);
        NetworkSpan span = heraInMemoryServer.getSpans().get(0);
        assertThat(span).extracting(NetworkSpan::getName, ns -> ns.getTags().get("http.url"))
                .containsExactly("/rpc/remote.hello", "http://127.0.0.1:8080/rpc/remote.hello");
    }

    @Test
    public void testJsonRpcClient_removeRepetitiveSlash() throws Throwable {
        Enhanced e = new Enhanced();
        interceptor.onConstruct(e, new Object[]{null, new URL("http://127.0.0.1:8080//rpc/remote")});
        interceptor.beforeMethod(e, null, new Object[]{"hello", null, null, new HashMap<>()}, new Class<?>[]{HttpServletRequest.class}, null);
        interceptor.afterMethod(e, null, null, new Class<?>[0], "world", null);

        assertThat(heraInMemoryServer.getSpans()).hasSize(1);
        NetworkSpan span = heraInMemoryServer.getSpans().get(0);
        assertThat(span).extracting(NetworkSpan::getName, ns -> ns.getTags().get("http.url"))
                .containsExactly("/rpc/remote.hello", "http://127.0.0.1:8080/rpc/remote.hello");
    }

    public static class SampleService {
        public String hello() {
            return "world";
        }
    }

    public static class Enhanced implements EnhancedInstance {
        private Object obj;

        @Override
        public Object getSkyWalkingDynamicField() {
            return obj;
        }

        @Override
        public void setSkyWalkingDynamicField(Object o) {
            this.obj = o;
        }
    }
}
