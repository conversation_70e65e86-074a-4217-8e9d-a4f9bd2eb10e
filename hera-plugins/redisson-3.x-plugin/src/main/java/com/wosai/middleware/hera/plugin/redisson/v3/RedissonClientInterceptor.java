package com.wosai.middleware.hera.plugin.redisson.v3;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.plugin.redisson.v3.util.ClassUtil;
import org.redisson.client.RedisClientConfig;

import java.lang.reflect.Method;
import java.net.InetSocketAddress;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;

public class RedissonClientInterceptor implements InstanceMethodsAroundInterceptor {

    private static final ILog LOGGER = LogManager.getLogger(RedisConnectionMethodInterceptor.class);

    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, MethodInterceptResult methodInterceptResult) throws Throwable {

    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Object o) throws Throwable {
        String redisUri;
        try {
            RedisClientConfig config = (RedisClientConfig) ClassUtil.getObjectField(enhancedInstance, "config");
            try {
                redisUri = config.getAddress().getHost() + ":" + config.getAddress().getPort();
            } catch (NoSuchMethodError e) {
                Object uri = ClassUtil.getObjectField(config, "address");
                redisUri = ((URI) uri).getHost() + ":" + ((URI) uri).getPort();
            }
        } catch (NoSuchFieldException e) {
            Object address = ClassUtil.getObjectField(enhancedInstance, "addr");
            redisUri = ((InetSocketAddress) address).getHostName() + ":" + ((InetSocketAddress) address).getPort();
        }

        String finalRedisUri = redisUri;
        List<String> metricsNameLst = new ArrayList<>();
        for (RedissonMetricsName redissonMetricsName: RedissonMetricsName.REDISSON_METRICS_NAMES) {
            metricsNameLst.add(redissonMetricsName.getMetricName());
        }
        MetricsHandler.removeMeterNamesIfPresent(metricsNameLst, "redisUri", value -> value.equals(finalRedisUri));
        return o;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {

    }
}
