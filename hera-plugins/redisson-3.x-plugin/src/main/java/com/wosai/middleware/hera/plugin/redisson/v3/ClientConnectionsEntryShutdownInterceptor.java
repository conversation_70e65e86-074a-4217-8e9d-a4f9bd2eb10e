package com.wosai.middleware.hera.plugin.redisson.v3;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.plugin.redisson.v3.util.ClassUtil;
import org.redisson.client.RedisClient;
import org.redisson.client.RedisClientConfig;
import org.redisson.connection.ClientConnectionsEntry;

import java.lang.reflect.Method;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;

public class ClientConnectionsEntryShutdownInterceptor implements InstanceMethodsAroundInterceptor {

    private static final ILog LOGGER = LogManager.getLogger(RedisConnectionMethodInterceptor.class);

    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, MethodInterceptResult methodInterceptResult) throws Throwable {
        RedisClient redisClient = ((ClientConnectionsEntry) enhancedInstance).getClient();
        RedisClientConfig config = (RedisClientConfig) ClassUtil.getObjectField(redisClient, "config");
        String redisUri;
        try {
            redisUri = config.getAddress().getHost() + ":" + config.getAddress().getPort();
        } catch (NoSuchMethodError e) {
            Object uri = ClassUtil.getObjectField(config, "address");
            redisUri = ((URI) uri).getHost() + ":" + ((URI) uri).getPort();
        }
        List<String> metricsNameLst = new ArrayList<>();
        for (RedissonMetricsName redissonMetricsName: RedissonMetricsName.REDISSON_METRICS_NAMES) {
            metricsNameLst.add(redissonMetricsName.getMetricName());
        }
        String finalRedisUri = redisUri;
        MetricsHandler.removeMeterNamesIfPresent(metricsNameLst, "redisUri", value -> value.equals(finalRedisUri));
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Object o) throws Throwable {
        return o;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {

    }
}
