package com.wosai.middleware.hera.plugin.redisson.v3;

import com.wosai.middleware.hera.agent.conf.dynamic.MetaConfigurationDiscoveryService;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;

public class RedissonConstructorInterceptor implements InstanceConstructorInterceptor {
    @Override
    public void onConstruct(EnhancedInstance enhancedInstance, Object[] objects) throws Throwable {
        TraceRedissonParametersWatcher traceRedissonParametersWatcher = new TraceRedissonParametersWatcher("plugin.redis.trace_parameters");
        MetaConfigurationDiscoveryService configurationDiscoveryService = ServiceManager.INSTANCE.findService(
                MetaConfigurationDiscoveryService.class);
        configurationDiscoveryService.registerAgentConfigChangeWatcher(traceRedissonParametersWatcher);
    }
}
