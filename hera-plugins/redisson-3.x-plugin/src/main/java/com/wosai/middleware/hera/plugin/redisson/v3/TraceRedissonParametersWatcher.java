package com.wosai.middleware.hera.plugin.redisson.v3;

import org.apache.skywalking.apm.agent.core.conf.dynamic.AgentConfigChangeWatcher;
import org.apache.skywalking.apm.plugin.redisson.v3.RedissonPluginConfig;

public class TraceRedissonParametersWatcher extends AgentConfigChangeWatcher {

    private final boolean defaultValue;

    public TraceRedissonParametersWatcher(String propertyKey) {
        super(propertyKey);
        defaultValue = RedissonPluginConfig.Plugin.Redisson.TRACE_REDIS_PARAMETERS;
    }

    @Override
    public void notify(ConfigChangeEvent value) {
        if (EventType.DELETE.equals(value.getEventType())) {
            RedissonPluginConfig.Plugin.Redisson.TRACE_REDIS_PARAMETERS = defaultValue;
        } else {
            RedissonPluginConfig.Plugin.Redisson.TRACE_REDIS_PARAMETERS = Boolean.parseBoolean(value.getNewValue());
        }
    }

    @Override
    public String value() {
        return Boolean.toString(RedissonPluginConfig.Plugin.Redisson.TRACE_REDIS_PARAMETERS);
    }
}
