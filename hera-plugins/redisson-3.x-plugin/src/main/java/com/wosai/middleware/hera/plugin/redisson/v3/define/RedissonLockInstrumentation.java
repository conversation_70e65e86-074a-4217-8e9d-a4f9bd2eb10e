package com.wosai.middleware.hera.plugin.redisson.v3.define;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.InstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.ClassInstanceMethodsEnhancePluginDefine;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;
import org.apache.skywalking.apm.agent.core.plugin.match.HierarchyMatch;
import org.apache.skywalking.apm.agent.core.plugin.match.MultiClassNameMatch;
import org.apache.skywalking.apm.agent.core.plugin.match.logical.LogicalMatchOperation;

import static net.bytebuddy.matcher.ElementMatchers.named;
import static org.apache.skywalking.apm.agent.core.plugin.bytebuddy.ArgumentTypeNameMatch.takesArgumentWithType;

public class RedissonLockInstrumentation extends ClassInstanceMethodsEnhancePluginDefine {

    private static final String REDISSON_LOCK_CLASS = "org.redisson.RedissonLock";

    private static final String REDISSON_SPIN_LOCK_CLASS = "org.redisson.RedissonSpinLock";

    private static final String REDISSON_LOCK_INTERCEPTOR = "com.wosai.middleware.hera.plugin.redisson.v3.RedissonLockInterceptor";

    private static final String REDISSON_HIGH_LEVEL_LOCK_INTERCEPTOR = "com.wosai.middleware.hera.plugin.redisson.v3.RedissonHighLevelLockInterceptor";

    @Override
    protected ClassMatch enhanceClass() {
        return LogicalMatchOperation.or(HierarchyMatch.byHierarchyMatch(REDISSON_LOCK_CLASS), MultiClassNameMatch.byMultiClassMatch(REDISSON_LOCK_CLASS, REDISSON_SPIN_LOCK_CLASS));
    }

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[0];
    }

    @Override
    public InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints() {
        return new InstanceMethodsInterceptPoint[]{
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("tryLockInnerAsync").and(takesArgumentWithType(1, "java.util.concurrent.TimeUnit"));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return REDISSON_LOCK_INTERCEPTOR;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("tryLockInnerAsync").and(takesArgumentWithType(2, "java.util.concurrent.TimeUnit"));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return REDISSON_HIGH_LEVEL_LOCK_INTERCEPTOR;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }
}