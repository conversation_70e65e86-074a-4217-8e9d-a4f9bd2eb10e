package com.wosai.middleware.hera.plugin.redisson.v3;

public enum RedissonMetricsName {

    REDISSON_CONNECTIONS_FREE("redisson.connections.free"),
    REDISSON_CONNECTIONS_TOTAL("redisson.connections.total"),
    RED<PERSON>SON_PUB_SUB_CONNECTIONS_FREE("redisson.publish-subscribe-connections.free"),
    REDISSON_PUB_SUB_CONNECTIONS_TOTAL("redisson.publish-subscribe-connections.total"),
    REDISSON_CONNECTIONS_FREE_SEMAPHORE_COUNTER("redisson.connections.free.semaphore.counter"),
    REDISSON_CONNECTIONS_FREE_SEMAPHORE_QUEUE_SIZE("redisson.connections.free.semaphore.queue.size"),
    REDISSON_PUB_SUB_CONNECTIONS_FREE_SEMAPHORE_COUNTER("redisson.publish-subscribe-connections.free.semaphore.counter"),
    REDISSON_PUB_SUB_CONNECTIONS_FREE_SEMAPHORE_QUEUE_SIZE("redisson.publish-subscribe-connections.free.semaphore.queue.size");

    private final String redissonMetricsName;

    RedissonMetricsName(String redissonMetricsName) {
        this.redissonMetricsName = redissonMetricsName;
    }

    public String getMetricName() {
        return redissonMetricsName;
    }

    public static final RedissonMetricsName[] REDISSON_METRICS_NAMES = RedissonMetricsName.values();
}
