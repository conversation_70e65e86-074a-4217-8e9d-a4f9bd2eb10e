# mysql-8.x plugin
mysql-8.x=org.apache.skywalking.apm.plugin.jdbc.mysql.v8.define.ConnectionImplCreateInstrumentation
mysql-8.x=com.wosai.middleware.hera.plugin.jdbc.mysql.v8.define.ConnectionInstrumentation
mysql-8.x=com.wosai.middleware.hera.plugin.jdbc.mysql.v8.define.CallableInstrumentation
mysql-8.x=com.wosai.middleware.hera.plugin.jdbc.mysql.v8.define.PreparedStatementInstrumentation
mysql-8.x=com.wosai.middleware.hera.plugin.jdbc.mysql.v8.define.StatementInstrumentation
mysql-8.x=org.apache.skywalking.apm.plugin.jdbc.mysql.v8.define.PreparedStatementSetterInstrumentation
mysql-8.x=org.apache.skywalking.apm.plugin.jdbc.mysql.v8.define.PreparedStatementNullSetterInstrumentation
mysql-8.x=org.apache.skywalking.apm.plugin.jdbc.mysql.v8.define.PreparedStatementIgnoredSetterInstrumentation
mysql-8.x=com.wosai.middleware.hera.plugin.jdbc.mysql.v8.define.CacheIpsDriverInstrumentation