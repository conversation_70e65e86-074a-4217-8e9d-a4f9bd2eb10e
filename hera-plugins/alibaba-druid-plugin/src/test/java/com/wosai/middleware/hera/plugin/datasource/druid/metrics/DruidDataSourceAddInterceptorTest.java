package com.wosai.middleware.hera.plugin.datasource.druid.metrics;

import com.alibaba.druid.pool.DruidDataSource;
import com.wosai.middleware.hera.agent.metrics.BaseUnits;
import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.MeterId;
import com.wosai.middleware.hera.agent.metrics.api.Tags;
import com.wosai.middleware.hera.plugin.datasource.druid.DruidDataSourceAddInterceptor;
import com.wosai.middleware.hera.test.HeraInMemoryMetrics;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class DruidDataSourceAddInterceptorTest {
    private DruidDataSourceAddInterceptor interceptor;
    private DruidDataSource druidDataSource;
    @Rule
    public HeraInMemoryMetrics heraInMemoryMetrics = new HeraInMemoryMetrics();

    @Before
    public void setup() throws Exception {
        interceptor = new DruidDataSourceAddInterceptor();
        druidDataSource = new DruidDataSource();
        druidDataSource.setUrl("******************************************************");
        druidDataSource.init();
    }

    @After
    public void teardown() {
        if (druidDataSource != null) {
            druidDataSource.close();
        }
    }

    @Test
    public void testBeforeAndAfterHooks() throws Throwable {
        MethodInvocationContext methodInvocationContext = new MethodInvocationContext();
        interceptor.beforeMethod(null, null, new Object[]{druidDataSource}, null, methodInvocationContext);
        interceptor.afterMethod(null, null, null, null, new Object(), methodInvocationContext);
        assertThat(MetricsHandler.getMeters()).hasSize(33);

        assertThat(MetricsHandler.find("druid.connection.close").gauge().getId()).isEqualTo(
                MeterId.dummy("druid.connection.close", Tags.of("name", druidDataSource.getName()))
        ).extracting(MeterId::getBaseUnit).isEqualTo(BaseUnits.CONNECTIONS);

        assertThat(MetricsHandler.find("druid.connection.milli.sum").gauge().getId()).isEqualTo(
                MeterId.dummy("druid.connection.milli.sum", Tags.of("name", druidDataSource.getName()))
        ).extracting(MeterId::getBaseUnit).isEqualTo(BaseUnits.MILLISECONDS);

        assertThat(MetricsHandler.find("druid.resultset.opening").gauge().getId()).isEqualTo(
                MeterId.dummy("druid.resultset.opening", Tags.of("name", druidDataSource.getName()))
        ).extracting(MeterId::getBaseUnit).isEqualTo(BaseUnits.OPERATIONS);

        assertThat(MetricsHandler.find("druid.resultset.error").gauge().getId()).isEqualTo(
                MeterId.dummy("druid.resultset.error", Tags.of("name", druidDataSource.getName()))
        ).extracting(MeterId::getBaseUnit).isEqualTo(BaseUnits.EVENTS);

        assertThat(MetricsHandler.find("druid.max.wait.thread.count").gauge().getId()).isEqualTo(
                MeterId.dummy("druid.max.wait.thread.count", Tags.of("name", druidDataSource.getName()))
        ).extracting(MeterId::getBaseUnit).isEqualTo(BaseUnits.THREADS);
    }
}
