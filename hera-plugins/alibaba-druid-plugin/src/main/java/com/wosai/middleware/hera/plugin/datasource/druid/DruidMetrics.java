package com.wosai.middleware.hera.plugin.datasource.druid;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.stat.JdbcConnectionStat;
import com.alibaba.druid.stat.JdbcDataSourceStat;
import com.alibaba.druid.stat.JdbcResultSetStat;
import com.alibaba.druid.stat.JdbcStatementStat;
import com.wosai.middleware.hera.agent.metrics.BaseUnits;
import com.wosai.middleware.hera.agent.metrics.MeterBinder;
import com.wosai.middleware.hera.agent.metrics.api.Gauge;

public class DruidMetrics implements MeterBinder {
    private final DruidDataSource dataSource;

    public DruidMetrics(DruidDataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public void bindTo() {
        registerJdbcDataSourceMetrics();
    }

    private void registerJdbcDataSourceMetrics() {
        final JdbcDataSourceStat jdbcDataSourceStat = this.dataSource.getDataSourceStat();
        registerDatasourceMetricsMBean(this.dataSource);
        registerJdbcConnectionStatMBean(jdbcDataSourceStat.getConnectionStat());
        registerStatementStatMBean(jdbcDataSourceStat.getStatementStat());
        registerResultSetStatMBean(jdbcDataSourceStat.getResultSetStat());
    }

    // Bind
    private void registerJdbcConnectionStatMBean(JdbcConnectionStat connectionStat) {
        // Connection Creation/Destroy/Active
        Gauge.builder("druid.connection.close", connectionStat::getCloseCount).description("Connections closed").tag("name", dataSource.getName()).baseUnit(BaseUnits.CONNECTIONS).build();
        Gauge.builder("druid.connection.active", connectionStat::getActiveCount).description("Active connection").tag("name", dataSource.getName()).baseUnit(BaseUnits.CONNECTIONS).build();
        Gauge.builder("druid.connection.active.max", connectionStat::getActiveMax).description("Max active connection").tag("name", dataSource.getName()).baseUnit(BaseUnits.CONNECTIONS).build();
        // Operations
        Gauge.builder("druid.connection.commit", connectionStat::getCommitCount).description("Connection commit count").tag("name", dataSource.getName()).baseUnit(BaseUnits.CONNECTIONS).build();
        Gauge.builder("druid.connection.rollback", connectionStat::getRollbackCount).description("Connection rollback count").tag("name", dataSource.getName()).baseUnit(BaseUnits.MILLISECONDS).build();
        // Others
        Gauge.builder("druid.connection.connect.error", connectionStat::getConnectErrorCount).description("Connect error count").tag("name", dataSource.getName()).build();
        // Mimic timer
        Gauge.builder("druid.connection.milli.count", connectionStat::getConnectCount).description("Connections created").tag("name", dataSource.getName()).baseUnit(BaseUnits.CONNECTIONS).build();
        Gauge.builder("druid.connection.milli.sum", connectionStat::getConnectMillis).description("Total time for connection construction").tag("name", dataSource.getName()).baseUnit(BaseUnits.MILLISECONDS).build();
    }

    private void registerResultSetStatMBean(JdbcResultSetStat resultSetStat) {
        Gauge.builder("druid.resultset.opening", resultSetStat::getOpeningCount).description("Opening resultset").tag("name", dataSource.getName()).baseUnit(BaseUnits.OPERATIONS).build();
        Gauge.builder("druid.resultset.opening.max", resultSetStat::getOpeningMax).description("Max opening resultset").tag("name", dataSource.getName()).baseUnit(BaseUnits.OPERATIONS).build();
        Gauge.builder("druid.resultset.open", resultSetStat::getOpenCount).description("Resultset opened").tag("name", dataSource.getName()).baseUnit(BaseUnits.OPERATIONS).build();
        Gauge.builder("druid.resultset.error", resultSetStat::getErrorCount).description("Resultset error count").tag("name", dataSource.getName()).baseUnit(BaseUnits.EVENTS).build();
    }

    private void registerStatementStatMBean(JdbcStatementStat statementStat) {
        // statement operation
        Gauge.builder("druid.statement.create", statementStat::getCreateCount).description("Statements created").tag("name", dataSource.getName()).baseUnit(BaseUnits.OPERATIONS).build();
        Gauge.builder("druid.statement.close", statementStat::getCloseCount).description("Statements closed").tag("name", dataSource.getName()).baseUnit(BaseUnits.OPERATIONS).build();
        Gauge.builder("druid.statement.prepare", statementStat::getPrepareCount).description("Statement prepared").tag("name", dataSource.getName()).baseUnit(BaseUnits.OPERATIONS).build();
        // current status
        Gauge.builder("druid.statement.running", statementStat::getRunningCount).description("Running statements").tag("name", dataSource.getName()).baseUnit(BaseUnits.OPERATIONS).build();
        Gauge.builder("druid.statement.concurrent.max", statementStat::getConcurrentMax).description("Max concurrent running statements").tag("name", dataSource.getName()).baseUnit(BaseUnits.OPERATIONS).build();
        // execution stat
        Gauge.builder("druid.statement.execution", statementStat::getExecuteCount).description("Execution count").tag("name", dataSource.getName()).baseUnit(BaseUnits.OPERATIONS).build();
        Gauge.builder("druid.statement.execution.error", statementStat::getErrorCount).description("Execution error count").tag("name", dataSource.getName()).baseUnit(BaseUnits.OPERATIONS).build();
    }

    private void registerDatasourceMetricsMBean(DruidDataSource druidDataSource) {
        Gauge.builder("druid.max.idle", druidDataSource::getMaxIdle).description("Max Idle").tag("name", druidDataSource.getName()).baseUnit(BaseUnits.CONNECTIONS).build();
        Gauge.builder("druid.pooling.count", druidDataSource::getPoolingCount).description("Pooling count").tag("name", druidDataSource.getName()).baseUnit(BaseUnits.CONNECTIONS).build();
        Gauge.builder("druid.pooling.peak", druidDataSource::getPoolingPeak).description("Pooling peak").tag("name", druidDataSource.getName()).baseUnit(BaseUnits.CONNECTIONS).build();
        Gauge.builder("druid.lock.queue.length", druidDataSource::getLockQueueLength).description("Lock Queue Length").tag("name", druidDataSource.getName()).build();
        Gauge.builder("druid.max.wait.thread.count", druidDataSource::getMaxWaitThreadCount).description("Max Wait Thread Count").tag("name", druidDataSource.getName()).baseUnit(BaseUnits.THREADS).build();
        Gauge.builder("druid.wait.thread.count", druidDataSource::getWaitThreadCount).description("Wait Thread Count").tag("name", druidDataSource.getName()).baseUnit(BaseUnits.THREADS).build();
        Gauge.builder("druid.max.wait", druidDataSource::getMaxWait).description("Max Wait").tag("name", druidDataSource.getName()).build();
        Gauge.builder("druid.notEmpty.wait.count", druidDataSource::getNotEmptyWaitCount).description("Not Empty Wait Count").tag("name", druidDataSource.getName()).build();
        Gauge.builder("druid.notEmpty.wait.count.millis", druidDataSource::getNotEmptyWaitMillis).description("Not Empty Wait Count Millis").tag("name", druidDataSource.getName()).build();
        Gauge.builder("druid.cached.preparedStatement.count", druidDataSource::getCachedPreparedStatementCount).description("Cached Prepared Statement Count").tag("name", druidDataSource.getName()).build();
        Gauge.builder("druid.cached.preparedStatement.hit.count", druidDataSource::getCachedPreparedStatementHitCount).description("Cached Prepared Statement Hit Count").tag("name", druidDataSource.getName()).build();
        Gauge.builder("druid.cached.preparedStatement.access.count", druidDataSource::getCachedPreparedStatementAccessCount).description("Cached Prepared Statement Access Count").tag("name", druidDataSource.getName()).build();
        Gauge.builder("druid.max.open.preparedStatement", druidDataSource::getMaxOpenPreparedStatements).description("Max Open Prepared Statements").tag("name", druidDataSource.getName()).build();
        Gauge.builder("druid.max.pool.preparedStatement.per.connection.size", druidDataSource::getMaxPoolPreparedStatementPerConnectionSize).description("Max Pool Prepared Statement PerConnection Size").tag("name", druidDataSource.getName()).build();
    }
}
