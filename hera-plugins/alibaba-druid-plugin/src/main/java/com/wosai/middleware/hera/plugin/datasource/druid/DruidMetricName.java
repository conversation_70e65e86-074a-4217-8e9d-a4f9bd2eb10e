package com.wosai.middleware.hera.plugin.datasource.druid;

public enum DruidMetricName {

    // Connection metrics
    DRUID_CONNECTION_CLOSE("druid.connection.close"),
    DRUID_CONNECTION_ACTIVE("druid.connection.active"),
    DRUID_CONNECTION_ACTIVE_MAX("druid.connection.active.max"),
    DRUID_CONNECTION_COMMIT("druid.connection.commit"),
    DRUID_CONNECTION_ROLLBACK("druid.connection.rollback"),
    DRUID_CONNECTION_CONNECT_ERROR("druid.connection.connect.error"),
    DRUID_CONNECTION_MILLI_COUNT("druid.connection.milli.count"),
    DRUID_CONNECTION_MILLI_SUM("druid.connection.milli.sum"),

    // ResultSet metrics
    DRUID_RESULTSET_OPENING("druid.resultset.opening"),
    DRUID_RESULTSET_OPENING_MAX("druid.resultset.opening.max"),
    DRUID_RESULTSET_OPEN("druid.resultset.open"),
    DRUID_RESULTSET_ERROR("druid.resultset.error"),

    // Statement metrics
    DRUID_STATEMENT_CREATE("druid.statement.create"),
    DRUID_STATEMENT_CLOSE("druid.statement.close"),
    DRUID_STATEMENT_PREPARE("druid.statement.prepare"),
    DRUID_STATEMENT_RUNNING("druid.statement.running"),
    DRUID_STATEMENT_CONCURRENT_MAX("druid.statement.concurrent.max"),
    DRUID_STATEMENT_EXECUTION("druid.statement.execution"),
    DRUID_STATEMENT_EXECUTION_ERROR("druid.statement.execution.error"),

    // DataSource metrics
    DRUID_MAX_IDLE("druid.max.idle"),
    DRUID_POOLING_COUNT("druid.pooling.count"),
    DRUID_POOLING_PEAK("druid.pooling.peak"),
    DRUID_LOCK_QUEUE_LENGTH("druid.lock.queue.length"),
    DRUID_MAX_WAIT_THREAD_COUNT("druid.max.wait.thread.count"),
    DRUID_WAIT_THREAD_COUNT("druid.wait.thread.count"),
    DRUID_MAX_WAIT("druid.max.wait"),
    DRUID_NOT_EMPTY_WAIT_COUNT("druid.notEmpty.wait.count"),
    DRUID_NOT_EMPTY_WAIT_COUNT_MILLIS("druid.notEmpty.wait.count.millis"),
    DRUID_CACHED_PREPARED_STATEMENT_COUNT("druid.cached.preparedStatement.count"),
    DRUID_CACHED_PREPARED_STATEMENT_HIT_COUNT("druid.cached.preparedStatement.hit.count"),
    DRUID_CACHED_PREPARED_STATEMENT_ACCESS_COUNT("druid.cached.preparedStatement.access.count"),
    DRUID_MAX_OPEN_PREPARED_STATEMENT("druid.max.open.preparedStatement"),
    DRUID_MAX_POOL_PREPARED_STATEMENT_PER_CONNECTION_SIZE("druid.max.pool.preparedStatement.per.connection.size");

    private final String druidMetricName;

    DruidMetricName(String druidMetricName) {
        this.druidMetricName = druidMetricName;
    }

    public String getMetricName() {
        return druidMetricName;
    }

    public static final DruidMetricName[] DRUID_METRIC_NAMES = DruidMetricName.values();

}
