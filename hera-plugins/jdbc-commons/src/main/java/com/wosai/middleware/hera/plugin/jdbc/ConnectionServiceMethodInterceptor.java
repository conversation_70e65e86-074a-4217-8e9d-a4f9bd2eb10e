package com.wosai.middleware.hera.plugin.jdbc;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.plugin.jdbc.trace.ConnectionInfo;

import java.lang.reflect.Method;

/**
 * {@link ConnectionServiceMethodInterceptor} create an exit span when the following methods execute:
 * 1. close
 * 2. rollback
 * 3. releaseSavepoint
 * 4. commit
 *
 * <AUTHOR>
 */
public class ConnectionServiceMethodInterceptor implements InstanceMethodsAroundInterceptor {

    @Override
    public final void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments,
                                   Class<?>[] argumentsTypes,
                                   MethodInterceptResult result) throws Throwable {
        ConnectionInfo connectInfo = (ConnectionInfo) objInst.getSkyWalkingDynamicField();
        if (connectInfo != null) {
            AbstractHeraSpan span = ContextManager.createExitSpan(connectInfo.getDBType() + "/JDBC/Connection/" + method.getName(), connectInfo.getDatabasePeer());
            Tags.DB_TYPE.set(span, connectInfo.getDBType());
            Tags.DB_INSTANCE.set(span, connectInfo.getDatabaseName());
            Tags.DB_STATEMENT.set(span, "");
            Tags.COMPONENT.set(span, connectInfo.getComponent().getName());
        }
    }

    @Override
    public final Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments,
                                    Class<?>[] argumentsTypes,
                                    Object ret) throws Throwable {
        ConnectionInfo connectInfo = (ConnectionInfo) objInst.getSkyWalkingDynamicField();
        if (connectInfo != null) {
            ContextManager.stopSpan();
        }
        return ret;
    }

    @Override
    public final void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                            Class<?>[] argumentsTypes, Throwable t) {
        ContextManager.logError(t);
    }

}