package com.wosai.middleware.hera.plugin.jdbc.tomcat;

import com.wosai.middleware.hera.agent.metrics.MeterBinder;
import com.wosai.middleware.hera.agent.metrics.api.Gauge;
import org.apache.tomcat.jdbc.pool.DataSourceProxy;

class DataSourceMetrics implements MeterBinder {
    private final DataSourceProxy dataSourceProxy;

    public DataSourceMetrics(DataSourceProxy dataSourceProxy) {
        this.dataSourceProxy = dataSourceProxy;
    }

    @Override
    public void bindTo() {
        Gauge.builder("tomcat.jdbc.pool.idle.min", dataSourceProxy, DataSourceProxy::getMinIdle).build();
        Gauge.builder("tomcat.jdbc.pool.idle", dataSourceProxy, DataSourceProxy::getIdle).build();
        Gauge.builder("tomcat.jdbc.pool.idle.max", dataSourceProxy, DataSourceProxy::getMaxIdle).build();
        Gauge.builder("tomcat.jdbc.pool.active", dataSourceProxy, DataSourceProxy::getActive).build();
        Gauge.builder("tomcat.jdbc.pool.active.max", dataSourceProxy, DataSourceProxy::getMaxActive).build();

        Gauge.builder("tomcat.jdbc.pool.size", dataSourceProxy, DataSourceProxy::getSize).build();
        Gauge.builder("tomcat.jdbc.pool.wait.max", dataSourceProxy, DataSourceProxy::getMaxWait).build();
        Gauge.builder("tomcat.jdbc.pool.wait.count", dataSourceProxy, DataSourceProxy::getWaitCount).build();
        Gauge.builder("tomcat.jdbc.pool.default.transaction.isolation", dataSourceProxy, DataSourceProxy::getDefaultTransactionIsolation).build();
        Gauge.builder("tomcat.jdbc.pool.initial.size", dataSourceProxy, DataSourceProxy::getInitialSize).build();

        Gauge.builder("tomcat.jdbc.pool.evictable.idle.time.millis.min", dataSourceProxy, DataSourceProxy::getMinEvictableIdleTimeMillis).build();
        Gauge.builder("tomcat.jdbc.pool.age.max", dataSourceProxy, DataSourceProxy::getMaxAge).build();
        Gauge.builder("tomcat.jdbc.pool.remove.abandoned.timeout", dataSourceProxy, DataSourceProxy::getRemoveAbandonedTimeout).build();
        Gauge.builder("tomcat.jdbc.pool.time.between.eviction.runs.millis", dataSourceProxy, DataSourceProxy::getTimeBetweenEvictionRunsMillis).build();
        Gauge.builder("tomcat.jdbc.pool.validation.interval", dataSourceProxy, DataSourceProxy::getValidationInterval).build();

        Gauge.builder("tomcat.jdbc.pool.validation.query.timeout", dataSourceProxy, DataSourceProxy::getValidationQueryTimeout).build();
        Gauge.builder("tomcat.jdbc.pool.abandon.when.percentage.full", dataSourceProxy, DataSourceProxy::getAbandonWhenPercentageFull).build();
    }
}