/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.wosai.middleware.hera.plugin.spring.commons;

import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import org.apache.skywalking.apm.network.trace.component.OfficialComponent;

public class EnhanceCacheObjects {
    private OfficialComponent component;

    private String operationName;
    private AbstractHeraSpan parentSpan;

    public EnhanceCacheObjects(String operationName, OfficialComponent component, AbstractHeraSpan span) {
        this.component = component;
        this.operationName = operationName;
        parentSpan = span;
    }

    public EnhanceCacheObjects(String operationName, AbstractHeraSpan span) {
        this(operationName, null, span);
    }

    public OfficialComponent getComponent() {
        return component;
    }

    public String getOperationName() {
        return operationName;
    }

    public AbstractHeraSpan getParentSpan() {
        return parentSpan;
    }
}
