/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.wosai.middleware.hera.plugin.spring.concurrent;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.plugin.spring.commons.EnhanceCacheObjects;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;

import java.lang.reflect.Method;

public class FailureCallbackInterceptor implements InstanceMethodsAroundInterceptor {

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Throwable {
        EnhanceCacheObjects cacheValues = (EnhanceCacheObjects) objInst.getSkyWalkingDynamicField();
        if (cacheValues == null) {
            return;
        }

        AbstractHeraSpan span = ContextManager.createLocalSpan("future/failureCallback:" + cacheValues.getOperationName(), cacheValues.getParentSpan().context());
        ContextManager.logError((Throwable) allArguments[0]);
        Tags.COMPONENT.set(span, cacheValues.getComponent().getName());
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) throws Throwable {
        EnhanceCacheObjects cacheValues = (EnhanceCacheObjects) objInst.getSkyWalkingDynamicField();
        if (cacheValues == null) {
            return ret;
        }
        ContextManager.stopSpan();
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {
        EnhanceCacheObjects cacheValues = (EnhanceCacheObjects) objInst.getSkyWalkingDynamicField();
        if (cacheValues == null) {
            return;
        }
        ContextManager.logError(t);
    }
}
