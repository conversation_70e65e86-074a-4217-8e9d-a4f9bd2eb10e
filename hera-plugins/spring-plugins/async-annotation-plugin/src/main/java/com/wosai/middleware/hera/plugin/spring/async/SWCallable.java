/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.wosai.middleware.hera.plugin.spring.async;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;

import java.util.concurrent.Callable;

public class SWCallable<V> implements Callable<V> {

    private static final String OPERATION_NAME = "SpringAsync";

    private Callable<V> callable;

    private AbstractHeraSpan parentSpan;

    SWCallable(Callable<V> callable, AbstractHeraSpan parentSpan) {
        this.callable = callable;
        this.parentSpan = parentSpan;
    }

    @Override
    public V call() throws Exception {
        AbstractHeraSpan span = ContextManager.createLocalSpan(SWCallable.OPERATION_NAME, parentSpan.context());
        Tags.COMPONENT.set(span, ComponentsDefine.SPRING_ASYNC.getName());
        try {

            return callable.call();
        } catch (Exception e) {
            ContextManager.logError(e);
            throw e;
        } finally {
            ContextManager.stopSpan();
        }
    }
}
