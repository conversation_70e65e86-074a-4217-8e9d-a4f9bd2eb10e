package com.wosai.middleware.hera.plugin.spring.data.redis.v1;

import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;
import org.apache.skywalking.apm.util.StringUtil;

public class RedisCacheConstructorInterceptor implements InstanceConstructorInterceptor {
    @Override
    public void onConstruct(EnhancedInstance enhancedInstance, Object[] arguments) throws Throwable {
        final String cacheName = (String) arguments[0];
        if (StringUtil.isEmpty(cacheName)) {
            enhancedInstance.setSkyWalkingDynamicField("unknown");
        } else {
            enhancedInstance.setSkyWalkingDynamicField(cacheName);
        }
    }
}
