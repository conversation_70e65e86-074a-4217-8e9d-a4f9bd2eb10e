package com.wosai.middleware.hera.plugin.spring.data.redis.v2;

import com.wosai.middleware.hera.plugin.spring.data.redis.RedisCacheStatistics;

public class CacheGetInterceptor extends AbstractExceptionAwareInterceptor {
    @Override
    void record(String cacheName, Object returnObj) {
        // increase gets
        if (returnObj != null) {
            RedisCacheStatistics.incHits(cacheName);
        } else {
            RedisCacheStatistics.incMisses(cacheName);
        }
    }
}
