package com.wosai.middleware.hera.plugin.spring.data.redis.define;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.ClassInstanceMethodsEnhancePluginDefineV2;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.v2.InstanceMethodsInterceptV2Point;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;

import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.takesArgument;
import static org.apache.skywalking.apm.agent.core.plugin.bytebuddy.ArgumentTypeNameMatch.takesArgumentWithType;
import static org.apache.skywalking.apm.agent.core.plugin.match.NameMatch.byName;

/**
 * SpringDataRedisV1Instrumentation is for spring-data-redis < 2.0.0 and >= 1.5.
 * It intends to enhance methods in RedisCache
 */
public class SpringDataRedisV1Instrumentation extends ClassInstanceMethodsEnhancePluginDefineV2 {
    private static final String ENHANCE_CLASS = "org.springframework.data.redis.cache.RedisCache";

    @Override
    protected ClassMatch enhanceClass() {
        return byName(ENHANCE_CLASS);
    }

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[]{
                new ConstructorInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getConstructorMatcher() {
                        return takesArgument(0, String.class);
                    }

                    @Override
                    public String getConstructorInterceptor() {
                        return "com.wosai.middleware.hera.plugin.spring.data.redis.v1.RedisCacheConstructorInterceptor";
                    }
                }
        };
    }

    @Override
    public InstanceMethodsInterceptV2Point[] getInstanceMethodsInterceptV2Points() {
        return new InstanceMethodsInterceptV2Point[]{
                // public RedisCacheElement get(final RedisCacheKey cacheKey) {...}
                new InstanceMethodsInterceptV2Point() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("get").and(takesArgumentWithType(0, "org.springframework.data.redis.cache.RedisCacheKey"));
                    }

                    @Override
                    public String getMethodsInterceptorV2() {
                        return "com.wosai.middleware.hera.plugin.spring.data.redis.v1.CacheGetInterceptor";
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                // public void evict(final RedisCacheElement element) {...}
                new InstanceMethodsInterceptV2Point() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("evict").and(takesArgumentWithType(0, "org.springframework.data.redis.cache.RedisCacheElement"));
                    }

                    @Override
                    public String getMethodsInterceptorV2() {
                        return "com.wosai.middleware.hera.plugin.spring.data.redis.v1.CacheDeleteInterceptor";
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                // public ValueWrapper putIfAbsent(RedisCacheElement element) {...}
                new InstanceMethodsInterceptV2Point() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("putIfAbsent").and(takesArgumentWithType(0, "org.springframework.data.redis.cache.RedisCacheElement"));
                    }

                    @Override
                    public String getMethodsInterceptorV2() {
                        return "com.wosai.middleware.hera.plugin.spring.data.redis.v1.CachePutIfAbsentInterceptor";
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                // public void put(RedisCacheElement element) {...}
                new InstanceMethodsInterceptV2Point() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("put").and(takesArgumentWithType(0, "org.springframework.data.redis.cache.RedisCacheElement"));
                    }

                    @Override
                    public String getMethodsInterceptorV2() {
                        return "com.wosai.middleware.hera.plugin.spring.data.redis.v1.CachePutInterceptor";
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
        };
    }
}
