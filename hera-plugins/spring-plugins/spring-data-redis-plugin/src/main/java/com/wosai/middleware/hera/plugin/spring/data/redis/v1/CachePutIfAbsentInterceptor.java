package com.wosai.middleware.hera.plugin.spring.data.redis.v1;

import com.wosai.middleware.hera.plugin.spring.data.redis.RedisCacheStatistics;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.InstanceMethodsAroundInterceptorV2;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;

import java.lang.reflect.Method;

/**
 * CachePutIfAbsentInterceptor only increases the puts counter when the key does not exist.
 */
public class CachePutIfAbsentInterceptor implements InstanceMethodsAroundInterceptorV2 {
    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, MethodInvocationContext methodInvocationContext) throws Throwable {

    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] arguments, Class<?>[] classes, Object ret, MethodInvocationContext methodInvocationContext) throws Throwable {
        if (arguments[0] != null) {
            if (ret == null) {
                // which means an old value exists
                // increase puts if "set" operation succeeded
                RedisCacheStatistics.incPuts((String) enhancedInstance.getSkyWalkingDynamicField());
            }
        }

        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable, MethodInvocationContext methodInvocationContext) {

    }
}
