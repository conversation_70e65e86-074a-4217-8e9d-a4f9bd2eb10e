package com.wosai.middleware.hera.plugin.spring.data.redis.v2;

import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.InstanceMethodsAroundInterceptorV2;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;

import java.lang.reflect.Method;

public abstract class AbstractExceptionAwareInterceptor implements InstanceMethodsAroundInterceptorV2 {
    abstract void record(String cacheName, Object returnObj);

    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, MethodInvocationContext methodInvocationContext) throws Throwable {
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable t, MethodInvocationContext methodInvocationContext) {
        if (t instanceof IllegalArgumentException) {
            // set flag to skip recording
            methodInvocationContext.setContext(Boolean.TRUE);
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] arguments, Class<?>[] classes, Object ret, MethodInvocationContext methodInvocationContext) throws Throwable {
        if (!Boolean.TRUE.equals(methodInvocationContext.getContext())) {
            record((String) arguments[0], ret);
        }
        return ret;
    }
}
