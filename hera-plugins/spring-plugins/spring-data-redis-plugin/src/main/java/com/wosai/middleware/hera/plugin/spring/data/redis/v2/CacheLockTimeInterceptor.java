package com.wosai.middleware.hera.plugin.spring.data.redis.v2;

import com.wosai.middleware.hera.plugin.spring.data.redis.RedisCacheStatistics;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.InstanceMethodsAroundInterceptorV2;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;

import java.lang.reflect.Method;
import java.time.Duration;

public class CacheLockTimeInterceptor implements InstanceMethodsAroundInterceptorV2 {
    boolean isLockingCacheWriter(EnhancedInstance enhancedInstance) {
        Object df = enhancedInstance.getSkyWalkingDynamicField();

        if (df == null) {
            return false;
        }

        final Duration sleepTime = (Duration) df;
        return !sleepTime.isZero() && !sleepTime.isNegative();
    }

    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, MethodInvocationContext methodInvocationContext) throws Throwable {
        if (isLockingCacheWriter(enhancedInstance)) {
            methodInvocationContext.setContext(System.nanoTime());
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] arguments, Class<?>[] classes, Object ret, MethodInvocationContext methodInvocationContext) throws Throwable {
        if (isLockingCacheWriter(enhancedInstance)) {
            long lockWaitTimeNs = (long) methodInvocationContext.getContext();
            RedisCacheStatistics.incLockTime((String) arguments[0], System.nanoTime() - lockWaitTimeNs);
        }

        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable, MethodInvocationContext methodInvocationContext) {

    }
}
