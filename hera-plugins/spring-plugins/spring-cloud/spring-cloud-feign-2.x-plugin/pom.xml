<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hera-spring-cloud</artifactId>
        <groupId>com.wosai.middleware</groupId>
        <version>1.14.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hera-spring-cloud-feign-2.x-plugin</artifactId>
    <packaging>jar</packaging>

    <properties>
        <spring-core.version>4.3.10.RELEASE</spring-core.version>
        <spring-webmvc.version>4.3.8.RELEASE</spring-webmvc.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sdk.plugin.related.dir>/../../..</sdk.plugin.related.dir>
    </properties>

</project>