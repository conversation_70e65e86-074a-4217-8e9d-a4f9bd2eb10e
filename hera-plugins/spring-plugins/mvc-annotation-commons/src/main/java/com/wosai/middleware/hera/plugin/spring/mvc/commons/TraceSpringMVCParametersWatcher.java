package com.wosai.middleware.hera.plugin.spring.mvc.commons;

import org.apache.skywalking.apm.agent.core.conf.dynamic.AgentConfigChangeWatcher;
import org.apache.skywalking.apm.plugin.spring.mvc.commons.SpringMVCPluginConfig;

public class TraceSpringMVCParametersWatcher extends AgentConfigChangeWatcher {

    private final boolean defaultValue;

    public TraceSpringMVCParametersWatcher(String propertyKey) {
        super(propertyKey);
        defaultValue = SpringMVCPluginConfig.Plugin.SpringMVC.COLLECT_HTTP_PARAMS;
    }

    @Override
    public void notify(ConfigChangeEvent value) {
        if (EventType.DELETE.equals(value.getEventType())) {
            SpringMVCPluginConfig.Plugin.SpringMVC.COLLECT_HTTP_PARAMS = defaultValue;
        } else {
            SpringMVCPluginConfig.Plugin.SpringMVC.COLLECT_HTTP_PARAMS = Boolean.parseBoolean(value.getNewValue());
        }
    }

    @Override
    public String value() {
        return Boolean.toString(SpringMVCPluginConfig.Plugin.SpringMVC.COLLECT_HTTP_PARAMS);
    }
}
