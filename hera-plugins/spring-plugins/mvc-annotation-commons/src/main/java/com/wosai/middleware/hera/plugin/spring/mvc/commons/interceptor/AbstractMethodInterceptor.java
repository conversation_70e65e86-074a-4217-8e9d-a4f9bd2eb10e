/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.wosai.middleware.hera.plugin.spring.mvc.commons.interceptor;

import com.wosai.middleware.hera.agent.conf.dynamic.MetaConfigurationDiscoveryService;
import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.plugin.spring.mvc.commons.RequestUtil;
import com.wosai.middleware.hera.plugin.spring.mvc.commons.SkywalkingRequestExtractAdapter;
import com.wosai.middleware.hera.plugin.spring.mvc.commons.TraceSpringMVCParametersWatcher;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import com.wosai.middleware.hera.tracing.tag.Tags;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;
import org.apache.skywalking.apm.agent.core.context.RuntimeContext;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.agent.core.util.CollectionUtil;
import org.apache.skywalking.apm.agent.core.util.MethodUtil;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.spring.mvc.commons.EnhanceRequireObjectCache;
import org.apache.skywalking.apm.plugin.spring.mvc.commons.SpringMVCPluginConfig;
import org.apache.skywalking.apm.plugin.spring.mvc.commons.exception.IllegalMethodStackDepthException;
import org.apache.skywalking.apm.plugin.spring.mvc.commons.exception.ServletResponseNotFoundException;
import org.apache.skywalking.apm.plugin.spring.mvc.commons.interceptor.StackDepth;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.net.URI;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.apache.skywalking.apm.plugin.spring.mvc.commons.Constants.CONTROLLER_METHOD_STACK_DEPTH;
import static org.apache.skywalking.apm.plugin.spring.mvc.commons.Constants.FORWARD_REQUEST_FLAG;
import static org.apache.skywalking.apm.plugin.spring.mvc.commons.Constants.REACTIVE_ASYNC_SPAN_IN_RUNTIME_CONTEXT;
import static org.apache.skywalking.apm.plugin.spring.mvc.commons.Constants.REQUEST_KEY_IN_RUNTIME_CONTEXT;
import static org.apache.skywalking.apm.plugin.spring.mvc.commons.Constants.RESPONSE_KEY_IN_RUNTIME_CONTEXT;

/**
 * the abstract method interceptor
 */
@Slf4j
public abstract class AbstractMethodInterceptor implements InstanceMethodsAroundInterceptor {
    private static boolean IS_SERVLET_GET_STATUS_METHOD_EXIST;
    private static boolean IS_JAKARTA_SERVLET_GET_STATUS_METHOD_EXIST;
    private static final String SERVLET_RESPONSE_CLASS = "javax.servlet.http.HttpServletResponse";
    private static final String JAKARTA_SERVLET_RESPONSE_CLASS = "jakarta.servlet.http.HttpServletResponse";
    private static final String GET_STATUS_METHOD = "getStatus";

    private static boolean IN_SERVLET_CONTAINER;
    private static boolean IS_JAVAX = false;
    private static boolean IS_JAKARTA = false;

    static {
        try {
            TraceSpringMVCParametersWatcher traceSpringMVCParametersWatcher =
                    new TraceSpringMVCParametersWatcher("plugin.springmvc.trace_http_parameters");
            MetaConfigurationDiscoveryService configurationDiscoveryService = ServiceManager.INSTANCE.findService(
                    MetaConfigurationDiscoveryService.class);
            configurationDiscoveryService.registerAgentConfigChangeWatcher(traceSpringMVCParametersWatcher);
        } catch (Throwable t) {
            log.error("fail to register spring mvc watcher", t);
        }

        IS_SERVLET_GET_STATUS_METHOD_EXIST = MethodUtil.isMethodExist(AbstractMethodInterceptor.class.getClassLoader(),
                SERVLET_RESPONSE_CLASS, GET_STATUS_METHOD);
        IS_JAKARTA_SERVLET_GET_STATUS_METHOD_EXIST = MethodUtil.isMethodExist(AbstractMethodInterceptor.class.getClassLoader(),
                JAKARTA_SERVLET_RESPONSE_CLASS, GET_STATUS_METHOD);
        try {
            Class.forName(SERVLET_RESPONSE_CLASS, true, AbstractMethodInterceptor.class.getClassLoader());
            IN_SERVLET_CONTAINER = true;
            IS_JAVAX = true;
        } catch (Exception ignore) {
            try {
                Class.forName(
                        JAKARTA_SERVLET_RESPONSE_CLASS, true, AbstractMethodInterceptor.class.getClassLoader());
                IN_SERVLET_CONTAINER = true;
                IS_JAKARTA = true;
            } catch (Exception ignore2) {
                IN_SERVLET_CONTAINER = false;
            }
        }
    }

    public abstract String getRequestURL(Method method);

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Throwable {

        Boolean forwardRequestFlag = (Boolean) ContextManager.getRuntimeContext().get(FORWARD_REQUEST_FLAG);
        /**
         * Spring MVC plugin do nothing if current request is forward request.
         * Ref: https://github.com/apache/skywalking/pull/1325
         */
        if (forwardRequestFlag != null && forwardRequestFlag) {
            return;
        }

        Object request = ContextManager.getRuntimeContext().get(REQUEST_KEY_IN_RUNTIME_CONTEXT);

        if (request != null) {
            StackDepth stackDepth = (StackDepth) ContextManager.getRuntimeContext().get(CONTROLLER_METHOD_STACK_DEPTH);
            if (stackDepth == null) {
                // Servlet + HttpServletRequest.class
                if (IN_SERVLET_CONTAINER && IS_JAVAX && HttpServletRequest.class.isAssignableFrom(request.getClass())) {
                    final HttpServletRequest httpServletRequest = (HttpServletRequest) request;

                    String operationName = this.buildOperationName(method, httpServletRequest.getMethod(),
                            (EnhanceRequireObjectCache) objInst.getSkyWalkingDynamicField());

                    HeraSpanContext parentSpan = ContextManager.extract(new SkywalkingRequestExtractAdapter(headersToMultiMap(httpServletRequest)));
                    AbstractHeraSpan span = ContextManager.createEntrySpan(operationName, parentSpan);
                    Tags.HTTP_URL.set(span, httpServletRequest.getRequestURL().toString());
                    Tags.HTTP_METHOD.set(span, httpServletRequest.getMethod());
                    Tags.COMPONENT.set(span, ComponentsDefine.SPRING_MVC_ANNOTATION.getName());

                    if (SpringMVCPluginConfig.Plugin.SpringMVC.COLLECT_HTTP_PARAMS) {
                        RequestUtil.collectHttpParam(httpServletRequest, span);
                    }

                    if (!CollectionUtil.isEmpty(SpringMVCPluginConfig.Plugin.Http.INCLUDE_HTTP_HEADERS)) {
                        RequestUtil.collectHttpHeaders(httpServletRequest, span);
                    }
                } else if (IN_SERVLET_CONTAINER && IS_JAKARTA && jakarta.servlet.http.HttpServletRequest.class.isAssignableFrom(request.getClass())) {
                    final jakarta.servlet.http.HttpServletRequest httpServletRequest = (jakarta.servlet.http.HttpServletRequest) request;

                    String operationName =
                            this.buildOperationName(method, httpServletRequest.getMethod(),
                                    (EnhanceRequireObjectCache) objInst.getSkyWalkingDynamicField());

                    HeraSpanContext parentSpan = ContextManager.extract(new SkywalkingRequestExtractAdapter(headersToMultiMap(httpServletRequest)));
                    AbstractHeraSpan span =
                            ContextManager.createEntrySpan(operationName, parentSpan);
                    Tags.HTTP_URL.set(span, httpServletRequest.getRequestURL().toString());
                    Tags.HTTP_METHOD.set(span, httpServletRequest.getMethod());
                    Tags.COMPONENT.set(span, ComponentsDefine.SPRING_MVC_ANNOTATION.getName());

                    if (SpringMVCPluginConfig.Plugin.SpringMVC.COLLECT_HTTP_PARAMS) {
                        RequestUtil.collectHttpParam(httpServletRequest, span);
                    }

                    if (!CollectionUtil
                            .isEmpty(SpringMVCPluginConfig.Plugin.Http.INCLUDE_HTTP_HEADERS)) {
                        RequestUtil.collectHttpHeaders(httpServletRequest, span);
                    }
                } else if (ServerHttpRequest.class.isAssignableFrom(request.getClass())) {
                    final ServerHttpRequest serverHttpRequest = (ServerHttpRequest) request;

                    HeraSpanContext parentSpan = ContextManager.extract(new SkywalkingRequestExtractAdapter(headersToMultiMap(serverHttpRequest)));

                    String operationName = this.buildOperationName(method, serverHttpRequest.getMethod().name(),
                            (EnhanceRequireObjectCache) objInst.getSkyWalkingDynamicField());

                    AbstractHeraSpan span = ContextManager.createEntrySpan(operationName, parentSpan);
                    Tags.HTTP_URL.set(span, formatURI(serverHttpRequest.getURI()));
                    Tags.HTTP_METHOD.set(span, serverHttpRequest.getMethod().name());
                    Tags.COMPONENT.set(span, ComponentsDefine.SPRING_MVC_ANNOTATION.getName());

                    if (SpringMVCPluginConfig.Plugin.SpringMVC.COLLECT_HTTP_PARAMS) {
                        RequestUtil.collectHttpParam(serverHttpRequest, span);
                    }

                    if (!CollectionUtil.isEmpty(SpringMVCPluginConfig.Plugin.Http.INCLUDE_HTTP_HEADERS)) {
                        RequestUtil.collectHttpHeaders(serverHttpRequest, span);
                    }
                } else {
                    throw new IllegalStateException("this line should not be reached");
                }

                stackDepth = new StackDepth();
                ContextManager.getRuntimeContext().put(CONTROLLER_METHOD_STACK_DEPTH, stackDepth);
            } else {
                AbstractHeraSpan span = ContextManager.createLocalSpan(buildOperationName(objInst, method));
                Tags.COMPONENT.set(span, ComponentsDefine.SPRING_MVC_ANNOTATION.getName());
            }

            stackDepth.increment();
        }
    }

    /**
     * formatURI removes the query and fragment from URI
     *
     * @return truncated URI
     */
    static String formatURI(URI uri) {
        String uriStr = uri.toString();
        int queryIdx = uriStr.indexOf('?');
        int fragmentIdx = uriStr.indexOf('#', queryIdx);
        if (queryIdx != -1) {
            return uriStr.substring(0, queryIdx);
        } else if (fragmentIdx != -1) {
            return uriStr.substring(0, fragmentIdx);
        } else {
            return uriStr;
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) throws Throwable {
        final RuntimeContext runtimeContext = ContextManager.getRuntimeContext();

        Boolean forwardRequestFlag = (Boolean) runtimeContext.get(FORWARD_REQUEST_FLAG);
        /**
         * Spring MVC plugin do nothing if current request is forward request.
         * Ref: https://github.com/apache/skywalking/pull/1325
         */
        if (forwardRequestFlag != null && forwardRequestFlag) {
            return ret;
        }

        Object request = runtimeContext.get(REQUEST_KEY_IN_RUNTIME_CONTEXT);

        if (request != null) {
            try {
                StackDepth stackDepth = (StackDepth) runtimeContext.get(CONTROLLER_METHOD_STACK_DEPTH);
                if (stackDepth == null) {
                    throw new IllegalMethodStackDepthException();
                } else {
                    stackDepth.decrement();
                }

                AbstractHeraSpan span = ContextManager.activeSpan();

                if (stackDepth.depth() == 0) {
                    Object response = runtimeContext.get(RESPONSE_KEY_IN_RUNTIME_CONTEXT);
                    if (response == null) {
                        throw new ServletResponseNotFoundException();
                    }

                    Integer statusCode = null;

                    if (IS_SERVLET_GET_STATUS_METHOD_EXIST && HttpServletResponse.class.isAssignableFrom(response.getClass())) {
                        statusCode = ((HttpServletResponse) response).getStatus();
                    } else if (IS_JAKARTA_SERVLET_GET_STATUS_METHOD_EXIST && jakarta.servlet.http.HttpServletResponse.class.isAssignableFrom(response.getClass())) {
                        statusCode = ((jakarta.servlet.http.HttpServletResponse) response).getStatus();
                    } else if (ServerHttpResponse.class.isAssignableFrom(response.getClass())) {
                        if (IS_SERVLET_GET_STATUS_METHOD_EXIST || IS_JAKARTA_SERVLET_GET_STATUS_METHOD_EXIST) {
                            statusCode = ((ServerHttpResponse) response).getRawStatusCode();
                        }

                        Object context = runtimeContext.get(REACTIVE_ASYNC_SPAN_IN_RUNTIME_CONTEXT);
                        if (context != null) {
                            ((AbstractHeraSpan[]) context)[0] = span.prepareForAsync();
                        }
                    }

                    if (ret != null && "org.springframework.web.context.request.async.DeferredResult".equals(ret.getClass().getName())) {
                        span.prepareForAsync();
                        ((EnhancedInstance) ret).setSkyWalkingDynamicField(span);
                    }

                    if (statusCode != null) {
                        Tags.HTTP_STATUS.set(span, statusCode);
                        if (statusCode >= 400) {
                            ContextManager.markError(span);
                        }
                    }

                    runtimeContext.remove(REACTIVE_ASYNC_SPAN_IN_RUNTIME_CONTEXT);
                    runtimeContext.remove(REQUEST_KEY_IN_RUNTIME_CONTEXT);
                    runtimeContext.remove(RESPONSE_KEY_IN_RUNTIME_CONTEXT);
                    runtimeContext.remove(CONTROLLER_METHOD_STACK_DEPTH);
                }

                // TODO: support profiling
            } finally {
                ContextManager.stopSpan();
            }
        }

        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {
        ContextManager.logError(t);
    }

    static Map<String, List<String>> headersToMultiMap(javax.servlet.http.HttpServletRequest request) {
        Map<String, List<String>> headersResult = new HashMap<>();

        Enumeration<String> headerNamesIt = request.getHeaderNames();
        while (headerNamesIt.hasMoreElements()) {
            String headerName = headerNamesIt.nextElement();

            Enumeration<String> valuesIt = request.getHeaders(headerName);
            List<String> valuesList = new ArrayList<>(1);
            while (valuesIt.hasMoreElements()) {
                valuesList.add(valuesIt.nextElement());
            }

            headersResult.put(headerName, valuesList);
        }

        return headersResult;
    }

    static Map<String, List<String>> headersToMultiMap(jakarta.servlet.http.HttpServletRequest request) {
        Map<String, List<String>> headersResult = new HashMap<>();

        Enumeration<String> headerNamesIt = request.getHeaderNames();
        while (headerNamesIt.hasMoreElements()) {
            String headerName = headerNamesIt.nextElement();

            Enumeration<String> valuesIt = request.getHeaders(headerName);
            List<String> valuesList = new ArrayList<>(1);
            while (valuesIt.hasMoreElements()) {
                valuesList.add(valuesIt.nextElement());
            }

            headersResult.put(headerName, valuesList);
        }

        return headersResult;
    }

    static Map<String, List<String>> headersToMultiMap(org.springframework.http.server.reactive.ServerHttpRequest request) {
        Map<String, List<String>> headersResult = new HashMap<>();

        Enumeration<String> headerNamesIt = Collections.enumeration(request.getHeaders().keySet());
        while (headerNamesIt.hasMoreElements()) {
            String headerName = headerNamesIt.nextElement();

            Enumeration<String> valuesIt = Collections.enumeration(request.getHeaders().get(headerName));
            List<String> valuesList = new ArrayList<>(1);
            while (valuesIt.hasMoreElements()) {
                valuesList.add(valuesIt.nextElement());
            }

            headersResult.put(headerName, valuesList);
        }

        return headersResult;
    }

    private String buildOperationName(Object invoker, Method method) {
        StringBuilder operationName = new StringBuilder(invoker.getClass().getName()).append(".")
                .append(method.getName())
                .append("(");
        for (Class<?> type : method.getParameterTypes()) {
            operationName.append(type.getName()).append(",");
        }

        if (method.getParameterTypes().length > 0) {
            operationName = operationName.deleteCharAt(operationName.length() - 1);
        }

        return operationName.append(")").toString();
    }

    private String buildOperationName(Method method, String httpMethod, EnhanceRequireObjectCache pathMappingCache) {
        String operationName;
        if (SpringMVCPluginConfig.Plugin.SpringMVC.USE_QUALIFIED_NAME_AS_ENDPOINT_NAME) {
            operationName = MethodUtil.generateOperationName(method);
        } else {
            String requestURL = pathMappingCache.findPathMapping(method);
            if (requestURL == null) {
                requestURL = getRequestURL(method);
                pathMappingCache.addPathMapping(method, requestURL);
                requestURL = pathMappingCache.findPathMapping(method);
            }
            operationName = String.join(":", httpMethod, requestURL);
        }

        return operationName;
    }
}
