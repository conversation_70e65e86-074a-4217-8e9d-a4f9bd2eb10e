package com.wosai.middleware.hera.plugin.spring.mvc.commons;

import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.util.ExtraTags;
import org.apache.skywalking.apm.agent.core.util.CollectionUtil;
import org.apache.skywalking.apm.plugin.spring.mvc.commons.SpringMVCPluginConfig;
import org.apache.skywalking.apm.util.StringUtil;
import org.springframework.http.server.reactive.ServerHttpRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RequestUtil {
    public static void collectHttpParam(HttpServletRequest request, AbstractHeraSpan span) {
        final Map<String, String[]> parameterMap = request.getParameterMap();
        if (parameterMap != null && !parameterMap.isEmpty()) {
            String tagValue = CollectionUtil.toString(parameterMap);
            tagValue = SpringMVCPluginConfig.Plugin.Http.HTTP_PARAMS_LENGTH_THRESHOLD > 0 ?
                    StringUtil.cut(tagValue, SpringMVCPluginConfig.Plugin.Http.HTTP_PARAMS_LENGTH_THRESHOLD) : tagValue;
            span.setTag(ExtraTags.HTTP_PARAMS, tagValue);
        }
    }

    public static void collectHttpParam(jakarta.servlet.http.HttpServletRequest request, AbstractHeraSpan span) {
        final Map<String, String[]> parameterMap = request.getParameterMap();
        if (parameterMap != null && !parameterMap.isEmpty()) {
            String tagValue = CollectionUtil.toString(parameterMap);
            tagValue = SpringMVCPluginConfig.Plugin.Http.HTTP_PARAMS_LENGTH_THRESHOLD > 0 ?
                    StringUtil.cut(tagValue, SpringMVCPluginConfig.Plugin.Http.HTTP_PARAMS_LENGTH_THRESHOLD) : tagValue;
            span.setTag(ExtraTags.HTTP_PARAMS, tagValue);
        }
    }

    public static void collectHttpParam(ServerHttpRequest request, AbstractHeraSpan span) {
        Map<String, String[]> parameterMap = new HashMap<>(request.getQueryParams().size());
        request.getQueryParams().forEach((key, value) -> {
            parameterMap.put(key, value.toArray(new String[0]));
        });
        if (!parameterMap.isEmpty()) {
            String tagValue = CollectionUtil.toString(parameterMap);
            tagValue = SpringMVCPluginConfig.Plugin.Http.HTTP_PARAMS_LENGTH_THRESHOLD > 0 ?
                    StringUtil.cut(tagValue, SpringMVCPluginConfig.Plugin.Http.HTTP_PARAMS_LENGTH_THRESHOLD) : tagValue;
            span.setTag(ExtraTags.HTTP_PARAMS, tagValue);
        }
    }

    public static void collectHttpHeaders(HttpServletRequest request, AbstractHeraSpan span) {
        final List<String> headersList = new ArrayList<>(SpringMVCPluginConfig.Plugin.Http.INCLUDE_HTTP_HEADERS.size());
        SpringMVCPluginConfig.Plugin.Http.INCLUDE_HTTP_HEADERS.stream()
                .filter(
                        headerName -> request.getHeaders(headerName) != null)
                .forEach(headerName -> {
                    Enumeration<String> headerValues = request.getHeaders(
                            headerName);
                    List<String> valueList = Collections.list(
                            headerValues);
                    if (!CollectionUtil.isEmpty(valueList)) {
                        String headerValue = valueList.toString();
                        headersList.add(headerName + "=" + headerValue);
                    }
                });

        collectHttpHeaders(headersList, span);
    }

    public static void collectHttpHeaders(jakarta.servlet.http.HttpServletRequest request, AbstractHeraSpan span) {
        final List<String> headersList = new ArrayList<>(SpringMVCPluginConfig.Plugin.Http.INCLUDE_HTTP_HEADERS.size());
        SpringMVCPluginConfig.Plugin.Http.INCLUDE_HTTP_HEADERS.stream()
                .filter(
                        headerName -> request.getHeaders(headerName) != null)
                .forEach(headerName -> {
                    Enumeration<String> headerValues = request.getHeaders(
                            headerName);
                    List<String> valueList = Collections.list(
                            headerValues);
                    if (!CollectionUtil.isEmpty(valueList)) {
                        String headerValue = valueList.toString();
                        headersList.add(headerName + "=" + headerValue);
                    }
                });

        collectHttpHeaders(headersList, span);
    }

    public static void collectHttpHeaders(ServerHttpRequest request, AbstractHeraSpan span) {
        final List<String> headersList = new ArrayList<>(SpringMVCPluginConfig.Plugin.Http.INCLUDE_HTTP_HEADERS.size());
        SpringMVCPluginConfig.Plugin.Http.INCLUDE_HTTP_HEADERS.stream()
                .filter(headerName -> getHeaders(request, headerName).hasMoreElements())
                .forEach(headerName -> {
                    Enumeration<String> headerValues = getHeaders(request, headerName);
                    List<String> valueList = Collections.list(
                            headerValues);
                    if (!CollectionUtil.isEmpty(valueList)) {
                        String headerValue = valueList.toString();
                        headersList.add(headerName + "=" + headerValue);
                    }
                });

        collectHttpHeaders(headersList, span);
    }

    private static void collectHttpHeaders(final List<String> headersList, final AbstractHeraSpan span) {
        if (headersList != null && !headersList.isEmpty()) {
            String tagValue = String.join("\n", headersList);
            tagValue = SpringMVCPluginConfig.Plugin.Http.HTTP_HEADERS_LENGTH_THRESHOLD > 0 ?
                    StringUtil.cut(tagValue, SpringMVCPluginConfig.Plugin.Http.HTTP_HEADERS_LENGTH_THRESHOLD) : tagValue;
            span.setTag(ExtraTags.HTTP_HEADER, tagValue);
        }
    }

    private static Enumeration<String> getHeaders(final ServerHttpRequest request, final String headerName) {
        List<String> values = request.getHeaders().get(headerName);
        if (values == null) {
            return Collections.enumeration(Collections.emptyList());
        }
        return Collections.enumeration(values);
    }
}
