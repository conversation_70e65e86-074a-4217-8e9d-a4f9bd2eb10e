package com.wosai.middleware.hera.plugin.jedis.v3;

import org.apache.skywalking.apm.agent.core.conf.dynamic.AgentConfigChangeWatcher;
import org.apache.skywalking.apm.plugin.jedis.v3.JedisPluginConfig;

public class TraceJedis3ParametersWatcher extends AgentConfigChangeWatcher {

    private final boolean defaultValue;

    public TraceJedis3ParametersWatcher(String propertyKey) {
        super(propertyKey);
        defaultValue = JedisPluginConfig.Plugin.Jedis.TRACE_REDIS_PARAMETERS;
    }

    @Override
    public void notify(ConfigChangeEvent value) {
        if (EventType.DELETE.equals(value.getEventType())) {
            JedisPluginConfig.Plugin.Jedis.TRACE_REDIS_PARAMETERS = defaultValue;
        } else {
            JedisPluginConfig.Plugin.Jedis.TRACE_REDIS_PARAMETERS = Boolean.parseBoolean(value.getNewValue());
        }
    }

    @Override
    public String value() {
        return Boolean.toString(JedisPluginConfig.Plugin.Jedis.TRACE_REDIS_PARAMETERS);
    }
}
