package com.wosai.middleware.hera.plugin.jedis.v3.define;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.InstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;
import org.apache.skywalking.apm.plugin.jedis.v3.define.AbstractWitnessInstrumentation;

import static net.bytebuddy.matcher.ElementMatchers.named;
import static org.apache.skywalking.apm.agent.core.plugin.bytebuddy.ArgumentTypeNameMatch.takesArgumentWithType;
import static org.apache.skywalking.apm.agent.core.plugin.match.NameMatch.byName;

public class TransactionConstructorInstrumentation extends AbstractWitnessInstrumentation {

    private static final String ENHANCE_CLASS = "redis.clients.jedis.Transaction";
    private static final String TRANSACTION_CONSTRUCTION_INTERCEPT_CLASS = "org.apache.skywalking.apm.plugin.jedis.v3.TransactionConstructorInterceptor";
    private static final String JEDIS_METHOD_INTERCEPT_CLASS = "com.wosai.middleware.hera.plugin.jedis.v3.JedisMethodInterceptor";
    private static final String ARGUMENT_TYPE_NAME = "redis.clients.jedis.Client";

    @Override
    protected ClassMatch enhanceClass() {
        return byName(ENHANCE_CLASS);
    }

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[]{
                new ConstructorInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getConstructorMatcher() {
                        return takesArgumentWithType(0, ARGUMENT_TYPE_NAME);
                    }

                    @Override
                    public String getConstructorInterceptor() {
                        return TRANSACTION_CONSTRUCTION_INTERCEPT_CLASS;
                    }
                }
        };
    }

    @Override
    public InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints() {
        return new InstanceMethodsInterceptPoint[]{
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("exec").or(named("execGetResponse")).or(named("discard"));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return JEDIS_METHOD_INTERCEPT_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }
}
