package com.wosai.middleware.hera.plugin.jedis.v4.define;

import org.apache.skywalking.apm.agent.core.plugin.WitnessMethod;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.ClassInstanceMethodsEnhancePluginDefine;

import java.util.Collections;
import java.util.List;

import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.takesArguments;

public abstract class AbstractWitnessInstrumentation extends ClassInstanceMethodsEnhancePluginDefine {

    @Override
    protected String[] witnessClasses() {
        return new String[0];
    }

    @Override
    protected List<WitnessMethod> witnessMethods() {
        return Collections.singletonList(new WitnessMethod(
                "redis.clients.jedis.Pipeline",
                named("persist").and(takesArguments(1))));
    }
}