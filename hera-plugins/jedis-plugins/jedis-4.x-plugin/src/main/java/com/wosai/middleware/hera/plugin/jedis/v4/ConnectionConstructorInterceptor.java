package com.wosai.middleware.hera.plugin.jedis.v4;

import com.wosai.middleware.hera.agent.conf.dynamic.MetaConfigurationDiscoveryService;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;
import org.apache.skywalking.apm.plugin.jedis.v4.ConnectionInformation;
import redis.clients.jedis.DefaultJedisSocketFactory;
import redis.clients.jedis.HostAndPort;

public class ConnectionConstructorInterceptor implements InstanceConstructorInterceptor {
    @Override
    public void onConstruct(EnhancedInstance objInst, Object[] allArguments) throws Throwable {
        HostAndPort hostAndPort = ((DefaultJedisSocketFactory) allArguments[0]).getHostAndPort();
        ConnectionInformation connectionData = new ConnectionInformation();
        connectionData.setActualTarget(hostAndPort.toString());
        objInst.setSkyWalkingDynamicField(connectionData);
        TraceJedis4ParametersWatcher traceJedis4ParametersWatcher = new TraceJedis4ParametersWatcher("plugin.redis.trace_parameters");
        MetaConfigurationDiscoveryService configurationDiscoveryService = ServiceManager.INSTANCE.findService(
                MetaConfigurationDiscoveryService.class);
        configurationDiscoveryService.registerAgentConfigChangeWatcher(traceJedis4ParametersWatcher);
    }
}