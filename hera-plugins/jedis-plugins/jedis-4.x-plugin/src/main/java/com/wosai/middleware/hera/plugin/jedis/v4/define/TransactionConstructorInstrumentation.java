package com.wosai.middleware.hera.plugin.jedis.v4.define;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import net.bytebuddy.matcher.ElementMatchers;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.InstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;

import static net.bytebuddy.matcher.ElementMatchers.named;
import static org.apache.skywalking.apm.agent.core.plugin.match.NameMatch.byName;

public class TransactionConstructorInstrumentation extends AbstractWitnessInstrumentation {

    private static final String ENHANCE_CLASS = "redis.clients.jedis.Transaction";
    private static final String JEDIS_CONSTRUCTION_INTERCEPT_CLASS = "org.apache.skywalking.apm.plugin.jedis.v4.JedisConstructorInterceptor";
    private static final String JEDIS_METHOD_INTERCEPT_CLASS = "com.wosai.middleware.hera.plugin.jedis.v4.JedisMethodInterceptor";

    @Override
    protected ClassMatch enhanceClass() {
        return byName(ENHANCE_CLASS);
    }

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[]{
                new ConstructorInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getConstructorMatcher() {
                        return ElementMatchers.takesArgument(0, named("redis.clients.jedis.Jedis"))
                                .or(ElementMatchers.takesArgument(0, named("redis.clients.jedis.Connection")));
                    }

                    @Override
                    public String getConstructorInterceptor() {
                        return JEDIS_CONSTRUCTION_INTERCEPT_CLASS;
                    }
                }
        };
    }

    @Override
    public InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints() {
        return new InstanceMethodsInterceptPoint[]{
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("exec").or(named("discard"));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return JEDIS_METHOD_INTERCEPT_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }
}
