/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.wosai.middleware.hera.plugin.httpasyncclient.v4;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import org.apache.http.HttpHost;
import org.apache.http.RequestLine;
import org.apache.http.client.methods.HttpRequestWrapper;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.nio.NHttpClientConnection;
import org.apache.http.protocol.HttpContext;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.httpasyncclient.v4.Constants;

import java.lang.reflect.Method;
import java.net.URL;
import java.util.Objects;

/**
 * the actual point request begin fetch the request from thread local .
 */
public class HttpAsyncRequestExecutorInterceptor implements InstanceMethodsAroundInterceptor {

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Throwable {
        HttpContext context = findHttpContext(allArguments);
        if (Objects.isNull(context)) {
            return;
        }

        HttpRequestWrapper requestWrapper = (HttpRequestWrapper) context.getAttribute(HttpClientContext.HTTP_REQUEST);
        HttpHost httpHost = (HttpHost) context.getAttribute(HttpClientContext.HTTP_TARGET_HOST);

        RequestLine requestLine = requestWrapper.getRequestLine();
        String uri = requestLine.getUri();
        String operationName = uri.startsWith("http") ? new URL(uri).getPath() : uri;
        int port = httpHost.getPort();
        AbstractHeraSpan span = ContextManager.createExitSpan(operationName, httpHost.getHostName() + ":" + (port == -1 ? 80 : port));

        Tags.COMPONENT.set(span, ComponentsDefine.HTTP_ASYNC_CLIENT.getName());
        Tags.HTTP_URL.set(span, requestWrapper.getOriginal().getRequestLine().getUri());
        Tags.HTTP_METHOD.set(span, requestLine.getMethod());

        ContextManager.inject(span, requestWrapper::setHeader);
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) throws Throwable {
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {

    }

    private HttpContext findHttpContext(Object[] allArguments) {
        HttpContext context = Constants.HTTP_CONTEXT_LOCAL.get();
        Constants.HTTP_CONTEXT_LOCAL.remove();
        if (Objects.nonNull(context)) {
            return context;
        }
        NHttpClientConnection conn = (NHttpClientConnection) allArguments[0];
        HttpContext contextInConn = conn.getContext();
        if (Objects.isNull(contextInConn)) {
            return null;
        }
        context = (HttpContext) contextInConn.getAttribute(Constants.SKYWALKING_HTTP_CONTEXT);
        conn.getContext().removeAttribute(Constants.SKYWALKING_HTTP_CONTEXT);
        if (Objects.isNull(context)) {
            return null;
        }

        AbstractHeraSpan parentSpan = (AbstractHeraSpan) contextInConn.getAttribute(Constants.SKYWALKING_CONTEXT_SNAPSHOT);
        conn.getContext().removeAttribute(Constants.SKYWALKING_CONTEXT_SNAPSHOT);

        if (parentSpan != null) {
            AbstractHeraSpan localSpan = ContextManager.createLocalSpan("HttpAsyncClient/local", parentSpan.context());
            Tags.COMPONENT.set(localSpan, ComponentsDefine.HTTP_ASYNC_CLIENT.getName());
        }
        return context;
    }
}
