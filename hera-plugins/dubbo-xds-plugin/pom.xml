<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wosai.middleware</groupId>
        <artifactId>hera-plugins</artifactId>
        <version>1.14.0-SNAPSHOT</version>
    </parent>

    <artifactId>dubbo-xds-plugin</artifactId>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sdk.plugin.related.dir>/..</sdk.plugin.related.dir>
        <dubbo.version>3.3.1</dubbo.version>
        <dubbo.xds.version>1.0.5-SNAPSHOT</dubbo.xds.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>dubbo-xds</artifactId>
            <version>${dubbo.xds.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>${dubbo.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>