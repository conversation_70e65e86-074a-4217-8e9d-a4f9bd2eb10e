package com.wosai.middleware.hera.plugin.dubboxds;

import com.wosai.middleware.dubbo.XdsDirectory;
import com.wosai.middleware.hera.agent.conf.HeraConfig;
import com.wosai.middleware.hera.agent.core.plugin.interceptor.enhance.v3.InstanceMethodsAroundInterceptorV3;
import com.wosai.middleware.hera.agent.services.MeshContextManager;
import com.wosai.middleware.hera.rpc.LoadBalancer;
import com.wosai.middleware.hera.rpc.proxyless.ManagedProxylessService;
import com.wosai.middleware.hera.rpc.proxyless.ManagedServiceBuilder;
import com.wosai.middleware.hera.util.CopyOnWriteMap;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Protocol;
import org.apache.dubbo.rpc.cluster.router.state.BitList;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.apache.skywalking.apm.agent.core.util.CollectionUtil;
import org.apache.skywalking.apm.util.StringUtil;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class XdsDirectoryInterceptor implements InstanceMethodsAroundInterceptorV3, InstanceConstructorInterceptor {

    private static final ILog LOGGER = LogManager.getLogger(XdsDirectoryInterceptor.class);

    private final Map<String, Invoker<?>> appInvokerCache = new CopyOnWriteMap<>();

    @Override
    public void onConstruct(EnhancedInstance enhancedInstance, Object[] objects) throws Throwable {
        // if cache is already set
        if (enhancedInstance.getSkyWalkingDynamicField() != null) {
            return;
        }
        // set clientDto and enhanceInstance first to avoid potential xDS failure.
        final XdsDubboPeerInfo clientDto = new XdsDubboPeerInfo();
        enhancedInstance.setSkyWalkingDynamicField(clientDto);

        if (MeshContextManager.xdsAvailable()) {
            XdsDirectory<?> xdsDirectory = (XdsDirectory<?>) enhancedInstance;
            final Class<?> serviceType = xdsDirectory.getServiceType();
            final URL consumerUrl = xdsDirectory.getConsumerUrl();
            final String protocolName = xdsDirectory.getProtocolName();
            final String[] applicationNames = xdsDirectory.getApplicationNames();
            final Protocol protocol = xdsDirectory.getProtocol();
            final int port = xdsDirectory.getPort();
            for (String applicationName : applicationNames) {
                final String serviceName = parseFQDNServiceName(applicationName);
                clientDto.getDnsInvoker().put(applicationName, appInvokerCache.computeIfAbsent(serviceName + ":" + port, key -> {
                    URL url = new URL(protocolName, applicationName + ".sqb.svc.cluster.local", port, serviceType.getName(), consumerUrl.getParameters());
                    return protocol.refer(serviceType, url);
                }));
                final String key = "xds:///" + serviceName + ":" + port;
                final ManagedProxylessService service = MeshContextManager.getOrRegistryManagedService(key,
                        () -> {
                            ManagedProxylessService instance = ManagedServiceBuilder.forAddress("xds:///" + serviceName, port).build();

                            instance.onReady(() -> {
                                List<LoadBalancer.Server> currentServers = instance.getServers();
                                if (currentServers != null && !currentServers.isEmpty()) {
                                    updateAndSetInvokers(clientDto, xdsDirectory, currentServers, applicationName);
                                }
                            });

                            return instance;
                        });
//                final ManagedProxylessService service = MeshContextManager.getOrRegistryManagedService(key,
//                        () -> {
//                            ManagedProxylessService instance = ManagedServiceBuilder.forAddress("xds:///" + serviceName, port).build();
//                            try {
//                                boolean isReady = instance.awaitReady(
//                                        LoadBalancer.PickServerArgs.create("/", null, new HashMap<>(), Collections.emptyMap()),
//                                        HeraConfig.Mesh.XDS.TIMEOUT_MILLIS, HeraConfig.Mesh.XDS.INTERVAL_MILLIS, TimeUnit.MILLISECONDS);
//
//                                LOGGER.info("{} is ready = {}", key, isReady);
//                                if (isReady) {
//                                    instance.addWatcher(servers -> {
//                                        LOGGER.info("EDS update received for {}, updating invokers size = {}", applicationName, servers.size());
//                                        updateAndSetInvokers(clientDto, xdsDirectory, servers, applicationName);
//                                    });
//                                    updateAndSetInvokers(clientDto, xdsDirectory, instance.getServers(), applicationName);
//                                } else {
//                                    LOGGER.warn("xDS not ready for {}, falling back to DNS", applicationName);
//                                    xdsDirectory.setInvokers(new BitList(Collections.singletonList(clientDto.getDnsInvoker().values())));
//                                }
//                            } catch (InterruptedException ex) {
//                                LOGGER.error("sleep interrupted", ex);
//                            }
//                            return instance;
//                        });
                clientDto.getManagedService().add(service);
            }
        }

    }

    private void updateAndSetInvokers(XdsDubboPeerInfo clientDto, XdsDirectory<?> xdsDirectory, List<LoadBalancer.Server> servers, String appName) {

        Map<String, Invoker<?>> appCache = clientDto.getInvokerCache().computeIfAbsent(appName, k -> new ConcurrentHashMap<>());

        Set<String> currentServerKeys = servers != null ? servers.stream()
                .map(server -> server.addr() + ":" + server.port())
                .collect(Collectors.toSet()) : Collections.emptySet();

        List<String> toRemove = appCache == null ? Collections.emptyList() : appCache.keySet().stream()
                .filter(key -> !currentServerKeys.contains(key))
                .collect(Collectors.toList());

        if (servers != null) {
            servers.forEach(server -> {
                String key = server.addr() + ":" + server.port();
                appCache.computeIfAbsent(key, k -> xdsDirectory.initInvoker(server.addr(), server.port(), appName));
            });
        }

        for (String key : toRemove) {
            Invoker<?> invoker = appCache.remove(key);
            if (invoker != null && invoker.isAvailable()) {
                try {
                    invoker.destroy();
                    if (LOGGER != null) {
                        LOGGER.info("Destroyed invoker for {}", key);
                    }
                } catch (Exception e) {
                    if (LOGGER != null) {
                        LOGGER.warn("Failed to destroy invoker for {}: {}", key, e.getMessage());
                    }
                }
            }
        }

        Map<String, Invoker<?>> globalInvokerCache = new HashMap<>();
        List<Invoker<?>> invokerList = clientDto.getInvokerCache().values().stream()
                .flatMap(innerMap -> {
                    globalInvokerCache.putAll(innerMap);
                    return innerMap.values().stream();
                })
                .collect(Collectors.toList());

        clientDto.setInvokers(globalInvokerCache);
        xdsDirectory.setInvokers(new BitList(invokerList));
    }

    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] allArguments, Class<?>[] argumentsTypes, MethodInvocationContext context) throws Throwable {
        if (!MeshContextManager.xdsAvailable()) {
            return;
        }
        final XdsDubboPeerInfo clientDto = (XdsDubboPeerInfo) enhancedInstance.getSkyWalkingDynamicField();
        if (clientDto == null || CollectionUtil.isEmpty(clientDto.getManagedService())) {
            context.defineReturnValue(new ArrayList<>(clientDto.getDnsInvoker().values()));
            return;
        }
        final Invocation invocation = (Invocation) allArguments[2];
        final List<ManagedProxylessService>  services = clientDto.getManagedService();
        ManagedProxylessService service = services.get(0);
        if (services.size() > 1) {
            service = services.get(ThreadLocalRandom.current().nextInt(services.size()));
        }
        final LoadBalancer.PickResult  pickResult = service.selectServer(LoadBalancer.PickServerArgs.create("/", invocation.getMethodName(), invocation.getAttachments(), Collections.emptyMap()));
        if (pickResult.getStatus().isOk() && pickResult.getServer() != null) {
            LoadBalancer.Server server = pickResult.getServer();
            String key = server.addr() + ":" + server.port();
            Invoker<?> invoker = clientDto.getInvokers().get(key);
            context.defineReturnValue(Collections.singletonList(invoker));
        } else {
            context.defineReturnValue(new ArrayList<>(clientDto.getDnsInvoker().values()));
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] allArguments, Class<?>[] argumentsTypes, Object ret, MethodInvocationContext context) throws Throwable {
        return ret;
    }

    @Override
    public Object handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] allArguments, Class<?>[] argumentsTypes, Throwable throwable, MethodInvocationContext context) throws Throwable {
        throw throwable;
    }

    static String parseFQDNServiceName(String host) {
        if (StringUtil.isEmpty(host) || "localhost".equals(host)) {
            return null;
        }
        // 1. if FQDN (i.e. <service>.<namespace>.svc.cluster.local) is used,
        if (host.endsWith(".svc.cluster.local")) {
            return host;
        }

        // 2. if <service>.<namespace>.svc is used,
        if (host.endsWith(".svc")) {
            return host + ".cluster.local";
        }

        // 3. if <service>.<namespace> is used,
        final String[] hostParts = host.split("\\.");
        if (hostParts.length == 2) {
            return host + ".svc.cluster.local";
        }

        // 4. if only short service is used,
        if (hostParts.length == 1) {
            return host + "." + HeraConfig.Kubernetes.Pod.NAMESPACE + ".svc.cluster.local";
        }

        // otherwise
        return null;
    }
}
