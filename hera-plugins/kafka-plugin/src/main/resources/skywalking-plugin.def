kafka=com.wosai.middleware.hera.plugin.kafka.define.CallbackInstrumentation
kafka=com.wosai.middleware.hera.plugin.kafka.define.CallbackInstrumentation
kafka=com.wosai.middleware.hera.plugin.kafka.define.KafkaTemplateCallbackInstrumentation
kafka=com.wosai.middleware.hera.plugin.kafka.define.AbstractConfigInstrumentation
kafka=com.wosai.middleware.hera.plugin.kafka.define.v0.LegacyKafkaProducerInstrumentation
kafka=com.wosai.middleware.hera.plugin.kafka.define.v0.LegacyKafkaConsumerInstrumentation
kafka=com.wosai.middleware.hera.plugin.kafka.define.v0.LegacyKafkaProducerMapInstrumentation
kafka=com.wosai.middleware.hera.plugin.kafka.define.v1.KafkaConsumerInstrumentation
kafka=com.wosai.middleware.hera.plugin.kafka.define.v1.KafkaProducerInstrumentation
kafka=com.wosai.middleware.hera.plugin.kafka.define.v1.KafkaProducerMapInstrumentation
kafka=com.wosai.middleware.hera.plugin.kafka.define.v37.Kafka37AsyncConsumerInstrumentation
kafka=com.wosai.middleware.hera.plugin.kafka.define.v37.Kafka37LegacyConsumerInstrumentation