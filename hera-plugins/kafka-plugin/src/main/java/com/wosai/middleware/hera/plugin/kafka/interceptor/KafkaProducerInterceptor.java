/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.wosai.middleware.hera.plugin.kafka.interceptor;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.plugin.kafka.define.HeadersMapInjectAdapter;
import com.wosai.middleware.hera.plugin.kafka.define.HeraKafkaPluginConfig;
import com.wosai.middleware.hera.plugin.kafka.define.KafkaProducerEnhanceInfos;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import com.wosai.middleware.hera.util.ExtraTags;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;

import java.lang.reflect.Method;

public class KafkaProducerInterceptor implements InstanceMethodsAroundInterceptor {

    public static final String OPERATE_NAME_PREFIX = "Kafka/";
    public static final String PRODUCER_OPERATE_NAME_SUFFIX = "/Producer";

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Throwable {

        ProducerRecord record = (ProducerRecord) allArguments[0];
        String topicName = record.topic();

        KafkaProducerEnhanceInfos enhanceInfos =
                (KafkaProducerEnhanceInfos) objInst.getSkyWalkingDynamicField();
        String peer = String.valueOf(enhanceInfos.getBootstrapServers());
        AbstractHeraSpan activeSpan = ContextManager.createExitSpan(OPERATE_NAME_PREFIX + topicName + PRODUCER_OPERATE_NAME_SUFFIX, peer);
        activeSpan.setTag(ExtraTags.MQ_BROKER, enhanceInfos.getBootstrapServers());
        Tags.MESSAGE_BUS_DESTINATION.set(activeSpan, topicName);
        Tags.COMPONENT.set(activeSpan, ComponentsDefine.KAFKA_PRODUCER.getName());
        Tags.SPAN_KIND.set(activeSpan, Tags.SPAN_KIND_PRODUCER);

        // Control whether inject span into kafka header
        if (HeraKafkaPluginConfig.Plugin.Kafka.ENABLE_INJECT_KAFKA_HEADER) {
            ContextManager.inject(activeSpan, new HeadersMapInjectAdapter(record.headers()));
        }

        //when use lambda expression, not to generate inner class,
        //    and not to trigger kafka CallBack class define, so allArguments[1] can't to cast EnhancedInstance
        Object shouldCallbackInstance = allArguments[1];
        if (null != shouldCallbackInstance) {
            if (shouldCallbackInstance instanceof EnhancedInstance) {
                EnhancedInstance callbackInstance = (EnhancedInstance) shouldCallbackInstance;

                if (null != activeSpan) {
                    CallbackCache cache = new CallbackCache();
                    cache.setParentSpan(activeSpan);
                    callbackInstance.setSkyWalkingDynamicField(cache);
                }
            } else if (shouldCallbackInstance instanceof Callback) {
                Callback callback = (Callback) shouldCallbackInstance;
                if (null != activeSpan) {
                    CallbackCache cache = new CallbackCache();
                    cache.setParentSpan(activeSpan);
                    cache.setCallback(callback);
                    allArguments[1] = new CallbackAdapterInterceptor(cache);
                }
            }
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) throws Throwable {
        ContextManager.stopSpan();
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {

    }
}