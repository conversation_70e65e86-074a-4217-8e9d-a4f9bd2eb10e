package com.wosai.middleware.hera.plugin.kafka.interceptor;

import com.wosai.middleware.hera.agent.services.KafkaContextManager;
import com.wosai.middleware.hera.plugin.kafka.define.ConsumerEnhanceRequiredInfo;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ConstructorWithMapInterceptPoint extends AbstractConstructorInterceptPoint<Map<String, ?>> {
    @Override
    protected ConsumerEnhanceRequiredInfo resolveConsumerEnhanceRequiredInfo(Map<String, ?> configArgument) {
        ConsumerEnhanceRequiredInfo requiredInfo = new ConsumerEnhanceRequiredInfo();

        if (configArgument != null) {
            // set the bootstrap server address
            requiredInfo.setBrokerServers(convertToList(configArgument.get("bootstrap.servers")));
            final String originalGroupId = (String) configArgument.get("group.id");
            requiredInfo.setOriginalGroupId(originalGroupId);
            requiredInfo.setGroupId(KafkaContextManager.getGroupId(originalGroupId));
        }

        return requiredInfo;
    }

    private List<String> convertToList(Object value) {
        if (value instanceof List)
            return (List<String>) value;
        else if (value instanceof String) {
            return Arrays.stream(((String) value).split(",")).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}
