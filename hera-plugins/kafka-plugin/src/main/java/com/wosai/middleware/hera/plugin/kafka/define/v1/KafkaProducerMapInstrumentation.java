package com.wosai.middleware.hera.plugin.kafka.define.v1;

import com.wosai.middleware.hera.plugin.kafka.define.v0.LegacyKafkaProducerMapInstrumentation;
import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.WitnessMethod;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;

import java.util.List;

import static org.apache.skywalking.apm.agent.core.plugin.bytebuddy.ArgumentTypeNameMatch.takesArgumentWithType;

/**
 * after version 2.1.0 use Map to config
 */
public class KafkaProducerMapInstrumentation extends LegacyKafkaProducerMapInstrumentation {
    public static final String CONSTRUCTOR_INTERCEPTOR_CLASS = "com.wosai.middleware.hera.plugin.kafka.interceptor.v1.ProducerConstructorMapInterceptor";

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[]{
                new ConstructorInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getConstructorMatcher() {
                        return takesArgumentWithType(0, CONSTRUCTOR_INTERCEPTOR_FLAG);
                    }

                    @Override
                    public String getConstructorInterceptor() {
                        return CONSTRUCTOR_INTERCEPTOR_CLASS;
                    }
                }
        };
    }

    @Override
    protected String[] getExtraWitnessClasses() {
        return new String[0];
    }

    @Override
    protected List<WitnessMethod> witnessMethods() {
        return WitnessMethodsConst.getWitnessMethodList();
    }
}
