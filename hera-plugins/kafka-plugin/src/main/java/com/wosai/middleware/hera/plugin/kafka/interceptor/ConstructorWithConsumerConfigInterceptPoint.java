package com.wosai.middleware.hera.plugin.kafka.interceptor;

import com.wosai.middleware.hera.agent.services.KafkaContextManager;
import com.wosai.middleware.hera.plugin.kafka.define.AbstractConfigEnhanceRequiredInfo;
import com.wosai.middleware.hera.plugin.kafka.define.ConsumerEnhanceRequiredInfo;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;

public class ConstructorWithConsumerConfigInterceptPoint extends AbstractConstructorInterceptPoint<ConsumerConfig> {
    @Override
    protected ConsumerEnhanceRequiredInfo resolveConsumerEnhanceRequiredInfo(ConsumerConfig configArgument) {
        ConsumerEnhanceRequiredInfo requiredInfo = new ConsumerEnhanceRequiredInfo();

        if (configArgument != null) {
            // set the bootstrap server address
            requiredInfo.setBrokerServers(configArgument.getList("bootstrap.servers"));
            final String originalGroupId;
            final String groupId;
            if (configArgument instanceof EnhancedInstance) {
                final AbstractConfigEnhanceRequiredInfo configEnhancedInfo = (AbstractConfigEnhanceRequiredInfo) ((EnhancedInstance) configArgument).getSkyWalkingDynamicField();
                originalGroupId = configEnhancedInfo.getOriginalGroupId();
                groupId = configEnhancedInfo.getGroupId();
            } else {
                originalGroupId = configArgument.getString("group.id");
                groupId = KafkaContextManager.getGroupId(originalGroupId);
            }

            requiredInfo.setOriginalGroupId(originalGroupId);
            requiredInfo.setGroupId(groupId);
        }

        return requiredInfo;
    }
}
