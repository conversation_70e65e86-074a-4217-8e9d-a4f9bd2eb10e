package com.wosai.middleware.hera.plugin.kafka.interceptor;

import org.apache.kafka.clients.producer.Callback;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;

/**
 * intercept Callback set cache
 **/
public class CallbackConstructorInterceptor implements InstanceConstructorInterceptor {
    @Override
    public void onConstruct(EnhancedInstance objInst, Object[] allArguments) {
        Callback callback = (Callback) allArguments[0];
        if (null != callback) {
            CallbackCache cache = new CallbackCache();
            cache.setCallback(callback);
            objInst.setSkyWalkingDynamicField(cache);
        }
    }
}
