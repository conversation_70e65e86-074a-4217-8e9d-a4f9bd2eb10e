/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.wosai.middleware.hera.plugin.kafka.interceptor;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.agent.services.KafkaContextManager;
import com.wosai.middleware.hera.plugin.kafka.define.ConsumerEnhanceRequiredInfo;
import com.wosai.middleware.hera.plugin.kafka.define.HeadersMapExtractAdapter;
import com.wosai.middleware.hera.plugin.kafka.define.KafkaConsumerEnhanceInfos;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import com.wosai.middleware.hera.tracing.tag.Tags;
import com.wosai.middleware.hera.util.ExtraTags;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.header.Header;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.agent.core.util.CollectionUtil;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.kafka.define.Constants;
import org.apache.skywalking.apm.plugin.kafka.define.KafkaContext;
import org.apache.skywalking.apm.util.StringUtil;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;

public class KafkaConsumerInterceptor implements InstanceMethodsAroundInterceptor {
    private static final ILog LOGGER = LogManager.getLogger(KafkaConsumerInterceptor.class);
    public static final String OPERATE_NAME_PREFIX = "Kafka/";
    public static final String CONSUMER_OPERATE_NAME = "/Consumer/";

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Throwable {
        KafkaConsumerEnhanceInfos enhanceInfos = (KafkaConsumerEnhanceInfos) objInst.getSkyWalkingDynamicField();
        ConsumerEnhanceRequiredInfo requiredInfo = enhanceInfos.getConsumerEnhanceRequiredInfo();
        requiredInfo.setStartTime(ContextManager.getClock().currentTimeMicros());
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) throws Throwable {
        /*
         * If the intercepted method throws exception, the ret will be null
         */
        if (ret == null) {
            return ret;
        }
        Map<TopicPartition, List<? extends ConsumerRecord<?, ?>>> records = fetchRecords(ret);
        //
        // The entry span will only be created when the consumer received at least one message.
        //
        if (records.size() > 0) {
            KafkaConsumerEnhanceInfos enhanceInfos = (KafkaConsumerEnhanceInfos) objInst.getSkyWalkingDynamicField();
            ConsumerEnhanceRequiredInfo requiredInfo = enhanceInfos.getConsumerEnhanceRequiredInfo();
            KafkaContext context = (KafkaContext) ContextManager.getRuntimeContext().get(Constants.KAFKA_FLAG);
            final String topics = requiredInfo.getTopics();
            final String originalGroupId = requiredInfo.getOriginalGroupId();
            final String clusterId = requiredInfo.getClusterID();
            // Filter consumer records based on env-flag(s)
            // @since 1.14.0
            if (KafkaContextManager.enabled()) {
                // TODO: add metrics to record how many records are skipped
                long filterNum = 0L;
                Set<String> skippedEnvFlags = new HashSet<>();
                Map<TopicPartition, List<? extends ConsumerRecord<?, ?>>> filteredRecords = new HashMap<>();
                for (final Map.Entry<TopicPartition, List<? extends ConsumerRecord<?, ?>>> entry : records.entrySet()) {
                    final TopicPartition topicPartition = entry.getKey();
                    final String topic = topicPartition.topic();
                    List<ConsumerRecord<?, ?>> recordsToBeConsumed = entry.getValue().stream()
                            .filter(r -> {
                                String envFlag = extractEnvFlag(r); // TODO: x-env-flag? Extract to a private method
                                boolean shouldConsume = KafkaContextManager.shouldConsume(clusterId, topic, originalGroupId, envFlag);
                                if (!shouldConsume && StringUtil.isNotEmpty(envFlag)) {
                                    skippedEnvFlags.add(envFlag);
                                }
                                return shouldConsume;
                            }
                            ).collect(Collectors.toList());
                    filterNum += entry.getValue().size() - recordsToBeConsumed.size();
                    if (CollectionUtil.isEmpty(recordsToBeConsumed)) {
                        continue;
                    }
                    filteredRecords.put(topicPartition, recordsToBeConsumed);
                }
                if (filterNum > 0) {
                    LOGGER.info("{} records skipped", filterNum);
                }
                if (!skippedEnvFlags.isEmpty()) {
                    LOGGER.info("Skipped records with envFlags: {}", skippedEnvFlags);
                }
                records = filteredRecords;
            }
            final String groupId = requiredInfo.getGroupId();
            ArrayList<HeraSpanContext> parentSpanList = new ArrayList<>();
            for (List<? extends ConsumerRecord<?, ?>> consumerRecords : records.values()) {
                for (ConsumerRecord<?, ?> record : consumerRecords) {
                    HeadersMapExtractAdapter adapter = new HeadersMapExtractAdapter(record.headers());
                    HeraSpanContext parentSpan = ContextManager.extract(adapter);
                    parentSpanList.add(parentSpan);
                }
            }
            HeraSpanContext[] parentSpans = parentSpanList.toArray(new HeraSpanContext[0]);

            if (context != null) {
                ContextManager.createEntrySpan(context.getOperationName(), parentSpans);
                context.setNeedStop(true);
            }
            String operationName = OPERATE_NAME_PREFIX + topics + CONSUMER_OPERATE_NAME + groupId;
            AbstractHeraSpan activeSpan = ContextManager.createEntrySpan(operationName, requiredInfo.getStartTime(), parentSpans);
            Tags.COMPONENT.set(activeSpan, ComponentsDefine.KAFKA_CONSUMER.getName());
            Tags.SPAN_KIND.set(activeSpan, Tags.SPAN_KIND_CONSUMER);
            Tags.MESSAGE_BUS_DESTINATION.set(activeSpan, topics);

            activeSpan.setTag(ExtraTags.MQ_BROKER, requiredInfo.getBrokerServers());

            ContextManager.stopSpan();
        }

        return wrap(records);
    }

    /**
     * Extract EnvFlag from ConsumerRecord
     *
     * @param record a record to be processed
     * @return env-flag in the Header part. Null if not exists.
     */
    private static String extractEnvFlag(ConsumerRecord<?, ?> record) {
        Header envFlagHeader = record.headers().lastHeader(com.wosai.middleware.hera.agent.mesh.Constants.TAG_ROUTING);
        if (envFlagHeader == null) {
            return null;
        }
        final byte[] payload = envFlagHeader.value();
        if (payload == null || payload.length == 0) {
            return null;
        }
        return new String(payload);
    }

    @SuppressWarnings({"unchecked"})
    protected Map<TopicPartition, List<? extends ConsumerRecord<?, ?>>> fetchRecords(Object retObj) {
        return (Map<TopicPartition, List<? extends ConsumerRecord<?, ?>>>) retObj;
    }

    protected Object wrap(Map<TopicPartition, List<? extends ConsumerRecord<?, ?>>> newRecords) {
        return newRecords;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {
        /*
         * The entry span is created in {@link #afterMethod}, but {@link #handleMethodException} is called before
         * {@link #afterMethod}, before the creation of entry span, we can not ensure there is an active span
         */
        if (ContextManager.isActive()) {
            ContextManager.logError(t);
        }
    }
}
