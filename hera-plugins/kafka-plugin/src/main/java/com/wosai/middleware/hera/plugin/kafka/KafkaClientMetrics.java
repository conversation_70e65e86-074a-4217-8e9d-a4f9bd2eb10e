package com.wosai.middleware.hera.plugin.kafka;

import com.wosai.middleware.hera.agent.metrics.MeterBinder;
import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.BaseMeter;
import com.wosai.middleware.hera.agent.metrics.api.FunctionCounter;
import com.wosai.middleware.hera.agent.metrics.api.Gauge;
import com.wosai.middleware.hera.agent.metrics.api.MeterId;
import com.wosai.middleware.hera.agent.metrics.api.Tag;
import com.wosai.middleware.hera.agent.metrics.api.Tags;
import org.apache.kafka.common.Metric;
import org.apache.kafka.common.MetricName;
import org.apache.skywalking.apm.agent.core.boot.DefaultNamedThreadFactory;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;

import javax.annotation.Nullable;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import java.util.function.ToDoubleFunction;
import java.util.stream.Collectors;

import static java.util.Collections.emptyList;

public class KafkaClientMetrics implements MeterBinder, AutoCloseable {
    private static final ILog LOGGER = LogManager.getLogger(KafkaClientMetrics.class);
    static final String METRIC_NAME_PREFIX = "kafka.";
    static final String METRIC_GROUP_APP_INFO = "app-info";
    static final String METRIC_GROUP_METRICS_COUNT = "kafka-metrics-count";
    static final String VERSION_METRIC_NAME = "version";
    static final String START_TIME_METRIC_NAME = "start-time-ms";
    static final Duration DEFAULT_REFRESH_INTERVAL = Duration.ofSeconds(60);
    static final String KAFKA_VERSION_TAG_NAME = "kafka.version";
    static final String DEFAULT_VALUE = "unknown";

    private final Supplier<Map<MetricName, ? extends Metric>> metricsSupplier;
    private final AtomicReference<Map<MetricName, ? extends Metric>> metrics = new AtomicReference<>();
    private final Iterable<Tag> extraTags;
    private final Duration refreshInterval;
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(new DefaultNamedThreadFactory("micrometer-kafka-metrics"));

    @Nullable
    private Iterable<Tag> commonTags;

    /**
     * Keeps track of current set of metrics.
     */
    private volatile Set<MetricName> currentMeters = new HashSet<>();

    private String kafkaVersion = DEFAULT_VALUE;

    private final Set<MeterId> registeredMeterIds = ConcurrentHashMap.newKeySet();

    public KafkaClientMetrics(Supplier<Map<MetricName, ? extends Metric>> metricsSupplier) {
        this(metricsSupplier, emptyList());
    }

    public KafkaClientMetrics(Supplier<Map<MetricName, ? extends Metric>> metricsSupplier, Iterable<Tag> extraTags) {
        this(metricsSupplier, extraTags, DEFAULT_REFRESH_INTERVAL);
    }

    KafkaClientMetrics(Supplier<Map<MetricName, ? extends Metric>> metricsSupplier, Iterable<Tag> extraTags, Duration refreshInterval) {
        this.metricsSupplier = metricsSupplier;
        this.extraTags = extraTags;
        this.refreshInterval = refreshInterval;
    }

    @Override
    public void bindTo() {
        commonTags = MetricsHandler.KafkaMetricsHelper.getCommonTags();
        prepareToBindMetrics();
        checkAndBindMetrics();
        scheduler.scheduleAtFixedRate(this::checkAndBindMetrics, getRefreshIntervalInMillis(),
                getRefreshIntervalInMillis(), TimeUnit.MILLISECONDS);
    }

    /**
     * Define common tags and meters before binding metrics
     */
    void prepareToBindMetrics() {
        this.metrics.set(this.metricsSupplier.get());
        Map<MetricName, ? extends Metric> metrics = metricsSupplier.get();
        // Collect static metrics and tags
        MetricName startTime = null;

        for (Map.Entry<MetricName, ? extends Metric> entry : metrics.entrySet()) {
            MetricName name = entry.getKey();
            if (METRIC_GROUP_APP_INFO.equals(name.group()))
                if (VERSION_METRIC_NAME.equals(name.name())) {
                    kafkaVersion = (String) entry.getValue().metricValue();
                } else if (START_TIME_METRIC_NAME.equals(name.name())) {
                    startTime = entry.getKey();
                }
        }

        if (startTime != null) {
            bindMeter(startTime, meterName(startTime), meterTags(startTime));
        }
    }

    private long getRefreshIntervalInMillis() {
        return refreshInterval.toMillis();
    }

    /**
     * Gather metrics from Kafka metrics API and register Meters.
     * <p>
     * As this is a one-off execution when binding a Kafka client, Meters include a call to this
     * validation to double-check new metrics when returning values. This should only add the cost of
     * comparing meters last returned from the Kafka client.
     */
    void checkAndBindMetrics() {
        try {
            Map<MetricName, ? extends Metric> currentMetrics = metricsSupplier.get();
            this.metrics.set(currentMetrics);

            if (!currentMeters.equals(currentMetrics.keySet())) {
                Set<MetricName> metricsToRemove = currentMeters.stream()
                        .filter(metricName -> !currentMetrics.containsKey(metricName)).collect(Collectors.toSet());

                for (MetricName metricName : metricsToRemove) {
                    MeterId id = meterIdForComparison(metricName);
                    MetricsHandler.remove(id);
                    registeredMeterIds.remove(id);
                }

                currentMeters = new HashSet<>(currentMetrics.keySet());

                Map<String, List<BaseMeter<?>>> registryMetersByNames = MetricsHandler.getMeters().stream()
                        .collect(Collectors.groupingBy(meter -> meter.getId().getName()));

                currentMetrics.forEach((name, metric) -> {
                    // Filter out non-numeric values
                    // Filter out metrics from groups that include metadata
                    if (!(metric.metricValue() instanceof Number) || METRIC_GROUP_APP_INFO.equals(name.group())
                            || METRIC_GROUP_METRICS_COUNT.equals(name.group())) {
                        return;
                    }

                    String meterName = meterName(name);

                    // Kafka has metrics with lower number of tags (e.g. with/without
                    // topic or partition tag)
                    // Remove meters with lower number of tags
                    boolean hasLessTags = false;
                    for (BaseMeter<?> other : registryMetersByNames.getOrDefault(meterName, emptyList())) {
                        MeterId otherId = other.getId();
                        List<Tag> tags = otherId.getTags();
                        List<Tag> meterTagsWithCommonTags = meterTags(name, true);
                        if (tags.size() < meterTagsWithCommonTags.size()) {
                            MetricsHandler.remove(otherId);
                            registeredMeterIds.remove(otherId);
                        }
                        // Check if already exists
                        else if (tags.size() == meterTagsWithCommonTags.size())
                            if (tags.containsAll(meterTagsWithCommonTags))
                                return;
                            else
                                break;
                        else
                            hasLessTags = true;
                    }
                    if (hasLessTags)
                        return;

                    List<Tag> tags = meterTags(name);
                    try {
                        BaseMeter<?> meter = bindMeter(metric.metricName(), meterName, tags);
                        List<BaseMeter<?>> meters = registryMetersByNames.computeIfAbsent(meterName, k -> new ArrayList<>());
                        meters.add(meter);
                    } catch (Exception ex) {
                        String message = ex.getMessage();
                        if (message != null && message.contains("Prometheus requires")) {
                            LOGGER.warn("Failed to bind meter: {} {}. However, this could happen and might be restored in the next refresh.", meterName, tags);
                        } else {
                            LOGGER.warn(ex, "Failed to bind meter: {} {}.", meterName, tags);
                        }
                    }
                });
            }
        } catch (Exception e) {
            LOGGER.warn(e, "Failed to bind KafkaMetric");
        }
    }

    private BaseMeter<?> bindMeter(MetricName metricName, String meterName, Iterable<Tag> tags) {
        BaseMeter<?> meter = registerMeter(metricName, meterName, tags);
        registeredMeterIds.add(meter.getId());
        return meter;
    }

    private BaseMeter<?> registerMeter(MetricName metricName, String meterName, Iterable<Tag> tags) {
        if (meterName.endsWith("total") || meterName.endsWith("count")) {
            return registerCounter(metricName, meterName, tags);
        } else {
            return registerGauge(metricName, meterName, tags);
        }
    }

    private Gauge registerGauge(MetricName metricName, String meterName, Iterable<Tag> tags) {
        return Gauge.builder(meterName, this.metrics, toMetricValue(metricName)).tags(tags)
                .description(metricName.description()).build();
    }

    private FunctionCounter registerCounter(MetricName metricName, String meterName,
                                            Iterable<Tag> tags) {
        return FunctionCounter.builder(meterName, this.metrics, toMetricValue(metricName)).tags(tags)
                .description(metricName.description()).build();
    }

    private ToDoubleFunction<AtomicReference<Map<MetricName, ? extends Metric>>> toMetricValue(MetricName metricName) {
        return metricsReference -> toDouble(metricsReference.get().get(metricName));
    }

    private double toDouble(@Nullable Metric metric) {
        return metric != null ? ((Number) metric.metricValue()).doubleValue() : Double.NaN;
    }

    private List<Tag> meterTags(MetricName metricName, boolean includeCommonTags) {
        List<Tag> tags = new ArrayList<>();
        metricName.tags().forEach((key, value) -> tags.add(Tag.of(key.replaceAll("-", "."), value)));
        tags.add(Tag.of(KAFKA_VERSION_TAG_NAME, kafkaVersion));
        extraTags.forEach(tags::add);
        if (includeCommonTags) {
            commonTags.forEach(tags::add);
        }
        return tags;
    }

    private List<Tag> meterTags(MetricName metricName) {
        return meterTags(metricName, false);
    }

    private String meterName(MetricName metricName) {
        String name = METRIC_NAME_PREFIX + metricName.group() + "." + metricName.name();
        return name.replaceAll("-metrics", "").replaceAll("-", ".");
    }

    private MeterId meterIdForComparison(MetricName metricName) {
        return new MeterId(meterName(metricName), Tags.of(meterTags(metricName, true)), null, null, MeterId.Type.OTHER);
    }

    @Override
    public void close() {
        this.scheduler.shutdownNow();

        for (MeterId id : registeredMeterIds) {
            MetricsHandler.remove(id);
        }
    }
}
