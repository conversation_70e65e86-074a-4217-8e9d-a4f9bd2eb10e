package com.wosai.middleware.hera.plugin.kafka.interceptor.v1;

/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.plugin.kafka.KafkaClientMetrics;
import com.wosai.middleware.hera.plugin.kafka.define.KafkaProducerEnhanceInfos;
import com.wosai.middleware.hera.plugin.kafka.interceptor.v0.LegacyProducerConstructorMapInterceptor;
import org.apache.kafka.clients.producer.Producer;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;

/**
 * Target at kafka-clients >=2.1.0
 */
public class ProducerConstructorMapInterceptor extends LegacyProducerConstructorMapInterceptor {

    @Override
    public void onConstruct(EnhancedInstance objInst, Object[] allArguments) {
        if (objInst.getSkyWalkingDynamicField() == null) {
            Producer producer = (Producer) objInst;
            KafkaClientMetrics metrics = new KafkaClientMetrics(producer::metrics);
            objInst.setSkyWalkingDynamicField(new KafkaProducerEnhanceInfos(metrics));
            MetricsHandler.bind(metrics);
            super.onConstruct(objInst, allArguments);
        }
    }
}
