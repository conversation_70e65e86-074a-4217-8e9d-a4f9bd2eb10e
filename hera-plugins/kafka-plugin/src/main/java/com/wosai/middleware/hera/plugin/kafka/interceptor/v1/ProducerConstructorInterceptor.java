package com.wosai.middleware.hera.plugin.kafka.interceptor.v1;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.plugin.kafka.KafkaClientMetrics;
import com.wosai.middleware.hera.plugin.kafka.define.KafkaProducerEnhanceInfos;
import com.wosai.middleware.hera.plugin.kafka.interceptor.v0.LegacyProducerConstructorInterceptor;
import org.apache.kafka.clients.producer.Producer;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;

public class ProducerConstructorInterceptor extends LegacyProducerConstructorInterceptor {
    @Override
    public void onConstruct(EnhancedInstance objInst, Object[] allArguments) {
        Producer producer = (Producer) objInst;
        KafkaClientMetrics metrics = new KafkaClientMetrics(producer::metrics);
        objInst.setSkyWalkingDynamicField(new KafkaProducerEnhanceInfos(metrics));
        MetricsHandler.bind(metrics);
        super.onConstruct(objInst, allArguments);
    }
}

