package com.wosai.middleware.hera.plugin.kafka.define.v1;

import net.bytebuddy.matcher.ElementMatchers;
import org.apache.skywalking.apm.agent.core.plugin.WitnessMethod;

import java.util.Collections;
import java.util.List;

public class WitnessMethodsConst {
    private static final String WITNESS_CLASS = "org.apache.kafka.common.metrics.KafkaMetric";
    private static final String WITNESS_METHOD = "metricValue";

    public static List<WitnessMethod> getWitnessMethodList() {
        return Collections.singletonList(new WitnessMethod(WITNESS_CLASS, ElementMatchers.named(WITNESS_METHOD)));
    }
}
