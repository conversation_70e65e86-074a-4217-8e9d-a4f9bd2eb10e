/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.wosai.middleware.hera.plugin.kafka.define.v0;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.InstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;

import java.util.Map;

import static net.bytebuddy.matcher.ElementMatchers.named;
import static net.bytebuddy.matcher.ElementMatchers.returns;
import static org.apache.skywalking.apm.agent.core.plugin.bytebuddy.ArgumentTypeNameMatch.takesArgumentWithType;
import static org.apache.skywalking.apm.agent.core.plugin.match.NameMatch.byName;

/**
 * Here is the intercept process steps.
 *
 * <pre>
 *  1. Record the topic when the client invoke <code>subscribed</code> method
 *  2. Create the entry span when the client invoke the method <code>pollOnce</code>.
 *  3. Extract all the <code>Trace Context</code> by iterate all <code>ConsumerRecord</code>
 *  4. Stop the entry span when <code>pollOnce</code> method finished.
 * </pre>
 */
public class LegacyKafkaConsumerInstrumentation extends LegacyKafkaInstrumentation {
    public static final String CONSTRUCTOR_INTERCEPT_TYPE = "org.apache.kafka.clients.consumer.ConsumerConfig";
    public static final String CONSTRUCTOR_INTERCEPT_MAP_TYPE = "java.util.Map";
    public static final String CONSTRUCTOR_INTERCEPTOR_CLASS = "com.wosai.middleware.hera.plugin.kafka.interceptor.ConstructorWithConsumerConfigInterceptPoint";
    public static final String INTERCEPTOR_CLASS = "com.wosai.middleware.hera.plugin.kafka.interceptor.KafkaConsumerInterceptor";
    public static final String INTERCEPTOR_CLASS_KAFKA3_2 = "com.wosai.middleware.hera.plugin.kafka.interceptor.Kafka3ConsumerInterceptor";
    public static final String ENHANCE_METHOD = "pollOnce";
    public static final String ENHANCE_COMPATIBLE_METHOD = "pollForFetches";
    public static final String ENHANCE_CLASS = "org.apache.kafka.clients.consumer.KafkaConsumer";
    public static final String SUBSCRIBE_METHOD = "subscribe";
    public static final String SUBSCRIBE_INTERCEPT_TYPE_PATTERN = "java.util.regex.Pattern";
    public static final String SUBSCRIBE_INTERCEPT_TYPE_NAME = "java.util.Collection";
    public static final String SUBSCRIBE_INTERCEPT_CLASS = "com.wosai.middleware.hera.plugin.kafka.interceptor.SubscribeMethodInterceptor";
    public static final String ASSIGN_METHOD = "assign";
    public static final String ASSIGN_INTERCEPT_CLASS = "com.wosai.middleware.hera.plugin.kafka.interceptor.AssignMethodInterceptor";
    public static final String ASSIGN_INTERCEPT_TYPE_NAME = "java.util.Collection";
    public static final String CLOSE_METHOD = "close";
    public static final String CLOSE_INTERCEPT_CLASS = "com.wosai.middleware.hera.plugin.kafka.interceptor.KafkaConsumerCloseMethodInterceptor";
    public static final String CLOSE_INTERCEPT_TYPE_NAME = "boolean";

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[]{
                new ConstructorInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getConstructorMatcher() {
                        return takesArgumentWithType(0, CONSTRUCTOR_INTERCEPT_TYPE);
                    }

                    @Override
                    public String getConstructorInterceptor() {
                        return CONSTRUCTOR_INTERCEPTOR_CLASS;
                    }
                }
        };
    }

    @Override
    public InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints() {
        return getInstanceMethodsInterceptPoints(INTERCEPTOR_CLASS);
    }

    protected static InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints(String interceptorClass) {
        return new InstanceMethodsInterceptPoint[]{
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        // targeting Kafka Client < 3.2
                        return named(ENHANCE_METHOD).or(named(ENHANCE_COMPATIBLE_METHOD).and(returns(Map.class)));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return interceptorClass;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        // targeting Kafka Client >= 3.2
                        return named(ENHANCE_COMPATIBLE_METHOD).and(returns(named("org.apache.kafka.clients.consumer.internals.Fetch")));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return INTERCEPTOR_CLASS_KAFKA3_2;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(SUBSCRIBE_METHOD)
                                .and(takesArgumentWithType(0, SUBSCRIBE_INTERCEPT_TYPE_NAME));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return SUBSCRIBE_INTERCEPT_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(SUBSCRIBE_METHOD)
                                .and(takesArgumentWithType(0, SUBSCRIBE_INTERCEPT_TYPE_PATTERN));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return SUBSCRIBE_INTERCEPT_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(ASSIGN_METHOD)
                                .and(takesArgumentWithType(0, ASSIGN_INTERCEPT_TYPE_NAME));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return ASSIGN_INTERCEPT_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                },
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named(CLOSE_METHOD)
                                .and(takesArgumentWithType(1, CLOSE_INTERCEPT_TYPE_NAME));
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return CLOSE_INTERCEPT_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }

    @Override
    protected ClassMatch enhanceClass() {
        return byName(ENHANCE_CLASS);
    }
}
