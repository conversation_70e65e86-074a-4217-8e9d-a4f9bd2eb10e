package com.wosai.middleware.hera.plugin.kafka.define.v37;

import com.wosai.middleware.hera.plugin.kafka.define.v1.KafkaConsumerInstrumentation;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;

import static org.apache.skywalking.apm.agent.core.plugin.match.NameMatch.byName;

/**
 * For Kafka 3.7.x change
 *
 * <pre>
 *  1. The method named pollFor<PERSON><PERSON><PERSON> was removed from KafkaConsumer to <code>AsyncKafkaConsumer</code> and <code>LegacyKafkaConsumer</code>
 *  2. Because of the enhance class was changed, so we should create new Instrumentation to intercept the method
 * </pre>
 */
public class Kafka37LegacyConsumerInstrumentation extends KafkaConsumerInstrumentation {

    private static final String ENHANCE_CLASS_37_LEGACY = "org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer";

    @Override
    protected ClassMatch enhanceClass() {
        return byName(ENHANCE_CLASS_37_LEGACY);
    }
}
