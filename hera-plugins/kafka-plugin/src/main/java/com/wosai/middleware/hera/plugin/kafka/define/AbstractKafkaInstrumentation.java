package com.wosai.middleware.hera.plugin.kafka.define;

import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.ClassInstanceMethodsEnhancePluginDefine;
import org.apache.skywalking.apm.dependencies.com.google.common.collect.Lists;

public abstract class AbstractKafkaInstrumentation extends ClassInstanceMethodsEnhancePluginDefine {
    protected static final String ORG_APACHE_KAFKA_CLIENTS_API_VERSIONS = "org.apache.kafka.clients.ApiVersions";

    @Override
    protected String[] witnessClasses() {
        return Lists.asList(ORG_APACHE_KAFKA_CLIENTS_API_VERSIONS, this.getExtraWitnessClasses()).toArray(new String[]{});
    }

    protected String[] getExtraWitnessClasses() {
        return new String[0];
    }
}
