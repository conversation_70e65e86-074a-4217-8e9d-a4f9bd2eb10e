package com.wosai.middleware.hera.plugin.kafka.interceptor.v0;

import com.wosai.middleware.hera.plugin.kafka.define.KafkaProducerEnhanceInfos;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;
import org.apache.skywalking.apm.util.StringUtil;

public class LegacyProducerConstructorInterceptor implements InstanceConstructorInterceptor {
    @Override
    public void onConstruct(EnhancedInstance objInst, Object[] allArguments) {
        ProducerConfig config = (ProducerConfig) allArguments[0];
        KafkaProducerEnhanceInfos enhanceInfos = (KafkaProducerEnhanceInfos) objInst.getSkyWalkingDynamicField();
        enhanceInfos.setBootstrapServers(StringUtil.join(';', config.getList("bootstrap.servers")
                .toArray(new String[0])));
        objInst.setSkyWalkingDynamicField(enhanceInfos);
    }

}

