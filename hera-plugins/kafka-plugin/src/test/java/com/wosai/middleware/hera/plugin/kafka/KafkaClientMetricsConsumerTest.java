package com.wosai.middleware.hera.plugin.kafka;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.Tags;
import com.wosai.middleware.hera.test.HeraInMemoryMetrics;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.junit.After;
import org.junit.Rule;
import org.junit.Test;

import java.util.Properties;

import static com.wosai.middleware.hera.plugin.kafka.KafkaClientMetrics.METRIC_NAME_PREFIX;
import static org.apache.kafka.clients.consumer.ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.GROUP_ID_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG;
import static org.assertj.core.api.Assertions.assertThat;

public class KafkaClientMetricsConsumerTest {

    private static final String BOOTSTRAP_SERVERS = "localhost:9092";

    private Tags tags = Tags.of("app", "myapp", "version", "1");

    KafkaClientMetrics metrics;

    @Rule
    public HeraInMemoryMetrics heraInMemoryMetrics = new HeraInMemoryMetrics();

    @After
    public void afterEach() {
        if (metrics != null)
            metrics.close();
    }

    @Test
    public void shouldCreateMeters() {
        try (Consumer<String, String> consumer = createConsumer()) {
            metrics = new KafkaClientMetrics(consumer::metrics);
            metrics.bindTo();

            assertThat(MetricsHandler.getMeters()).hasSizeGreaterThan(0).extracting(meter -> meter.getId().getName())
                    .allMatch(s -> s.startsWith(METRIC_NAME_PREFIX));
        }
    }

    @Test
    public void shouldCreateMetersWithTags() {
        try (Consumer<String, String> consumer = createConsumer()) {
            metrics = new KafkaClientMetrics(consumer::metrics, tags);
            metrics.bindTo();

            assertThat(MetricsHandler.getMeters()).hasSizeGreaterThan(0).extracting(meter -> meter.getId().getTag("app"))
                    .allMatch(s -> s.equals("myapp"));
        }
    }

    private Consumer<String, String> createConsumer() {
        Properties consumerConfig = new Properties();
        consumerConfig.put(BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        consumerConfig.put(KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(GROUP_ID_CONFIG, "group");
        return new KafkaConsumer<>(consumerConfig);
    }
}
