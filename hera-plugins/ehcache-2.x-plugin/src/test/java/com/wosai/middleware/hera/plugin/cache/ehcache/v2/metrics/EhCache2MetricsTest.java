package com.wosai.middleware.hera.plugin.cache.ehcache.v2.metrics;

import com.wosai.middleware.hera.agent.metrics.AbstractCacheMetricsTest;
import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.FunctionCounter;
import com.wosai.middleware.hera.agent.metrics.api.Gauge;
import com.wosai.middleware.hera.agent.metrics.api.Tags;
import com.wosai.middleware.hera.test.HeraInMemoryMetrics;
import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.statistics.StatisticsGateway;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;

import java.util.Random;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

public class EhCache2MetricsTest extends AbstractCacheMetricsTest {
    private static CacheManager CACHE_MANAGER;

    private static Cache CACHE;

    private EhCache2Metrics metrics = new EhCache2Metrics(CACHE);

    @Rule
    public HeraInMemoryMetrics heraInMemoryMetrics = new HeraInMemoryMetrics();

    @Test
    public void reportMetrics() {
        metrics.bindTo();

        verifyCommonCacheMetrics(metrics);

        StatisticsGateway stats = CACHE.getStatistics();

        Gauge remoteSize = MetricsHandler.find("ehcache.cache.remoteSize").gauge();
        assertThat(remoteSize.value()).isEqualTo(stats.getRemoteSize());

        FunctionCounter cacheRemovals = MetricsHandler.find("ehcache.cache.removals").functionCounter();
        assertThat(cacheRemovals.count()).isEqualTo(stats.cacheRemoveCount());

        String cacheAdded = "ehcache.cache.puts.added";
        FunctionCounter putsAdded = MetricsHandler.find(cacheAdded).tags(Tags.of("result", "added")).functionCounter();
        assertThat(putsAdded.count()).isEqualTo(stats.cachePutAddedCount());

        FunctionCounter putsUpdated = MetricsHandler.find(cacheAdded).tags(Tags.of("result", "updated")).functionCounter();
        assertThat(putsUpdated.count()).isEqualTo(stats.cachePutUpdatedCount());

        Gauge offHeapSize = MetricsHandler.find("ehcache.cache.local.offheap.size").gauge();
        assertThat(offHeapSize.value()).isEqualTo(stats.getLocalOffHeapSizeInBytes());

        Gauge heapSize = MetricsHandler.find("ehcache.cache.local.heap.size").gauge();
        assertThat(heapSize.value()).isEqualTo(stats.getLocalHeapSizeInBytes());

        Gauge diskSize = MetricsHandler.find("ehcache.cache.local.disk.size").gauge();
        assertThat(diskSize.value()).isEqualTo(stats.getLocalDiskSizeInBytes());

        // miss metrics
        String misses = "ehcache.cache.misses";
        FunctionCounter expiredMisses = MetricsHandler.find(misses).tags(Tags.of("reason", "expired")).functionCounter();
        assertThat(expiredMisses.count()).isEqualTo(stats.cacheMissExpiredCount());

        FunctionCounter notFoundMisses = MetricsHandler.find(misses).tags(Tags.of("reason", "notFound")).functionCounter();
        assertThat(notFoundMisses.count()).isEqualTo(stats.cacheMissNotFoundCount());

        // commit transaction metrics
        String xaCommits = "ehcache.cache.xa.commits";
        FunctionCounter readOnlyCommits = MetricsHandler.find(xaCommits).tags(Tags.of("result", "readOnly")).functionCounter();
        assertThat(readOnlyCommits.count()).isEqualTo(stats.xaCommitReadOnlyCount());

        FunctionCounter exceptionCommits = MetricsHandler.find(xaCommits).tags(Tags.of("result", "exception")).functionCounter();
        assertThat(exceptionCommits.count()).isEqualTo(stats.xaCommitExceptionCount());

        FunctionCounter committedCommits = MetricsHandler.find(xaCommits).tags(Tags.of("result", "committed")).functionCounter();
        assertThat(committedCommits.count()).isEqualTo(stats.xaCommitCommittedCount());

        // rollback transaction metrics
        String xaRollbacks = "ehcache.cache.xa.rollbacks";
        FunctionCounter exceptionRollback = MetricsHandler.find(xaRollbacks).tags(Tags.of("result", "exception"))
                .functionCounter();
        assertThat(exceptionRollback.count()).isEqualTo(stats.xaRollbackExceptionCount());

        FunctionCounter successRollback = MetricsHandler.find(xaRollbacks).tags(Tags.of("result", "success")).functionCounter();
        assertThat(successRollback.count()).isEqualTo(stats.xaRollbackSuccessCount());

        // recovery transaction metrics
        String xaRecoveries = "ehcache.cache.xa.recoveries";
        FunctionCounter nothingRecovered = MetricsHandler.find(xaRecoveries).tags(Tags.of("result", "nothing"))
                .functionCounter();
        assertThat(nothingRecovered.count()).isEqualTo(stats.xaRecoveryNothingCount());

        FunctionCounter successRecoveries = MetricsHandler.find(xaRecoveries).tags(Tags.of("result", "success"))
                .functionCounter();
        assertThat(successRecoveries.count()).isEqualTo(stats.xaRecoveryRecoveredCount());
    }

    @Test
    public void constructInstanceViaStaticMethodMonitor() {
        EhCache2Metrics.monitor(CACHE);

        MetricsHandler.find("ehcache.cache.remoteSize").tags(expectedTag).gauge();
    }

    @Test
    public void returnCacheSize() {
        StatisticsGateway stats = CACHE.getStatistics();
        assertThat(metrics.size()).isEqualTo(stats.getSize());
    }

    @Test
    public void returnEvictionCount() {
        StatisticsGateway stats = CACHE.getStatistics();
        assertThat(metrics.evictionCount()).isEqualTo(stats.cacheEvictedCount());
    }

    @Test
    public void returnHitCount() {
        StatisticsGateway stats = CACHE.getStatistics();
        assertThat(metrics.hitCount()).isEqualTo(stats.cacheHitCount());
    }

    @Test
    public void returnMissCount() {
        StatisticsGateway stats = CACHE.getStatistics();
        assertThat(metrics.missCount()).isEqualTo(stats.cacheMissCount());
    }

    @Test
    public void returnPutCount() {
        StatisticsGateway stats = CACHE.getStatistics();
        assertThat(metrics.putCount()).isEqualTo(stats.cachePutCount());
    }

    @BeforeClass
    public static void setup() {
        CACHE_MANAGER = CacheManager.newInstance();
        CACHE_MANAGER.addCache("testCache");
        CACHE = spy(CACHE_MANAGER.getCache("testCache"));
        StatisticsGateway stats = mock(StatisticsGateway.class);
        // generate non-negative random value to address false-positives
        int valueBound = 100000;
        Random random = new Random();
        when(stats.getSize()).thenReturn((long) random.nextInt(valueBound));
        when(stats.cacheEvictedCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.cacheHitCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.cacheMissCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.cachePutCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.getRemoteSize()).thenReturn((long) random.nextInt(valueBound));
        when(stats.cacheRemoveCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.cachePutAddedCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.cachePutUpdatedCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.getLocalOffHeapSizeInBytes()).thenReturn((long) random.nextInt(valueBound));
        when(stats.getLocalHeapSizeInBytes()).thenReturn((long) random.nextInt(valueBound));
        when(stats.getLocalDiskSizeInBytes()).thenReturn((long) random.nextInt(valueBound));
        when(stats.cacheMissExpiredCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.cacheMissNotFoundCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.xaCommitCommittedCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.xaCommitExceptionCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.xaCommitReadOnlyCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.xaRollbackExceptionCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.xaRollbackSuccessCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.xaRecoveryRecoveredCount()).thenReturn((long) random.nextInt(valueBound));
        when(stats.xaRecoveryNothingCount()).thenReturn((long) random.nextInt(valueBound));
        when(CACHE.getStatistics()).thenReturn(stats);
    }

    @AfterClass
    public static void cleanup() {
        CACHE_MANAGER.removeAllCaches();
        if (CACHE_MANAGER != null)
            CACHE_MANAGER.shutdown();
    }
}
