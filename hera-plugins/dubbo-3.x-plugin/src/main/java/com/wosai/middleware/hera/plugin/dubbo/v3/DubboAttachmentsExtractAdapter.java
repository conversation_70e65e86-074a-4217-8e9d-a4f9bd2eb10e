package com.wosai.middleware.hera.plugin.dubbo.v3;

import com.wosai.middleware.hera.tracing.propagation.TextMapExtract;

import java.util.Iterator;
import java.util.Map;

public class DubboAttachmentsExtractAdapter implements TextMapExtract {

    private final Map<String, String> dubboAttachments;

    public DubboAttachmentsExtractAdapter(Map<String, String> dubboAttachments) {
        this.dubboAttachments = dubboAttachments;
    }

    @Override
    public Iterator<Map.Entry<String, String>> iterator() {
        return this.dubboAttachments.entrySet().iterator();
    }
}
