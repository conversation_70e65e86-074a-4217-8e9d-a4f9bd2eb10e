package com.wosai.middleware.hera.plugin.feign.http.v9;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import com.wosai.middleware.hera.util.ExtraTags;
import feign.Request;
import feign.Response;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.feign.http.v9.FeignPluginConfig;
import org.apache.skywalking.apm.plugin.feign.http.v9.FeignResolvedURL;
import org.apache.skywalking.apm.util.StringUtil;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static feign.Util.valuesOrEmpty;

public class DefaultHttpClientInterceptor implements InstanceMethodsAroundInterceptor {
    private static final String CONTENT_TYPE_HEADER = "Content-Type";

    private static Field FIELD_HEADERS_OF_REQUEST;

    static {
        try {
            final Field field = Request.class.getDeclaredField("headers");
            field.setAccessible(true);
            FIELD_HEADERS_OF_REQUEST = field;
        } catch (Exception ignore) {
            FIELD_HEADERS_OF_REQUEST = null;
        }
    }

    /**
     * Get the {@link feign.Request} from {@link EnhancedInstance}, then create {@link AbstractHeraSpan} and set host, port,
     * kind, component, url from {@link feign.Request}. Through the reflection of the way, set the http header of
     * context data into {@link feign.Request#headers}.
     *
     * @param method intercept method
     * @param result change this result, if you want to truncate the method.
     * @throws Throwable NoSuchFieldException or IllegalArgumentException
     */
    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] allArguments, Class<?>[] classes, MethodInterceptResult methodInterceptResult) throws Throwable {
        Request request = (Request) allArguments[0];
        URL url = new URL(request.url());
        int port = url.getPort() == -1 ? 80 : url.getPort();
        String remotePeer = url.getHost() + ":" + port;
        String operationName = url.getPath();

        FeignResolvedURL feignResolvedURL = PathVarInterceptor.URL_CONTEXT.get();
        if (feignResolvedURL != null) {
            try {
                operationName = operationName.replace(feignResolvedURL.getUrl(), feignResolvedURL.getOriginUrl());
            } finally {
                PathVarInterceptor.URL_CONTEXT.remove();
            }
        }
        if (operationName.length() == 0) {
            operationName = "/";
        }

        AbstractHeraSpan span = ContextManager.createExitSpan(operationName, remotePeer);
        Tags.COMPONENT.set(span, ComponentsDefine.FEIGN.getName());
        Tags.HTTP_METHOD.set(span, request.method());
        Tags.HTTP_URL.set(span, request.url());

        if (FeignPluginConfig.Plugin.Feign.COLLECT_REQUEST_BODY) {
            boolean needCollectHttpBody = false;
            Iterator<String> stringIterator = valuesOrEmpty(request.headers(), CONTENT_TYPE_HEADER).iterator();
            String contentTypeHeaderValue = stringIterator.hasNext() ? stringIterator.next() : "";
            for (String contentType : FeignPluginConfig.Plugin.Feign.SUPPORTED_CONTENT_TYPES_PREFIX.split(",")) {
                if (contentTypeHeaderValue.startsWith(contentType)) {
                    needCollectHttpBody = true;
                    break;
                }
            }
            if (needCollectHttpBody) {
                collectHttpBody(request, span);
            }
        }

        if (FIELD_HEADERS_OF_REQUEST != null) {
            Map<String, Collection<String>> headers = new LinkedHashMap<>();
            ContextManager.inject(span, (key, value) -> {
                List<String> contextCollection = new ArrayList<>(1);
                contextCollection.add(value);
                headers.put(key, contextCollection);
            });
            headers.putAll(request.headers());

            FIELD_HEADERS_OF_REQUEST.set(request, Collections.unmodifiableMap(headers));
        }
    }

    private void collectHttpBody(final Request request, final AbstractHeraSpan span) {
        if (request.body() == null || request.charset() == null) {
            return;
        }
        String tagValue = new String(request.body(), request.charset());
        tagValue = FeignPluginConfig.Plugin.Feign.FILTER_LENGTH_LIMIT > 0 ?
                StringUtil.cut(tagValue, FeignPluginConfig.Plugin.Feign.FILTER_LENGTH_LIMIT) : tagValue;

        span.setTag(ExtraTags.HTTP_BODY, tagValue);
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Object ret) throws Throwable {
        Response response = (Response) ret;
        if (response != null) {
            int statusCode = response.status();

            AbstractHeraSpan span = ContextManager.activeSpan();
            Tags.HTTP_STATUS.set(span, statusCode);
            if (statusCode >= 400) {
                ContextManager.markError(span);
            }
        }

        ContextManager.stopSpan();

        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable t) {
        ContextManager.logError(t);
    }
}
