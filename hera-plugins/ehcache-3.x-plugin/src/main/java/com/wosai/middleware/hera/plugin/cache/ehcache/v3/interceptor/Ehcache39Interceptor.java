package com.wosai.middleware.hera.plugin.cache.ehcache.v3.interceptor;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.plugin.cache.ehcache.v3.EhcacheStatFactory;
import com.wosai.middleware.hera.plugin.cache.ehcache.v3.metrics.EhCache3Metrics;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.ehcache.Status;
import org.ehcache.core.Ehcache;

import java.lang.reflect.Method;

/*
    private <K, V> Cache<K, V> createCache(String alias, CacheConfiguration<K, V> originalConfig, boolean addToConfig)
*/
public class Ehcache39Interceptor implements InstanceMethodsAroundInterceptor {
    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentTypes, MethodInterceptResult result) throws Throwable {
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentTypes, Object ret) throws Throwable {
        String cacheName = (String) allArguments[0];
        Ehcache cache = (Ehcache) ret;
        if (cache != null && cache.getStatus().equals(Status.AVAILABLE)) {
            MetricsHandler.bind(new EhCache3Metrics(cache, cacheName, EhcacheStatFactory.Version.V39));
        }
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {
    }
}