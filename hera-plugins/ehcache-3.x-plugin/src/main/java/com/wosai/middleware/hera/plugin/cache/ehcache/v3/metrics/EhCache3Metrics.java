package com.wosai.middleware.hera.plugin.cache.ehcache.v3.metrics;

import com.wosai.middleware.hera.agent.metrics.BaseUnits;
import com.wosai.middleware.hera.agent.metrics.CacheMeterBinder;
import com.wosai.middleware.hera.agent.metrics.api.FunctionCounter;
import com.wosai.middleware.hera.agent.metrics.api.Gauge;
import com.wosai.middleware.hera.plugin.cache.ehcache.v3.EhcacheStatFactory;
import org.ehcache.core.Ehcache;
import org.ehcache.core.statistics.CacheStatistics;
import org.ehcache.core.statistics.TierStatistics;

public class EhCache3Metrics extends CacheMeterBinder<Ehcache<?, ?>> {
    private final CacheStatistics stats;

    public EhCache3Metrics(Ehcache cache, String cacheName, EhcacheStatFactory.Version version) {
        super(cache, cacheName);
        this.stats = version.createStatsInstance(cache);
    }

    @Override
    protected Long size() {
        Long size = 0L;
        for (String key : new String[]{"OnHeap", "OffHeap", "Disk"}) {
            TierStatistics tierStatistics = stats.getTierStatistics().get(key);
            size += tierStatistics != null ? tierStatistics.getMappings() : 0;
        }
        return size;
    }

    @Override
    protected long hitCount() {
        return stats.getCacheHits();
    }

    @Override
    protected Long missCount() {
        return stats.getCacheMisses();
    }

    @Override
    protected Long evictionCount() {
        return stats.getCacheEvictions();
    }

    @Override
    protected long putCount() {
        return stats.getCachePuts();
    }

    @Override
    protected void bindImplementationSpecificMetrics() {
        Gauge.builder("ehcache.cache.remoteSize", stats,
                        c -> {
                            TierStatistics tierStatistics = stats.getTierStatistics().get("Clustered");
                            return tierStatistics != null ? tierStatistics.getMappings() : 0;
                        })
                .tags(getTagsWithCacheName())
                .description("The number of entries held remotely in this cache")
                .build();

        FunctionCounter.builder("ehcache.cache.removals", stats, CacheStatistics::getCacheRemovals)
                .tags(getTagsWithCacheName())
                .description("Cache removals")
                .build();

        Gauge.builder("ehcache.cache.hits.percentage", stats, CacheStatistics::getCacheHitPercentage)
                .tags(getTagsWithCacheName())
                .tag("result", "hitted")
                .description("The percentage of cache lookup methods returned a value")
                .build();

        Gauge.builder("ehcache.cache.misses.percentage", stats, CacheStatistics::getCacheMissPercentage)
                .tags(getTagsWithCacheName())
                .tag("result", "missed")
                .description("The percentage of cache lookup methods have not returned a value")
                .build();

        Gauge.builder("ehcache.cache.local.heap.size", stats,
                        c -> {
                            TierStatistics tierStatistics = stats.getTierStatistics().get("OnHeap");
                            Long size = tierStatistics != null ? tierStatistics.getOccupiedByteSize() : 0;
                            return size != -1 ? size : 0;
                        })
                .tags(getTagsWithCacheName())
                .description("Local heap size")
                .baseUnit(BaseUnits.BYTES)
                .build();

        Gauge.builder("ehcache.cache.local.offheap.size", stats,
                        c -> {
                            TierStatistics tierStatistics = stats.getTierStatistics().get("OffHeap");
                            Long size = tierStatistics != null ? tierStatistics.getOccupiedByteSize() : 0;
                            return size != -1 ? size : 0;
                        })
                .tags(getTagsWithCacheName())
                .description("Local off-heap size")
                .baseUnit(BaseUnits.BYTES)
                .build();

        Gauge.builder("ehcache.cache.local.disk.size", stats,
                        c -> {
                            TierStatistics tierStatistics = stats.getTierStatistics().get("Disk");
                            Long size = tierStatistics != null ? tierStatistics.getOccupiedByteSize() : 0;
                            return size != -1 ? size : 0;
                        })
                .tags(getTagsWithCacheName())
                .description("Local disk size")
                .baseUnit(BaseUnits.BYTES)
                .build();
    }
}
