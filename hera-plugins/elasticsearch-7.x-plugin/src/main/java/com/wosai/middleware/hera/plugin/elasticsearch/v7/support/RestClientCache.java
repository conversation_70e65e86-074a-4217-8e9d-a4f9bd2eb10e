package com.wosai.middleware.hera.plugin.elasticsearch.v7.support;

import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import org.apache.skywalking.apm.plugin.elasticsearch.common.RestClientEnhanceInfo;
import org.elasticsearch.action.ActionListener;

public class RestClientCache<T> {
    private RestClientEnhanceInfo enhanceInfo;
    private ActionListener<T> actionListener;
    private String operationName;
    private AbstractHeraSpan heraSpan;

    public RestClientEnhanceInfo getEnhanceInfo() {
        return enhanceInfo;
    }

    public void setEnhanceInfo(RestClientEnhanceInfo enhanceInfo) {
        this.enhanceInfo = enhanceInfo;
    }

    public ActionListener<T> getActionListener() {
        return actionListener;
    }

    public void setActionListener(ActionListener<T> actionListener) {
        this.actionListener = actionListener;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public AbstractHeraSpan getHeraSpan() {
        return heraSpan;
    }

    public void setHeraSpan(AbstractHeraSpan heraSpan) {
        this.heraSpan = heraSpan;
    }
}
