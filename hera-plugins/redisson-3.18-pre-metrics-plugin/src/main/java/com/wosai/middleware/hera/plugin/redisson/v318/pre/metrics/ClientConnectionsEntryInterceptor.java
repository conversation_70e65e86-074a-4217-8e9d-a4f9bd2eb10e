package com.wosai.middleware.hera.plugin.redisson.v318.pre.metrics;

import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;
import org.apache.skywalking.apm.plugin.redisson.v3.util.ClassUtil;
import org.redisson.client.RedisClient;
import org.redisson.client.RedisClientConfig;
import org.redisson.connection.ClientConnectionsEntry;

import java.net.URI;
import java.net.InetSocketAddress;

public class ClientConnectionsEntryInterceptor implements InstanceConstructorInterceptor {

    private static final ILog LOGGER = LogManager.getLogger(ClientConnectionsEntryInterceptor.class);

    @Override
    public void onConstruct(EnhancedInstance enhancedInstance, Object[] objects) throws Throwable {
        ClientConnectionsEntry clientConnectionsEntry = (ClientConnectionsEntry) enhancedInstance;
        String redisUri;
        RedisClient redisClient = clientConnectionsEntry.getClient();
        // RedisClient has RedisClientConfig after version 3.4.2
        try {
            RedisClientConfig config = (RedisClientConfig) ClassUtil.getObjectField(redisClient, "config");
            // Get the address from RedisClientConfig, compatible with both Uri and RedisUri.
            try {
                redisUri = config.getAddress().getHost() + ":" + config.getAddress().getPort();
            } catch (NoSuchMethodError e) {
                Object uri = ClassUtil.getObjectField(config, "address");
                redisUri = ((URI) uri).getHost() + ":" + ((URI) uri).getPort();
            }
        } catch (NoSuchFieldException e) {
            Object address = ClassUtil.getObjectField(redisClient, "addr");
            redisUri = ((InetSocketAddress) address).getHostName() + ":" + ((InetSocketAddress) address).getPort();
        }

        RedissonConnectionsMetrics metrics = new RedissonConnectionsMetrics(clientConnectionsEntry, redisUri);
        metrics.bindTo();
    }
}
