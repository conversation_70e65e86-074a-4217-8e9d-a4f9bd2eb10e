package com.wosai.middleware.hera.plugin.jetty.v9.server;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.plugin.jetty.v9.server.Constants;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

public class ForwardInterceptor implements InstanceMethodsAroundInterceptor, InstanceConstructorInterceptor {

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Throwable {
        if (ContextManager.isActive()) {
            AbstractHeraSpan abstractTracingSpan = ContextManager.activeSpan();
            Map<String, String> eventMap = new HashMap<>();
            eventMap.put("forward-url", (String) objInst.getSkyWalkingDynamicField());
            abstractTracingSpan.log(eventMap);
            ContextManager.getRuntimeContext().put(Constants.FORWARD_REQUEST_FLAG, true);
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) throws Throwable {
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {

    }

    @Override
    public void onConstruct(EnhancedInstance objInst, Object[] allArguments) {
        objInst.setSkyWalkingDynamicField(allArguments[2]);
    }
}
