package com.wosai.middleware.hera.plugin.jetty.v9.server;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import com.wosai.middleware.hera.tracing.tag.Tags;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.agent.core.util.MethodUtil;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.jetty.v9.server.Constants;
import org.eclipse.jetty.server.HttpChannel;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

public class HandleInterceptor implements InstanceMethodsAroundInterceptor {

    private static boolean IS_SERVLET_GET_STATUS_METHOD_EXIST;
    private static final String SERVLET_RESPONSE_CLASS = "javax.servlet.http.HttpServletResponse";
    private static final String GET_STATUS_METHOD = "getStatus";

    static {
        IS_SERVLET_GET_STATUS_METHOD_EXIST = MethodUtil.isMethodExist(HandleInterceptor.class.getClassLoader(), SERVLET_RESPONSE_CLASS, GET_STATUS_METHOD);
    }

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Throwable {
        HttpChannel httpChannel = (HttpChannel) objInst;
        HttpServletRequest servletRequest = httpChannel.getRequest();

        HeraSpanContext parentSpan = ContextManager.extract(new HttpServletRequestExtractAdapter(servletRequest));
        AbstractHeraSpan span = ContextManager.createEntrySpan(servletRequest.getRequestURI(), parentSpan);
        Tags.HTTP_URL.set(span, servletRequest.getRequestURL().toString());
        Tags.HTTP_METHOD.set(span, servletRequest.getMethod());
        Tags.COMPONENT.set(span, ComponentsDefine.JETTY_SERVER.getName());
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) throws Throwable {
        HttpChannel httpChannel = (HttpChannel) objInst;
        HttpServletResponse servletResponse = httpChannel.getResponse();
        AbstractHeraSpan span = ContextManager.activeSpan();
        if (IS_SERVLET_GET_STATUS_METHOD_EXIST) {
            Tags.HTTP_STATUS.set(span, servletResponse.getStatus());
            if (servletResponse.getStatus() >= 400) {
                ContextManager.markError(span);
            }
        }
        ContextManager.getRuntimeContext().remove(Constants.FORWARD_REQUEST_FLAG);
        ContextManager.stopSpan();
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {
        ContextManager.logError(t);
    }
}
