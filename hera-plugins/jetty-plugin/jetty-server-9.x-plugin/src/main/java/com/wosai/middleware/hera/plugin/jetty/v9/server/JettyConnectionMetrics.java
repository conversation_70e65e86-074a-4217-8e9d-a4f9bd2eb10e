/**
 * Copyright 2019 VMware, Inc.
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * https://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.wosai.middleware.hera.plugin.jetty.v9.server;

import com.wosai.middleware.hera.agent.metrics.api.Counter;
import com.wosai.middleware.hera.agent.metrics.api.DistributionSummary;
import com.wosai.middleware.hera.agent.metrics.api.Gauge;
import com.wosai.middleware.hera.agent.metrics.api.MeterFactory;
import com.wosai.middleware.hera.agent.metrics.api.TimeWindowMaxHolder;
import com.wosai.middleware.hera.agent.metrics.api.Timer;
import org.eclipse.jetty.io.Connection;
import org.eclipse.jetty.server.Connector;
import org.eclipse.jetty.server.HttpConnection;
import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.util.component.AbstractLifeCycle;

import java.util.HashMap;
import java.util.Map;

/**
 * Jetty connection metrics.<br><br>
 * <p>
 * Usage example:
 *
 * <pre>{@code
 * MeterRegistry registry = ...;
 * Server server = new Server(0);
 * Connector connector = new ServerConnector(server);
 * connector.addBean(new JettyConnectionMetrics(registry));
 * server.setConnectors(new Connector[] { connector });
 * }</pre>
 * <p>
 * Alternatively, configure on all connectors with {@link JettyConnectionMetrics#addToAllConnectors(Server)}.
 *
 * <AUTHOR> Schneider
 * @since 1.4.0
 */
public class JettyConnectionMetrics extends AbstractLifeCycle implements Connection.Listener {
    private final Object connectionSamplesLock = new Object();
    private final Map<Connection, Timer.Sample> connectionSamples = new HashMap<>();

    private final Counter messagesIn;
    private final Counter messagesOut;
    private final DistributionSummary bytesIn;
    private final DistributionSummary bytesOut;

    private final TimeWindowMaxHolder maxConnections;

    public JettyConnectionMetrics() {
        this.messagesIn = Counter.builder("jetty.connections.messages.in")
                .description("Messages received by tracked connections")
                .build();
        this.messagesOut = Counter.builder("jetty.connections.messages.out")
                .description("Messages sent by tracked connections")
                .build();

        this.bytesIn = DistributionSummary.builder("jetty.connections.bytes.in")
                .description("Bytes received by tracked connections")
                .build();
        this.bytesOut = DistributionSummary.builder("jetty.connections.bytes.out")
                .description("Bytes sent by tracked connections")
                .build();

        this.maxConnections = MeterFactory.defaultTimeWindowMax();

        Gauge.builder("jetty.connections.max", this, jcm -> jcm.maxConnections.poll())
                .strongReference(true)
                .description("The maximum number of observed connections over a rolling 2-minute interval")
                .build();
        Gauge.builder("jetty.connections.current", this, jcm -> jcm.connectionSamples.size())
                .strongReference(true)
                .description("The current number of open Jetty connections")
                .build();
    }

    @Override
    public void onOpened(Connection connection) {
        Timer.Sample started = Timer.start();
        synchronized (connectionSamplesLock) {
            connectionSamples.put(connection, started);
            maxConnections.record(connectionSamples.size());
        }
    }

    @Override
    public void onClosed(Connection connection) {
        Timer.Sample timerSample;
        synchronized (connectionSamplesLock) {
            timerSample = connectionSamples.remove(connection);
        }

        if (timerSample != null) {
            String serverOrClient = connection instanceof HttpConnection ? "server" : "client";
            timerSample.stop(Timer.builder("jetty.connections.request")
                    .description("Jetty client or server requests")
                    .tag("type", serverOrClient)
                    .build());
        }

        messagesIn.increment();
        messagesOut.increment();

        bytesIn.record(connection.getBytesIn());
        bytesOut.record(connection.getBytesOut());
    }

    public static void addToAllConnectors(Server server) {
        for (Connector connector : server.getConnectors()) {
            if (connector != null) {
                connector.addBean(new JettyConnectionMetrics());
            }
        }
    }
}
