package com.wosai.middleware.hera.plugin.jetty.v9.server;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceConstructorInterceptor;
import org.eclipse.jetty.server.Server;

public class JettyServerThreadPoolMetricsInterceptor implements InstanceConstructorInterceptor {
    @Override
    public void onConstruct(EnhancedInstance enhancedInstance, Object[] objects) throws Throwable {
        Server context = (Server) enhancedInstance;
        if (context != null && context.getThreadPool() != null) {
            MetricsHandler.bind(new JettyServerThreadPoolMetrics(context.getThreadPool()));
        }
    }
}
