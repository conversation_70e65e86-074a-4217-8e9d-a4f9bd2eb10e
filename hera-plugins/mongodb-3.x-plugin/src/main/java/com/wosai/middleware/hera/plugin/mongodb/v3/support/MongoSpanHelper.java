/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.wosai.middleware.hera.plugin.mongodb.v3.support;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import com.wosai.middleware.hera.util.ExtraTags;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.mongodb.v3.MongoPluginConfig;
import org.apache.skywalking.apm.plugin.mongodb.v3.support.MongoConstants;
import org.apache.skywalking.apm.plugin.mongodb.v3.support.MongoOperationHelper;

public class MongoSpanHelper {

    private MongoSpanHelper() {
    }

    public static void createExitSpan(String executeMethod, String remotePeer, Object operation) {

        AbstractHeraSpan span = ContextManager.createExitSpan(
                MongoConstants.MONGO_DB_OP_PREFIX + executeMethod, remotePeer);

        Tags.COMPONENT.set(span, ComponentsDefine.MONGO_DRIVER.getName());
        Tags.DB_TYPE.set(span, MongoConstants.DB_TYPE);

        if (MongoPluginConfig.Plugin.MongoDB.TRACE_PARAM) {
            //original db_statement tag was changed to db_bind_vars and executemethod name was removed, to provide conciser info
            // See at: https://github.com/apache/skywalking/pull/5524
            span.setTag(ExtraTags.DB_BIND_VARIABLES, MongoOperationHelper.getTraceParam(operation));
        }
    }
}
