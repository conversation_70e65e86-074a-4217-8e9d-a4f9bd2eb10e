package com.wosai.middleware.hera.plugin.cache.caffeine.v2.metrics;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.RemovalCause;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.github.benmanes.caffeine.cache.stats.StatsCounter;
import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.Counter;
import com.wosai.middleware.hera.agent.metrics.api.DistributionSummary;
import com.wosai.middleware.hera.agent.metrics.api.Gauge;
import com.wosai.middleware.hera.agent.metrics.api.Tags;
import com.wosai.middleware.hera.agent.metrics.api.Timer;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;

import java.lang.reflect.Constructor;
import java.util.Arrays;
import java.util.Collections;
import java.util.EnumMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static java.util.Objects.requireNonNull;

public class CaffeineStatsCounter implements StatsCounter {
    private final Tags tags;
    private final Counter hitCount;
    private final Counter missCount;
    private final Timer loadSuccesses;
    private final Timer loadFailures;
    private final EnumMap<RemovalCause, DistributionSummary> evictionMetrics;
    private final StatsCounter delegation;

    /**
     * Constructs an instance for use by a single cache.
     *
     * @param delegation the underlying {@link StatsCounter} if registered.
     * @param cacheName  will be used to tag metrics with "cache".
     */
    public CaffeineStatsCounter(StatsCounter delegation, String cacheName) {
        requireNonNull(cacheName);
        this.delegation = delegation;
        this.tags = Tags.of("cache", cacheName);

        // first remove previously registered meters
        MetricsHandler.removeMeterNamesIfPresent(getMeterNameList(), "cache", value -> value.equals(cacheName));

        hitCount = Counter.builder("caffeine.cache.gets").tags(tags).tag("result", "hit")
                .description("The number of times cache lookup methods have returned a cached value.")
                .build();

        missCount = Counter.builder("caffeine.cache.gets").tags(tags).tag("result", "miss")
                .description("The number of times cache lookup methods have returned an uncached (newly loaded) value.")
                .build();

        loadSuccesses = Timer.builder("caffeine.cache.loads").tags(tags).tag("result", "success")
                .description("Caffeine cache loads.").build();

        loadFailures = Timer.builder("caffeine.cache.loads").tags(tags).tag("result", "failure")
                .description("Caffeine cache loads.").build();

        evictionMetrics = new EnumMap<>(RemovalCause.class);
        Arrays.stream(RemovalCause.values())
                .forEach(cause -> evictionMetrics.put(cause,
                        DistributionSummary.builder("caffeine.cache.evictions").tag("cause", cause.name()).tags(tags)
                                .description("Entries evicted from cache.").build()));
    }

    protected List<String> getMeterNameList() {
        return Arrays.asList("caffeine.cache.gets", "caffeine.cache.loads", "caffeine.cache.evictions");
    }

    /**
     * Register a gauge for the size of the given cache.
     *
     * @param cache cache to register a gauge for its size
     */
    public static void registerSizeMetric(Cache<?, ?> cache, String name) {
        // first remove previously registered meters
        MetricsHandler.removeMeterNamesIfPresent(Collections.singletonList("caffeine.cache.size"),
                "cache", value -> value.equals(name));
        Gauge.builder("caffeine.cache.size", cache, Cache::estimatedSize).tag("cache", name)
                .description("The approximate number of entries in this cache.").build();
    }

    @Override
    public void recordHits(int count) {
        if (delegation != null) {
            delegation.recordHits(count);
        }
        hitCount.increment(count);
    }

    @Override
    public void recordMisses(int count) {
        if (delegation != null) {
            delegation.recordMisses(count);
        }
        missCount.increment(count);
    }

    @Override
    public void recordLoadSuccess(long loadTime) {
        if (delegation != null) {
            delegation.recordLoadSuccess(loadTime);
        }
        loadSuccesses.record(loadTime, TimeUnit.NANOSECONDS);
    }

    @Override
    public void recordLoadFailure(long loadTime) {
        if (delegation != null) {
            delegation.recordLoadFailure(loadTime);
        }
        loadFailures.record(loadTime, TimeUnit.NANOSECONDS);
    }

    @SuppressWarnings("deprecation")
    public void recordEviction() {
        if (delegation != null) {
            delegation.recordEviction();
        }
    }

    @Override
    public void recordEviction(int weight, RemovalCause cause) {
        if (delegation != null) {
            delegation.recordEviction(weight, cause);
        }
        evictionMetrics.get(cause).record(weight);
    }

    @Override
    public CacheStats snapshot() {
//        StatsCounter statCounter;
//        if ((statCounter = this.weakKey.get()) != null) {
//            return statCounter.snapshot();
//        }
        return createCacheStats(
                (long) hitCount.count(),
                (long) missCount.count(),
                loadSuccesses.count(),
                loadFailures.count(),
                (long) loadSuccesses.totalTime(TimeUnit.NANOSECONDS)
                        + (long) loadFailures.totalTime(TimeUnit.NANOSECONDS),
                evictionMetrics.values().stream().mapToLong(DistributionSummary::count).sum(),
                (long) evictionMetrics.values().stream().mapToDouble(DistributionSummary::totalAmount).sum()
        );
    }

    static CacheStats createCacheStats(long hitCount, long missCount, long loadSuccessCount, long loadFailureCount,
                                       long totalLoadTime, long evictionCount, long evictionWeight) {
        try {
            // try constructor with 7 longs
            Constructor<CacheStats> ctor = CacheStats.class.getConstructor(long.class, long.class, long.class, long.class, long.class, long.class, long.class);
            ctor.setAccessible(true);
            return ctor.newInstance(hitCount, missCount, loadSuccessCount, loadFailureCount, totalLoadTime, evictionCount, evictionWeight);
        } catch (Exception ex) {
            LogManager.getLogger(CaffeineStatsCounter.class)
                    .warn(ex, "fail to create an instance of CacheStats with 7 longs, downgrade to 6 longs");
            try {
                // downgrade: try 6 longs
                Constructor<CacheStats> ctor = CacheStats.class.getConstructor(long.class, long.class, long.class, long.class, long.class, long.class);
                ctor.setAccessible(true);
                return ctor.newInstance(hitCount, missCount, loadSuccessCount, loadFailureCount, totalLoadTime, evictionCount);
            } catch (Exception innerEx) {
                LogManager.getLogger(CaffeineStatsCounter.class)
                        .error(innerEx, "fail to create an instance of CacheStats with 6 longs. You may using incompatible version of Caffeine", ex);
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return snapshot().toString();
    }
}