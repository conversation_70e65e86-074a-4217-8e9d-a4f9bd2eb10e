package com.wosai.middleware.hera.plugin.cache.caffeine.v2.interceptor;

import com.github.benmanes.caffeine.cache.stats.StatsCounter;
import com.wosai.middleware.hera.plugin.cache.caffeine.v2.metrics.CaffeineStatsCounter;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.InstanceMethodsAroundInterceptorV2;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;

import java.lang.reflect.Method;
import java.util.function.Supplier;

public class CaffeineGetStatsInterceptor implements InstanceMethodsAroundInterceptorV2 {
    @Override
    public void beforeMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, MethodInvocationContext methodInvocationContext) throws Throwable {
    }

    @Override
    public Object afterMethod(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Object ret, MethodInvocationContext methodInvocationContext) throws Throwable {
        final String cacheName = SpringCaffeineCacheManagerInterceptor.CACHE_NAME.get();
        if (!(ret instanceof Supplier)) {
            return ret;
        }
        Supplier<StatsCounter> statsCounterSupplier = (Supplier<StatsCounter>) ret;
        return (Supplier<StatsCounter>) () -> new CaffeineStatsCounter(statsCounterSupplier.get(), cacheName);
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable, MethodInvocationContext methodInvocationContext) {

    }
}
