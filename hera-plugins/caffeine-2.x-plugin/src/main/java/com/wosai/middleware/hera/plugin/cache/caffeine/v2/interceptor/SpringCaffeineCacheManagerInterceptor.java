package com.wosai.middleware.hera.plugin.cache.caffeine.v2.interceptor;

import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;

import java.lang.reflect.Method;

public class SpringCaffeineCacheManagerInterceptor implements InstanceMethodsAroundInterceptor {
    /**
     * A temporary storage for saving cache name to be assigned.
     */
    static final ThreadLocal<String> CACHE_NAME = new ThreadLocal<>();

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentTypes, MethodInterceptResult result) throws Throwable {
        String name = (String) allArguments[0];
        if (name != null) {
            CACHE_NAME.set(name);
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentTypes, Object ret) throws Throwable {
        CACHE_NAME.remove();
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance enhancedInstance, Method method, Object[] objects, Class<?>[] classes, Throwable throwable) {

    }
}
