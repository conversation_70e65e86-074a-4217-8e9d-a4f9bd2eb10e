package com.wosai.middleware.hera.plugin.cache.caffeine.v2.metrics;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.Timer;
import com.wosai.middleware.hera.test.HeraInMemoryMetrics;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;

public class CaffeineStatsCounterTest {

    private static final String CACHE_NAME = "foo";

    private CaffeineStatsCounter stats;

    @Rule
    public HeraInMemoryMetrics heraInMemoryMetrics = new HeraInMemoryMetrics();

    @Before
    public void setUp() {
        stats = new CaffeineStatsCounter(null, CACHE_NAME);
    }

    @Test
    public void registerSize() {
        Cache<String, String> cache = Caffeine.newBuilder().maximumSize(10).recordStats(() -> stats).build();
        CaffeineStatsCounter.registerSizeMetric(cache, CACHE_NAME);
        assertThat(MetricsHandler.find("caffeine.cache.size").gauge().value()).isEqualTo(0);
        cache.put("foo", "bar");
        assertThat(MetricsHandler.find("caffeine.cache.size").gauge().value()).isEqualTo(1);
    }

    @Test
    public void hit() {
        stats.recordHits(2);
        assertThat(MetricsHandler.find("caffeine.cache.gets").tag("result", "hit").counter().count()).isEqualTo(2);
    }

    @Test
    public void miss() {
        stats.recordMisses(2);
        assertThat(MetricsHandler.find("caffeine.cache.gets").tag("result", "miss").counter().count()).isEqualTo(2);
    }

    @Test
    public void loadSuccess() {
        stats.recordLoadSuccess(256);
        Timer timer = MetricsHandler.find("caffeine.cache.loads").tag("result", "success").timer();
        assertThat(timer.count()).isEqualTo(1);
        assertThat(timer.totalTime(TimeUnit.NANOSECONDS)).isEqualTo(256);
    }

    @Test
    public void loadFailure() {
        stats.recordLoadFailure(256);
        Timer timer = MetricsHandler.find("caffeine.cache.loads").tag("result", "failure").timer();
        assertThat(timer.count()).isEqualTo(1);
        assertThat(timer.totalTime(TimeUnit.NANOSECONDS)).isEqualTo(256);
    }

    @Test
    public void name() {
        assertThat(MetricsHandler.getMeters()).allSatisfy(m ->
                assertThat(m.getId().getName()).startsWith("caffeine.cache."));
    }
}
