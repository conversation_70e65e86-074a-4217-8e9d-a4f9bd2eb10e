package com.wosai.middleware.hera.plugin.cache.caffeine.v2.metrics;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.StatsCounter;
import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.test.HeraInMemoryMetrics;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCacheManager;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

public class CaffeineCacheManagerTest {
    private static final String CACHE_NAME = "my_cache";
    private CaffeineCacheManager manager;
    private StatsCounter delegation;

    @Rule
    public HeraInMemoryMetrics heraInMemoryMetrics = new HeraInMemoryMetrics();

    @Before
    public void setup() {
        this.manager = new CaffeineCacheManager();
        delegation = mock(StatsCounter.class);
    }

    @Test
    public void test() {
        // @Bean: user code for Caffeine<Object, Object>
        Caffeine<Object, Object> caffeine = Caffeine.newBuilder().expireAfterWrite(60, TimeUnit.MINUTES)
                // Agent code: register stats counter
                .recordStats(() -> new CaffeineStatsCounter(delegation, CACHE_NAME));
        // @Bean: user code for CaffeineCacheManager
        manager.setCaffeine(caffeine);
        Cache c = manager.getCache(CACHE_NAME);
        c.get("unknown-key");
        verify(delegation, times(1)).recordMisses(1);
        c.put("known-key", "value");
        c.get("known-key");
        verify(delegation, times(1)).recordHits(1);
        CaffeineStatsCounter.registerSizeMetric((com.github.benmanes.caffeine.cache.Cache<?, ?>) c.getNativeCache(), CACHE_NAME);
        assertThat(MetricsHandler.getMeters()).hasSizeGreaterThan(1);
        assertThat(MetricsHandler.find("caffeine.cache.size")
                .tag("cache", CACHE_NAME).gauge())
                .isNotNull();
    }
}
