package com.wosai.middleware.hera.plugin.cache.caffeine.v2.metrics;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.Gauge;
import com.wosai.middleware.hera.test.HeraInMemoryMetrics;
import org.junit.Rule;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class CaffeineGCTest {
    private static final String CACHE_NAME = "my_cache";

    @Rule
    public HeraInMemoryMetrics heraInMemoryMetrics = new HeraInMemoryMetrics();

    @Test
    public void initiativeGC() {
        Cache<String, String> cache = Caffeine.newBuilder()
                .maximumSize(10_000)
                .build();
        cache.put("k", "v");
        CaffeineStatsCounter.registerSizeMetric(cache, CACHE_NAME);
        assertThat(MetricsHandler.getMeters()).hasSize(1);
        assertThat(MetricsHandler.find("caffeine.cache.size").tags("cache", CACHE_NAME).gauge())
                .isNotNull()
                .extracting(Gauge::value).isNotEqualTo(1);
        // release reference and call GC
        cache = null;
        System.gc();
        assertThat(MetricsHandler.find("caffeine.cache.size")
                .tags("cache", CACHE_NAME).gauge())
                .isNotNull().extracting(Gauge::value).isEqualTo(Double.NaN);
    }
}
