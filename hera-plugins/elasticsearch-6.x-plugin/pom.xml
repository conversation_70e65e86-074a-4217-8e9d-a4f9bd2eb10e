<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hera-plugins</artifactId>
        <groupId>com.wosai.middleware</groupId>
        <version>1.14.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hera-elasticsearch-6.x-plugin</artifactId>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sdk.plugin.related.dir>/..</sdk.plugin.related.dir>
        <elasticsearch.rest.high.level.client.version>6.7.1</elasticsearch.rest.high.level.client.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-elasticsearch-common</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-elasticsearch-6.x-plugin</artifactId>
            <version>${apache.skywalking.version}</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>${elasticsearch.rest.high.level.client.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>