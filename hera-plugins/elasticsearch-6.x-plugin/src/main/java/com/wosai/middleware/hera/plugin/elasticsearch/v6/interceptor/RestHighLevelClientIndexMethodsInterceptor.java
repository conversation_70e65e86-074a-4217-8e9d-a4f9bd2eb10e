package com.wosai.middleware.hera.plugin.elasticsearch.v6.interceptor;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.plugin.elasticsearch.v6.support.AdapterUtil;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.elasticsearch.common.RestClientEnhanceInfo;
import org.apache.skywalking.apm.plugin.elasticsearch.v6.interceptor.Constants;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.index.IndexRequest;

import java.lang.reflect.Method;

import static org.apache.skywalking.apm.plugin.elasticsearch.v6.ElasticsearchPluginConfig.Plugin.Elasticsearch.TRACE_DSL;
import static org.apache.skywalking.apm.plugin.elasticsearch.v6.interceptor.Constants.DB_TYPE;

public class RestHighLevelClientIndexMethodsInterceptor implements InstanceMethodsAroundInterceptor {

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Throwable {
        IndexRequest indexRequest = (IndexRequest) (allArguments[0]);

        RestClientEnhanceInfo restClientEnhanceInfo = (RestClientEnhanceInfo) objInst.getSkyWalkingDynamicField();
        AbstractHeraSpan span = ContextManager.createExitSpan(Constants.INDEX_OPERATOR_NAME, restClientEnhanceInfo.getPeers());
        Tags.COMPONENT.set(span, ComponentsDefine.REST_HIGH_LEVEL_CLIENT.getName());

        Tags.DB_TYPE.set(span, DB_TYPE);
        Tags.DB_INSTANCE.set(span, indexRequest.index());
        if (TRACE_DSL) {
            Tags.DB_STATEMENT.set(span, indexRequest.toString());
        }
        if (allArguments.length > 2 && allArguments[2] != null && allArguments[2] instanceof ActionListener) {
            allArguments[2] = AdapterUtil.wrapActionListener(restClientEnhanceInfo, Constants.ANALYZE_OPERATOR_NAME, (ActionListener) allArguments[2]);
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) throws Throwable {
        ContextManager.stopSpan();
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {
        ContextManager.logError(t);
    }
}
