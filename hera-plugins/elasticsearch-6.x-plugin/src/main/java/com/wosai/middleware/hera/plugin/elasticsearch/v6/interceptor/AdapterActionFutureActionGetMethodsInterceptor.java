package com.wosai.middleware.hera.plugin.elasticsearch.v6.interceptor;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.elasticsearch.v6.interceptor.Constants;
import org.apache.skywalking.apm.util.StringUtil;
import org.elasticsearch.action.ActionResponse;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateResponse;

import java.lang.reflect.Method;

import static org.apache.skywalking.apm.plugin.elasticsearch.v6.ElasticsearchPluginConfig.Plugin.Elasticsearch.ELASTICSEARCH_DSL_LENGTH_THRESHOLD;
import static org.apache.skywalking.apm.plugin.elasticsearch.v6.ElasticsearchPluginConfig.Plugin.Elasticsearch.TRACE_DSL;

public class AdapterActionFutureActionGetMethodsInterceptor implements InstanceMethodsAroundInterceptor {

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments,
                             Class<?>[] argumentsTypes, MethodInterceptResult result) throws Throwable {
        if (isTrace(objInst)) {
            AbstractHeraSpan span = ContextManager.createLocalSpan(Constants.DB_TYPE + "/" + Constants.BASE_FUTURE_METHOD);
            Tags.COMPONENT.set(span, ComponentsDefine.TRANSPORT_CLIENT.getName());
            Tags.DB_TYPE.set(span, Constants.DB_TYPE);
        }
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments,
                              Class<?>[] argumentsTypes, Object ret) throws Throwable {

        if (isTrace(objInst)) {
            AbstractHeraSpan span = ContextManager.activeSpan();
            parseResponseInfo((ActionResponse) ret, span);
            ContextManager.stopSpan();
        }
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {
        if (isTrace(objInst)) {
            ContextManager.logError(t);
        }
    }

    private boolean isTrace(EnhancedInstance objInst) {
        return objInst.getSkyWalkingDynamicField() != null && (boolean) objInst.getSkyWalkingDynamicField();
    }

    private void parseResponseInfo(ActionResponse response, AbstractHeraSpan span) {
        // search response
        if (response instanceof SearchResponse) {
            parseSearchResponse((SearchResponse) response, span);
            return;
        }
        // bulk response
        if (response instanceof BulkResponse) {
            parseBulkResponse((BulkResponse) response, span);
            return;
        }
        // get response
        if (response instanceof GetResponse) {
            parseGetResponse((GetResponse) response, span);
            return;
        }
        // index response
        if (response instanceof IndexResponse) {
            parseIndexResponse((IndexResponse) response, span);
            return;
        }
        // update response
        if (response instanceof UpdateResponse) {
            parseUpdateResponse((UpdateResponse) response, span);
            return;
        }
        // delete response
        if (response instanceof DeleteResponse) {
            parseDeleteResponse((DeleteResponse) response, span);
            return;
        }
    }

    private void parseSearchResponse(SearchResponse searchResponse, AbstractHeraSpan span) {
        span.setTag(Constants.ES_TOOK_MILLIS.key(), Long.toString(searchResponse.getTook().getMillis()));
        span.setTag(Constants.ES_TOTAL_HITS.key(), Long.toString(searchResponse.getHits().getTotalHits()));
        if (TRACE_DSL) {
            String tagValue = searchResponse.toString();
            tagValue = ELASTICSEARCH_DSL_LENGTH_THRESHOLD > 0 ? StringUtil.cut(tagValue, ELASTICSEARCH_DSL_LENGTH_THRESHOLD) : tagValue;
            Tags.DB_STATEMENT.set(span, tagValue);
        }
    }

    private void parseBulkResponse(BulkResponse bulkResponse, AbstractHeraSpan span) {
        span.setTag(Constants.ES_TOOK_MILLIS.key(), Long.toString(bulkResponse.getTook().getMillis()));
        span.setTag(Constants.ES_INGEST_TOOK_MILLIS.key(), Long.toString(bulkResponse.getIngestTookInMillis()));
        if (TRACE_DSL) {
            String tagValue = bulkResponse.toString();
            tagValue = ELASTICSEARCH_DSL_LENGTH_THRESHOLD > 0 ? StringUtil.cut(tagValue, ELASTICSEARCH_DSL_LENGTH_THRESHOLD) : tagValue;
            Tags.DB_STATEMENT.set(span, tagValue);
        }
    }

    private void parseGetResponse(GetResponse getResponse, AbstractHeraSpan span) {
        if (TRACE_DSL) {
            String tagValue = getResponse.toString();
            tagValue = ELASTICSEARCH_DSL_LENGTH_THRESHOLD > 0 ? StringUtil.cut(tagValue, ELASTICSEARCH_DSL_LENGTH_THRESHOLD) : tagValue;
            Tags.DB_STATEMENT.set(span, tagValue);
        }
    }

    private void parseIndexResponse(IndexResponse indexResponse, AbstractHeraSpan span) {
        if (TRACE_DSL) {
            String tagValue = indexResponse.toString();
            tagValue = ELASTICSEARCH_DSL_LENGTH_THRESHOLD > 0 ? StringUtil.cut(tagValue, ELASTICSEARCH_DSL_LENGTH_THRESHOLD) : tagValue;
            Tags.DB_STATEMENT.set(span, tagValue);
        }
    }

    private void parseUpdateResponse(UpdateResponse updateResponse, AbstractHeraSpan span) {
        if (TRACE_DSL) {
            String tagValue = updateResponse.toString();
            tagValue = ELASTICSEARCH_DSL_LENGTH_THRESHOLD > 0 ? StringUtil.cut(tagValue, ELASTICSEARCH_DSL_LENGTH_THRESHOLD) : tagValue;
            Tags.DB_STATEMENT.set(span, tagValue);
        }
    }

    private void parseDeleteResponse(DeleteResponse deleteResponse, AbstractHeraSpan span) {
        if (TRACE_DSL) {
            String tagValue = deleteResponse.toString();
            tagValue = ELASTICSEARCH_DSL_LENGTH_THRESHOLD > 0 ? StringUtil.cut(tagValue, ELASTICSEARCH_DSL_LENGTH_THRESHOLD) : tagValue;
            Tags.DB_STATEMENT.set(span, tagValue);
        }
    }
}
