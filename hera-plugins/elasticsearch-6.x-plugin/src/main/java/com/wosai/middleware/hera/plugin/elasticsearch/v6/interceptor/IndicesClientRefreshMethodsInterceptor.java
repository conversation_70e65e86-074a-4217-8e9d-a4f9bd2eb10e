/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package com.wosai.middleware.hera.plugin.elasticsearch.v6.interceptor;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.plugin.elasticsearch.v6.support.AdapterUtil;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.elasticsearch.common.RestClientEnhanceInfo;
import org.apache.skywalking.apm.plugin.elasticsearch.v6.interceptor.Constants;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest;

import java.lang.reflect.Method;

import static org.apache.skywalking.apm.plugin.elasticsearch.v6.interceptor.Constants.DB_TYPE;

public class IndicesClientRefreshMethodsInterceptor implements InstanceMethodsAroundInterceptor {

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Throwable {
        RefreshRequest request = (RefreshRequest) (allArguments[0]);

        RestClientEnhanceInfo restClientEnhanceInfo = (RestClientEnhanceInfo) objInst.getSkyWalkingDynamicField();
        if (restClientEnhanceInfo != null) {
            AbstractHeraSpan span = ContextManager.createExitSpan(Constants.REFRESH_OPERATOR_NAME, restClientEnhanceInfo.getPeers());
            Tags.COMPONENT.set(span, ComponentsDefine.REST_HIGH_LEVEL_CLIENT.getName());

            Tags.DB_TYPE.set(span, DB_TYPE);
            Tags.DB_INSTANCE.set(span, buildIndicesString(request.indices()));
        }
        if (allArguments.length > 2 && allArguments[2] != null && allArguments[2] instanceof ActionListener) {
            allArguments[2] = AdapterUtil.wrapActionListener(restClientEnhanceInfo, Constants.ANALYZE_OPERATOR_NAME, (ActionListener) allArguments[2]);
        }
    }

    private String buildIndicesString(String[] indices) {
        if (indices == null || indices.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        int n = indices.length;
        for (int i = 0; i < n; i++) {
            sb.append(indices[i]);
            if (i < n - 1) {
                sb.append(',');
            }
        }
        return sb.toString();
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) throws Throwable {
        RestClientEnhanceInfo restClientEnhanceInfo = (RestClientEnhanceInfo) (objInst.getSkyWalkingDynamicField());
        if (restClientEnhanceInfo != null) {
            ContextManager.stopSpan();
        }
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments,
                                      Class<?>[] argumentsTypes, Throwable t) {
        RestClientEnhanceInfo restClientEnhanceInfo = (RestClientEnhanceInfo) (objInst.getSkyWalkingDynamicField());
        if (restClientEnhanceInfo != null) {
            ContextManager.activeSpan().error(t);
        }
    }
}
