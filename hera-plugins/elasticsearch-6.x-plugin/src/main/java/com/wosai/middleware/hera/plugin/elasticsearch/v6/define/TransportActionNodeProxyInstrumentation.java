package com.wosai.middleware.hera.plugin.elasticsearch.v6.define;

import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import net.bytebuddy.matcher.ElementMatchers;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.InstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.StaticMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.ClassEnhancePluginDefine;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;
import org.apache.skywalking.apm.plugin.elasticsearch.v6.interceptor.Constants;

import static net.bytebuddy.matcher.ElementMatchers.named;
import static org.apache.skywalking.apm.agent.core.plugin.match.NameMatch.byName;
import static org.apache.skywalking.apm.plugin.elasticsearch.v6.define.TransportActionNodeProxyInstrumentation.CONSTRUCTOR_ARG_CLASS;
import static org.apache.skywalking.apm.plugin.elasticsearch.v6.define.TransportActionNodeProxyInstrumentation.THREE_ARGS_CONSTRUCTOR_INTERCEPTOR_CLASS;
import static org.apache.skywalking.apm.plugin.elasticsearch.v6.define.TransportActionNodeProxyInstrumentation.TWO_ARGS_CONSTRUCTOR2_INTERCEPTOR_CLASS;

public class TransportActionNodeProxyInstrumentation extends ClassEnhancePluginDefine {

    public static final String INTERCEPTOR_CLASS = "com.wosai.middleware.hera.plugin.elasticsearch.v6.interceptor.TransportActionNodeProxyExecuteMethodsInterceptor";
    public static final String ENHANCE_CLASS = "org.elasticsearch.action.TransportActionNodeProxy";

    @Override
    public ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[]{
                new ConstructorInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getConstructorMatcher() {
                        return ElementMatchers.takesArgument(2, named(CONSTRUCTOR_ARG_CLASS));
                    }

                    @Override
                    public String getConstructorInterceptor() {
                        return THREE_ARGS_CONSTRUCTOR_INTERCEPTOR_CLASS;
                    }
                },
                new ConstructorInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getConstructorMatcher() {
                        return ElementMatchers.takesArgument(1, named(CONSTRUCTOR_ARG_CLASS));
                    }

                    @Override
                    public String getConstructorInterceptor() {
                        return TWO_ARGS_CONSTRUCTOR2_INTERCEPTOR_CLASS;
                    }
                }
        };
    }

    @Override
    public InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints() {
        return new InstanceMethodsInterceptPoint[]{
                new InstanceMethodsInterceptPoint() {
                    @Override
                    public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("execute");
                    }

                    @Override
                    public String getMethodsInterceptor() {
                        return INTERCEPTOR_CLASS;
                    }

                    @Override
                    public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }

    @Override
    public StaticMethodsInterceptPoint[] getStaticMethodsInterceptPoints() {
        return new StaticMethodsInterceptPoint[0];
    }

    @Override
    protected ClassMatch enhanceClass() {
        return byName(ENHANCE_CLASS);
    }

    @Override
    protected String[] witnessClasses() {
        return new String[]{Constants.TASK_TRANSPORT_CHANNEL_WITNESS_CLASSES};
    }
}
