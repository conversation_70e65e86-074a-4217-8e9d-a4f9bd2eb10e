package com.wosai.middleware.hera.plugin.lettuce.v5;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import io.lettuce.core.codec.StringCodec;
import io.lettuce.core.protocol.CommandArgs;
import io.lettuce.core.protocol.DecoratedCommand;
import io.lettuce.core.protocol.RedisCommand;
import org.apache.skywalking.apm.agent.core.conf.Constants;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.InstanceMethodsAroundInterceptor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.MethodInterceptResult;
import org.apache.skywalking.apm.network.trace.component.ComponentsDefine;
import org.apache.skywalking.apm.plugin.lettuce.v5.LettucePluginConfig;
import org.apache.skywalking.apm.util.StringUtil;

import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

public class RedisChannelWriterInterceptor implements InstanceMethodsAroundInterceptor {

    private static final String PASSWORD_MASK = "******";
    private static final String ABBR = "...";
    private static final String AUTH = "AUTH";
    private static final StringCodec STRING_CODEC = new StringCodec();

    private static final Optional<String> READ_OPTIONAL = Optional.of("read");
    private static final Optional<String> WRITE_OPTIONAL = Optional.of("write");

    @Override
    public void beforeMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                             MethodInterceptResult result) throws Throwable {
        String peer = (String) objInst.getSkyWalkingDynamicField();
        RedisCommand<?, ?, ?> spanCarrierCommand = getSpanCarrierCommand(allArguments[0]);
        if (spanCarrierCommand == null) {
            return;
        }
        EnhancedInstance enhancedCommand = (EnhancedInstance) spanCarrierCommand;

        // command has been handle by another channel writer (cluster or sentinel case)
        if (enhancedCommand.getSkyWalkingDynamicField() != null) {
            // set peer in last channel writer (delegate)
            if (peer != null) {
                AbstractHeraSpan span = (AbstractHeraSpan) enhancedCommand.getSkyWalkingDynamicField();
                span.remoteServiceName(peer);
            }
            return;
        }

        String operationName = "Lettuce/";
        String key = Constants.EMPTY_STRING;
        String command = Constants.EMPTY_STRING;
        if (allArguments[0] instanceof RedisCommand) {
            RedisCommand<?, ?, ?> redisCommand = (RedisCommand<?, ?, ?>) allArguments[0];
            command = redisCommand.getType().name();
            operationName = operationName + command;
            if (LettucePluginConfig.Plugin.Lettuce.TRACE_REDIS_PARAMETERS) {
                key = getArgsKey(redisCommand);
            }
        } else if (allArguments[0] instanceof Collection) {
            operationName = operationName + "BATCH_WRITE";
            command = "BATCH_WRITE";
        }
        AbstractHeraSpan span = ContextManager.createExitSpan(operationName, peer);
        Tags.COMPONENT.set(span, ComponentsDefine.LETTUCE.getName());
        Tags.CACHE_TYPE.set(span, "Redis");
        if (StringUtil.isNotEmpty(key)) {
            Tags.CACHE_KEY.set(span, key);
        }
        Tags.CACHE_CMD.set(span, command);
        parseOperation(command.toLowerCase()).ifPresent(op -> Tags.CACHE_OP.set(span, op));
        span.prepareForAsync();
        ContextManager.stopSpan();
        enhancedCommand.setSkyWalkingDynamicField(span);
    }

    private String getArgsKey(RedisCommand<?, ?, ?> redisCommand) {
        if (AUTH.equalsIgnoreCase(redisCommand.getType().name())) {
            return PASSWORD_MASK;
        }
        CommandArgs<?, ?> args = redisCommand.getArgs();
        if (args == null) {
            return Constants.EMPTY_STRING;
        }
        ByteBuffer firstEncodedKey = args.getFirstEncodedKey();
        if (firstEncodedKey == null) {
            return Constants.EMPTY_STRING;
        }
        String key = STRING_CODEC.decodeKey(firstEncodedKey);
        if (StringUtil.isNotEmpty(key) && key.length() > LettucePluginConfig.Plugin.Lettuce.REDIS_PARAMETER_MAX_LENGTH) {
            key = StringUtil.cut(key, LettucePluginConfig.Plugin.Lettuce.REDIS_PARAMETER_MAX_LENGTH) + ABBR;
        }
        return key;
    }

    @Override
    public Object afterMethod(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes,
                              Object ret) throws Throwable {
        return ret;
    }

    @Override
    public void handleMethodException(EnhancedInstance objInst, Method method, Object[] allArguments, Class<?>[] argumentsTypes, Throwable t) {
        RedisCommand<?, ?, ?> redisCommand = getSpanCarrierCommand(allArguments[0]);
        if (redisCommand instanceof EnhancedInstance && ((EnhancedInstance) redisCommand).getSkyWalkingDynamicField() != null) {
            EnhancedInstance enhancedRedisCommand = (EnhancedInstance) redisCommand;
            AbstractHeraSpan span = (AbstractHeraSpan) enhancedRedisCommand.getSkyWalkingDynamicField();
            enhancedRedisCommand.setSkyWalkingDynamicField(null);
            ContextManager.logError(span, t);
            span.asyncFinish();
        }
    }

    private static RedisCommand<?, ?, ?> getSpanCarrierCommand(Object o) {
        RedisCommand<?, ?, ?> command = null;
        if (o instanceof RedisCommand) {
            command = (RedisCommand<?, ?, ?>) o;
        } else if (o instanceof List) {
            List<?> list = (List<?>) o;
            command = list.isEmpty() ? null : (RedisCommand<?, ?, ?>) list.get(list.size() - 1);
        } else if (o instanceof Collection) {
            Collection<RedisCommand<?, ?, ?>> redisCommands = (Collection<RedisCommand<?, ?, ?>>) o;
            RedisCommand<?, ?, ?> last = null;
            for (RedisCommand<?, ?, ?> redisCommand : redisCommands) {
                last = redisCommand;
            }
            command = last;
        }
        if (command instanceof DecoratedCommand) {
            while (command instanceof DecoratedCommand) {
                DecoratedCommand<?, ?, ?> wrapper = (DecoratedCommand<?, ?, ?>) command;
                command = wrapper.getDelegate();
            }
        }
        return command;
    }

    private Optional<String> parseOperation(String cmd) {
        if (LettucePluginConfig.Plugin.Lettuce.OPERATION_MAPPING_READ.contains(cmd)) {
            return READ_OPTIONAL;
        }
        if (LettucePluginConfig.Plugin.Lettuce.OPERATION_MAPPING_WRITE.contains(cmd)) {
            return WRITE_OPTIONAL;
        }
        return Optional.empty();
    }
}
