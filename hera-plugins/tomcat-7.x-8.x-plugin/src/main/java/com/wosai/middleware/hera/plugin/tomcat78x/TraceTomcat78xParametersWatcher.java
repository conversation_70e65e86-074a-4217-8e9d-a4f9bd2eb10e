package com.wosai.middleware.hera.plugin.tomcat78x;

import org.apache.skywalking.apm.agent.core.conf.dynamic.AgentConfigChangeWatcher;
import org.apache.skywalking.apm.plugin.tomcat78x.TomcatPluginConfig;

public class TraceTomcat78xParametersWatcher extends AgentConfigChangeWatcher {

    private final boolean defaultValue;

    public TraceTomcat78xParametersWatcher(String propertyKey) {
        super(propertyKey);
        defaultValue = TomcatPluginConfig.Plugin.Tomcat.COLLECT_HTTP_PARAMS;
    }

    @Override
    public void notify(ConfigChangeEvent value) {
        if (EventType.DELETE.equals(value.getEventType())) {
            TomcatPluginConfig.Plugin.Tomcat.COLLECT_HTTP_PARAMS = defaultValue;
        } else {
            TomcatPluginConfig.Plugin.Tomcat.COLLECT_HTTP_PARAMS = Boolean.parseBoolean(value.getNewValue());
        }
    }

    @Override
    public String value() {
        return Boolean.toString(TomcatPluginConfig.Plugin.Tomcat.COLLECT_HTTP_PARAMS);
    }
}
