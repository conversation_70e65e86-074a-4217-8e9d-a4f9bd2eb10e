package com.wosai.middleware.hera.plugin.tomcat78x;

import com.wosai.middleware.hera.tracing.propagation.TextMapExtract;
import org.apache.catalina.connector.Request;
import org.apache.tomcat.util.http.MimeHeaders;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class TomcatHeadersExtractAdapter implements TextMapExtract {
    private Map<String, List<String>> headers;

    public TomcatHeadersExtractAdapter(Request request) {
        this.headers = prepareHeaders(request);
    }

    @Override
    public Iterator<Map.Entry<String, String>> iterator() {
        return new MultivaluedMapFlatIterator<>(headers.entrySet());
    }

    protected Map<String, List<String>> prepareHeaders(Request request) {
        // @NonNull
        final MimeHeaders mimeHeaders = request.getCoyoteRequest().getMimeHeaders();
        final int headerLen = mimeHeaders.size();
        Map<String, List<String>> map = new HashMap<>(headerLen);
        for (int i = 0; i < headerLen; i++) {
            final String headerName = mimeHeaders.getName(i).toString();
            List<String> headerValues = map.computeIfAbsent(headerName, s -> new ArrayList<>(1));
            headerValues.add(mimeHeaders.getValue(i).toString());
        }
        return map;
    }

    public static final class MultivaluedMapFlatIterator<K, V> implements Iterator<Map.Entry<K, V>> {

        private final Iterator<Map.Entry<K, List<V>>> mapIterator;
        private Map.Entry<K, List<V>> mapEntry;
        private Iterator<V> listIterator;

        public MultivaluedMapFlatIterator(Set<Map.Entry<K, List<V>>> multiValuesEntrySet) {
            this.mapIterator = multiValuesEntrySet.iterator();
        }

        @Override
        public boolean hasNext() {
            if (listIterator != null && listIterator.hasNext()) {
                return true;
            }

            return mapIterator.hasNext();
        }

        @Override
        public Map.Entry<K, V> next() {
            if (mapEntry == null || !listIterator.hasNext() && mapIterator.hasNext()) {
                mapEntry = mapIterator.next();
                listIterator = mapEntry.getValue().iterator();
            }

            if (listIterator.hasNext()) {
                return new AbstractMap.SimpleImmutableEntry<>(mapEntry.getKey(), listIterator.next());
            } else {
                return new AbstractMap.SimpleImmutableEntry<>(mapEntry.getKey(), null);
            }
        }

        @Override
        public void remove() {
            throw new UnsupportedOperationException();
        }
    }
}
