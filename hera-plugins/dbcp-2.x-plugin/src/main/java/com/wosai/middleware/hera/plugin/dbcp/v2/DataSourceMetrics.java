package com.wosai.middleware.hera.plugin.dbcp.v2;

import com.wosai.middleware.hera.agent.metrics.MeterBinder;
import com.wosai.middleware.hera.agent.metrics.api.Gauge;
import org.apache.commons.dbcp2.BasicDataSourceMXBean;

public class DataSourceMetrics implements MeterBinder {
    private final BasicDataSourceMXBean basicDataSource;

    public DataSourceMetrics(BasicDataSourceMXBean basicDataSource) {
        this.basicDataSource = basicDataSource;
    }

    @Override
    public void bindTo() {
        Gauge.builder("commons.dbcp2.active.num", basicDataSource, BasicDataSourceMXBean::getNumActive).build();
        Gauge.builder("commons.dbcp2.total.max", basicDataSource, BasicDataSourceMXBean::getMaxTotal).build();
        Gauge.builder("commons.dbcp2.idle.num", basicDataSource, BasicDataSourceMXBean::getNumIdle).build();
        Gauge.builder("commons.dbcp2.wait.millis.max", basicDataSource, BasicDataSourceMXBean::getMaxWaitMillis).build();
        Gauge.builder("commons.dbcp2.idle.max", basicDataSource, BasicDataSourceMXBean::getMaxIdle).build();
        Gauge.builder("commons.dbcp2.idle.min", basicDataSource, BasicDataSourceMXBean::getMinIdle).build();
        Gauge.builder("commons.dbcp2.initial.size", basicDataSource, BasicDataSourceMXBean::getInitialSize).build();
    }
}
