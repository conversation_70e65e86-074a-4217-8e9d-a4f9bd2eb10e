package com.wosai.middleware.hera.plugin.jdbc.mysql.v5.define;

import com.wosai.middleware.hera.plugin.jdbc.mysql.Constants;
import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.matcher.ElementMatcher;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.ConstructorInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.InstanceMethodsInterceptPoint;
import org.apache.skywalking.apm.agent.core.plugin.match.ClassMatch;
import org.apache.skywalking.apm.agent.core.plugin.match.MultiClassNameMatch;
import org.apache.skywalking.apm.plugin.jdbc.mysql.v5.define.AbstractMysqlInstrumentation;

import static net.bytebuddy.matcher.ElementMatchers.named;

public class PreparedStatementInstrumentation extends AbstractMysqlInstrumentation {

    private static final String SERVICE_METHOD_INTERCEPTOR = Constants.PREPARED_STATEMENT_EXECUTE_METHODS_INTERCEPTOR;
    public static final String MYSQL_PREPARED_STATEMENT_CLASS_NAME = "com.mysql.jdbc.PreparedStatement";
    public static final String MYSQL_SERVER_PREPARED_STATEMENT_CLASS_NAME = "com.mysql.jdbc.ServerPreparedStatement";

    @Override public final ConstructorInterceptPoint[] getConstructorsInterceptPoints() {
        return new ConstructorInterceptPoint[0];
    }

    @Override public InstanceMethodsInterceptPoint[] getInstanceMethodsInterceptPoints() {
        return new InstanceMethodsInterceptPoint[] {
                new InstanceMethodsInterceptPoint() {
                    @Override public ElementMatcher<MethodDescription> getMethodsMatcher() {
                        return named("execute")
                                .or(named("executeQuery"))
                                .or(named("executeUpdate"))
                                .or(named("executeLargeUpdate"));
                    }

                    @Override public String getMethodsInterceptor() {
                        return SERVICE_METHOD_INTERCEPTOR;
                    }

                    @Override public boolean isOverrideArgs() {
                        return false;
                    }
                }
        };
    }

    @Override protected ClassMatch enhanceClass() {
        return MultiClassNameMatch.byMultiClassMatch(MYSQL_PREPARED_STATEMENT_CLASS_NAME, MYSQL_SERVER_PREPARED_STATEMENT_CLASS_NAME);
    }

}
