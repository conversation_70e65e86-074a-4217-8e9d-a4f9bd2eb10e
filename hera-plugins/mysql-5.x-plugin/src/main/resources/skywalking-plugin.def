# mysql-5.x plugin
mysql-5.x=com.wosai.middleware.hera.plugin.jdbc.mysql.v5.define.Mysql5xConnectionInstrumentation
mysql-5.x=com.wosai.middleware.hera.plugin.jdbc.mysql.v5.define.Mysql50ConnectionInstrumentation
mysql-5.x=com.wosai.middleware.hera.plugin.jdbc.mysql.v5.define.CallableInstrumentation
mysql-5.x=com.wosai.middleware.hera.plugin.jdbc.mysql.v5.define.PreparedStatementInstrumentation
mysql-5.x=com.wosai.middleware.hera.plugin.jdbc.mysql.v5.define.StatementInstrumentation
mysql-5.x=com.wosai.middleware.hera.plugin.jdbc.mysql.v5.define.CacheIpsDriverInstrumentation
mysql-5.x=org.apache.skywalking.apm.plugin.jdbc.mysql.v5.define.ConnectionImplCreateInstrumentation
mysql-5.x=org.apache.skywalking.apm.plugin.jdbc.mysql.v5.define.PreparedStatementIgnoredSetterInstrumentation
mysql-5.x=org.apache.skywalking.apm.plugin.jdbc.mysql.v5.define.PreparedStatementSetterInstrumentation
mysql-5.x=org.apache.skywalking.apm.plugin.jdbc.mysql.v5.define.PreparedStatementNullSetterInstrumentation