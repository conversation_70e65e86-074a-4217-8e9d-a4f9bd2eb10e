package com.wosai.middleware.hera.plugin.okhttp.common;

import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;

/**
 * {@link EnhanceRequiredInfo} stores the `Span` and `RealCall` instances for support the async function of
 * okhttp client.
 */
@Getter
@RequiredArgsConstructor
public class EnhanceRequiredInfo {
    private final EnhancedInstance realCallEnhance;

    /**
     * Span is used to replace the ContextSnapshot in the Apache/Skywalking
     * since it is thread-safe
     */
    private final AbstractHeraSpan parentSpan;
}
