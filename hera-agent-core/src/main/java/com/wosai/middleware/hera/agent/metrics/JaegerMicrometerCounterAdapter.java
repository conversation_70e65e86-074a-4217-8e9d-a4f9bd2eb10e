package com.wosai.middleware.hera.agent.metrics;

import io.jaegertracing.internal.metrics.Counter;

public class JaegerMicrometerCounterAdapter implements Counter {
    private final io.micrometer.core.instrument.Counter delegate;

    private JaegerMicrometerCounterAdapter(io.micrometer.core.instrument.Counter delegate) {
        this.delegate = delegate;
    }

    @Override
    public void inc(long delta) {
        this.delegate.increment(delta);
    }

    public static Counter create(io.micrometer.core.instrument.Counter delegate) {
        return new JaegerMicrometerCounterAdapter(delegate);
    }
}
