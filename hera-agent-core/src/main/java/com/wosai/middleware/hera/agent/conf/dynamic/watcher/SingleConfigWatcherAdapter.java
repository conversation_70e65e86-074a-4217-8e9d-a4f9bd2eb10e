package com.wosai.middleware.hera.agent.conf.dynamic.watcher;

import com.google.common.base.Preconditions;
import com.wosai.middleware.hera.agent.conf.dynamic.AbstractAgentConfigChangeWatcher;
import org.apache.skywalking.apm.agent.core.conf.dynamic.AgentConfigChangeWatcher;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

public class SingleConfigWatcherAdapter extends AbstractAgentConfigChangeWatcher {

    private static final ILog LOGGER = LogManager.getLogger(SingleConfigWatcherAdapter.class);

    private final AgentConfigChangeWatcher watcher;

    public static SingleConfigWatcherAdapter wrap(AgentConfigChangeWatcher watcher) {
        Preconditions.checkNotNull(watcher);
        Preconditions.checkNotNull(watcher.getPropertyKey());
        return new SingleConfigWatcherAdapter(watcher);
    }

    private SingleConfigWatcherAdapter(AgentConfigChangeWatcher watcher) {
        super(Collections.singleton(watcher.getPropertyKey()));
        this.watcher = watcher;
    }

    @Override
    public void notify(List<ConfigChangeEvent> list) {
        if (list != null && list.size() == 1) {
            ConfigChangeEvent event = list.get(0);

            Optional<AgentConfigChangeWatcher.ConfigChangeEvent> configChangeEvent = convertEvent(event);

            if (configChangeEvent.isPresent()) {
                watcher.notify(configChangeEvent.get());
            } else {
                LOGGER.error("Event is null, will not notify watcher");
            }
        } else {
            LOGGER.warn("stop notifying, because the list size expected 1 but {}", Optional.ofNullable(list).map(List::size));
        }
    }

    private Optional<AgentConfigChangeWatcher.ConfigChangeEvent> convertEvent(ConfigChangeEvent event) {
        AgentConfigChangeWatcher.ConfigChangeEvent targetEvent = null;
        switch (event.getEventType()) {
            case ADD:
                targetEvent = new AgentConfigChangeWatcher.ConfigChangeEvent(event.getNewValue(), AgentConfigChangeWatcher.EventType.ADD);
                break;
            case DELETE:
                targetEvent = new AgentConfigChangeWatcher.ConfigChangeEvent(event.getNewValue(), AgentConfigChangeWatcher.EventType.DELETE);
                break;
            case MODIFY:
                targetEvent = new AgentConfigChangeWatcher.ConfigChangeEvent(event.getNewValue(), AgentConfigChangeWatcher.EventType.MODIFY);
                break;
            default:
                LOGGER.error("cannot convert AbstractAgentConfigChangeWatcher.ConfigChangeEvent to AgentConfigChangeWatcher.ConfigChangeEvent...");
        }
        return Optional.ofNullable(targetEvent);
    }
}
