/*
 * Copyright 2013-2020 The OpenZipkin Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
 * in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 */

package com.wosai.middleware.hera.tracing.internal.baggage;

import brave.internal.collect.UnsafeArrayMap;
import com.wosai.middleware.hera.tracing.baggage.BaggageField;
import com.wosai.middleware.hera.tracing.internal.extra.MapExtra;
import com.wosai.middleware.hera.tracing.internal.extra.MapExtraFactory;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Arrays;

/**
 * Holds one or more baggage fields in {@link com.wosai.middleware.hera.tracing.propagation.HeraSpanContext#extra()} or
 * {@link com.wosai.middleware.hera.tracing.propagation.TraceContextOrSamplingFlags#extra()}.
 */
public final class BaggageFields
    extends MapExtra<BaggageField, String, BaggageFields, BaggageFields.Factory>
    implements BaggageField.ValueUpdater {

  static final UnsafeArrayMap.Mapper<Object, String> FIELD_TO_NAME = new UnsafeArrayMap.Mapper<Object, String>() {
    @Override public String map(Object input) {
      return ((BaggageField) input).name();
    }
  };
  static final UnsafeArrayMap.Builder<String, String> MAP_STRING_STRING_BUILDER =
      UnsafeArrayMap.<String, String>newBuilder().mapKeys(FIELD_TO_NAME);

  public static Factory newFactory(List<BaggageField> fields, int maxDynamicEntries) {
    if (fields == null) throw new NullPointerException("fields == null");
    FactoryBuilder builder = new FactoryBuilder();
    for (BaggageField field : fields) builder.addInitialKey(field);
    return builder.maxDynamicEntries(maxDynamicEntries).build();
  }

  static final class FactoryBuilder extends
      MapExtraFactory.Builder<BaggageField, String, BaggageFields, Factory, FactoryBuilder> {
    @Override protected Factory build() {
      return new Factory(this);
    }
  }

  public static final class Factory
      extends MapExtraFactory<BaggageField, String, BaggageFields, Factory> {
    Factory(FactoryBuilder builder) {
      super(builder);
    }

    @Override public BaggageFields create() {
      return new BaggageFields(this);
    }
  }

  BaggageFields(Factory factory) {
    super(factory);
  }

  Object[] state() {
    return (Object[]) state;
  }

  @Override public boolean updateValue(BaggageField field, String value) {
    return put(field, value);
  }

  @Nullable
  public String getValue(BaggageField key) {
    return super.get(key);
  }

  /**
   * The list of fields present, regardless of value. The result is cacheable unless {@link
   * #isDynamic()}.
   */
  public List<BaggageField> getAllFields() {
    return Collections.unmodifiableList(new ArrayList<>(keySet()));
  }

  /** Returns a read-only view of the non-null baggage field values */
  public Map<String, String> toMapFilteringFieldNames(String... filtered) {
    return UnsafeArrayMap.<String, String>newBuilder().mapKeys(FIELD_TO_NAME)
        .filterKeys(filtered)
        .build(state());
  }

  /** Returns a possibly empty map of all name to non-{@code null} values. */
  public Map<String, String> getAllValues() {
    return MAP_STRING_STRING_BUILDER.build(state());
  }

  public void addBaggage(BaggageField baggageField, String value) {
    Object[] stateLst = state();
    List<Object> newStatelist = new ArrayList<>(Arrays.asList(stateLst));
    newStatelist.add(baggageField);
    newStatelist.add(value);
    this.state = newStatelist.toArray();
  }
}
