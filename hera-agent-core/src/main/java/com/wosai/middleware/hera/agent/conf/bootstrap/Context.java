package com.wosai.middleware.hera.agent.conf.bootstrap;

import com.google.common.base.Strings;
import com.wosai.middleware.hera.util.Platform;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.util.concurrent.TimeUnit;

public class Context {
    static final OkHttpClient META_CLIENT = new OkHttpClient.Builder()
            // connect timeout = 1000ms
            .connectTimeout(1000, TimeUnit.MILLISECONDS)
            // read timeout = 1000ms
            .readTimeout(1000, TimeUnit.MILLISECONDS)
            .build();
    private static final String URL_METADATA_REGION_ID = "http://100.100.100.200/latest/meta-data/region-id";

    // a cache for region
    private volatile Region region = null;

    /**
     * @return profile detected
     */
    public Profile getProfile() {
        String env = Platform.getEnv();
        if (Strings.isNullOrEmpty(env)) {
            return Profile.UNKNOWN;
        }
        if (Profile.PRO.name().equals(env.toUpperCase())) {
            return Profile.PRO;
        } else if (Profile.BETA.name().equals(env.toUpperCase())) {
            return Profile.BETA;
        }

        return Profile.UNKNOWN;
    }

    public Region getRegion() {
        if (region != null) {
            return region;
        }
        final Request r = new Request.Builder().url(URL_METADATA_REGION_ID).build();
        try (Response resp = META_CLIENT.newCall(r).execute()) {
            if (resp.body() != null) {
                region = parseRegion(resp.body().string());
            } else {
                region = Region.UNKNOWN;
            }
        } catch (Exception e) {
            region = Region.UNKNOWN;
        }
        return region;
    }

    static Region parseRegion(String regionStr) {
        if (Strings.isNullOrEmpty(regionStr)) {
            return Region.UNKNOWN;
        }
        return Region.valueOf(regionStr.trim().replace("-", "_").toUpperCase());
    }

    public enum Profile {
        PRO, BETA, UNKNOWN
    }

    @ToString
    @RequiredArgsConstructor
    public enum Region {
        CN_HANGZHOU("cn-hangzhou"),
        CN_ZHANGJIAKOU("cn-zhangjiakou"),
        CN_SHENZHEN("cn-shenzhen"),
        UNKNOWN("unknown");

        private final String regionID;
    }
}
