package com.wosai.middleware.hera.agent.commands.executor;

import com.wosai.middleware.hera.agent.commands.CommandExecutionException;
import com.wosai.middleware.hera.agent.commands.CommandExecutor;
import com.wosai.middleware.hera.network.component.command.BaseCommand;

/**
 * A dummy executor that does nothing when executing a command
 */
public enum NoopCommandExecutor implements CommandExecutor {
    INSTANCE;

    @Override
    public void execute(final BaseCommand command) throws CommandExecutionException {

    }

}