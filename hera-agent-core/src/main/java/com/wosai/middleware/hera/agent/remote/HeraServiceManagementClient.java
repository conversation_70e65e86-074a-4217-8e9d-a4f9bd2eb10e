package com.wosai.middleware.hera.agent.remote;

import com.google.common.annotations.VisibleForTesting;
import com.shouqianba.middleware.hera.network.common.v1.KeyValuePair;
import com.shouqianba.middleware.hera.network.management.v1.InstancePingRequest;
import com.shouqianba.middleware.hera.network.management.v1.InstancePropertiesRequest;
import com.shouqianba.middleware.hera.network.management.v1.RxManagementServiceGrpc;
import com.wosai.middleware.hera.agent.conf.GitProperty;
import com.wosai.middleware.hera.agent.core.kafka.KafkaConfigCollector;
import io.grpc.Channel;
import io.reactivex.Single;
import io.reactivex.disposables.Disposable;
import org.apache.skywalking.apm.agent.core.boot.BootService;
import org.apache.skywalking.apm.agent.core.boot.DefaultNamedThreadFactory;
import org.apache.skywalking.apm.agent.core.boot.OverrideImplementor;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;
import org.apache.skywalking.apm.agent.core.conf.Config;
import org.apache.skywalking.apm.agent.core.jvm.LoadedLibraryCollector;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.os.OSUtil;
import org.apache.skywalking.apm.agent.core.remote.GRPCChannelListener;
import org.apache.skywalking.apm.agent.core.remote.GRPCChannelStatus;
import org.apache.skywalking.apm.agent.core.remote.ServiceManagementClient;
import org.apache.skywalking.apm.agent.core.util.InstanceJsonPropertiesUtil;
import org.apache.skywalking.apm.dependencies.com.google.gson.Gson;
import org.apache.skywalking.apm.dependencies.com.google.gson.GsonBuilder;
import org.apache.skywalking.apm.util.RunnableWithExceptionProtection;

import java.lang.management.ManagementFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.apache.skywalking.apm.agent.core.conf.Config.Collector.GRPC_UPSTREAM_TIMEOUT;

@OverrideImplementor(ServiceManagementClient.class)
public class HeraServiceManagementClient extends ServiceManagementClient
        implements BootService, Runnable, GRPCChannelListener {

    private static final ILog LOGGER = LogManager.getLogger(HeraServiceManagementClient.class);
    private static final Gson GSON = new GsonBuilder().disableHtmlEscaping().create();
    private static List<KeyValuePair> SERVICE_INSTANCE_PROPERTIES;
    @VisibleForTesting
    volatile RxManagementServiceGrpc.RxManagementServiceStub rxManagementServiceStub;
    @VisibleForTesting
    volatile GRPCChannelStatus status = GRPCChannelStatus.DISCONNECT;
    private volatile ScheduledFuture<?> heartbeatFuture;
    private final AtomicInteger sendPropertiesCounter = new AtomicInteger(0);
    @VisibleForTesting
    Disposable disposable;

    @Override
    public void run() {
        LOGGER.debug("ServiceManagementClient running, status:{}.", status);

        if (GRPCChannelStatus.CONNECTED.equals(status)) {
            if (rxManagementServiceStub != null) {
                if (Math.abs(sendPropertiesCounter.getAndAdd(1)) % Config.Collector.PROPERTIES_REPORT_PERIOD_FACTOR == 0) {
                    // instancePropertiesRequest
                    disposable = Single.fromCallable(() -> InstancePropertiesRequest.newBuilder()
                                    .setService(Config.Agent.SERVICE_NAME)
                                    .setServiceInstance(Config.Agent.INSTANCE_NAME)
                                    .addAllProperties(SERVICE_INSTANCE_PROPERTIES)
                                    .addAllProperties(buildInfo()).build())
                            .compose(rxManagementServiceStub
                                    .withDeadlineAfter(GRPC_UPSTREAM_TIMEOUT
                                            , TimeUnit.SECONDS)::reportInstanceProperties)
                            .subscribe(commands -> {
                            }, ReactiveGrpcThrowableHandler::handleThrowable);
                } else {
                    // instancePingRequest
                    disposable = Single.fromCallable(() -> InstancePingRequest.newBuilder()
                                    .setService(Config.Agent.SERVICE_NAME)
                                    .setServiceInstance(Config.Agent.INSTANCE_NAME).build())
                            .compose(rxManagementServiceStub
                                    .withDeadlineAfter(GRPC_UPSTREAM_TIMEOUT, TimeUnit.SECONDS)::keepAlive)
                            .subscribe(commands -> {
                            }, ReactiveGrpcThrowableHandler::handleThrowable);
                }
            }
        }
    }

    @Override
    public void prepare() {
        ServiceManager.INSTANCE.findService(HeraGRPCChannelManager.class).addChannelListener(this);
        SERVICE_INSTANCE_PROPERTIES = KeyValuePairUtils.convert(InstanceJsonPropertiesUtil.parseProperties());
    }

    @Override
    public void boot() {
        heartbeatFuture = Executors.newSingleThreadScheduledExecutor(
                        new DefaultNamedThreadFactory("HeraServiceManagementClient"))
                .scheduleAtFixedRate(
                        new RunnableWithExceptionProtection(this, t -> LOGGER.error("unexpected exception.", t)),
                        0, Config.Collector.HEARTBEAT_PERIOD, TimeUnit.SECONDS);
    }

    @Override
    public void onComplete() {

    }

    @Override
    public void shutdown() {
        heartbeatFuture.cancel(true);
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
        }
        statusChanged(GRPCChannelStatus.DISCONNECT);
    }

    @Override
    public void statusChanged(GRPCChannelStatus grpcChannelStatus) {
        if (GRPCChannelStatus.CONNECTED.equals(grpcChannelStatus)) {
            LOGGER.info("connect to management grpc server success");
            Channel channel = ServiceManager.INSTANCE.findService(HeraGRPCChannelManager.class).getChannel();
            rxManagementServiceStub = RxManagementServiceGrpc.newRxStub(channel);
        } else {
            rxManagementServiceStub = null;
        }
        this.status = grpcChannelStatus;
    }

    private List<KeyValuePair> buildInfo() {
        List<KeyValuePair> result = new ArrayList<>();
        result.add(KeyValuePair.newBuilder()
                .setKey("JVM Version")
                .setValue(ManagementFactory.getRuntimeMXBean().getVmVersion()).build());
        result.add(KeyValuePair.newBuilder()
                .setKey("Environment Variables")
                .setValue(GSON.toJson(System.getenv())).build());
        result.add(KeyValuePair.newBuilder()
                .setKey("Hera Version")
                .setValue(String.format("%s-%s", GitProperty.Git.Build.VERSION, GitProperty.Git.Commit.Id.ABBREV))
                .build());
        result.addAll(KeyValuePairUtils.convert(LoadedLibraryCollector.buildJVMInfo()));
        result.addAll(KeyValuePairUtils.convert(KafkaConfigCollector.buildKafkaConfigInfo()));
        result.addAll(KeyValuePairUtils.convert(OSUtil.buildOSInfo(Config.OsInfo.IPV4_LIST_SIZE)));
        return result;
    }
}
