package com.wosai.middleware.hera.agent.sentinel;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;

public class FlowRule extends AbstractRule<com.alibaba.csp.sentinel.slots.block.flow.FlowRule> {
    public FlowRule(com.alibaba.csp.sentinel.slots.block.flow.FlowRule flowRule) {
        super(flowRule);
    }

    @VisibleForTesting
    public static FlowRule initFlowRule(String resourceName, String limitApp, int count) {
        com.alibaba.csp.sentinel.slots.block.flow.FlowRule flowRule = new com.alibaba.csp.sentinel.slots.block.flow.FlowRule(resourceName);
        if (!Strings.isNullOrEmpty(limitApp)) {
            flowRule.setLimitApp(limitApp);
        }
        flowRule.setCount(count);
        return new FlowRule(flowRule);
    }

    @VisibleForTesting
    public com.alibaba.csp.sentinel.slots.block.flow.FlowRule getCoreRule() {
        return this.delegation;
    }

    public int getControlBehavior() {
        return this.delegation.getControlBehavior();
    }

    public int getMaxQueueingTimeMs() {
        return this.delegation.getMaxQueueingTimeMs();
    }

    public int getGrade() {
        return this.delegation.getGrade();
    }

    public double getCount() {
        return this.delegation.getCount();
    }

    public int getStrategy() {
        return this.delegation.getStrategy();
    }

    public String getRefResource() {
        return this.delegation.getRefResource();
    }

    public boolean isClusterMode() {
        return this.delegation.isClusterMode();
    }

    public int getWarmUpPeriodSec() {
        return this.delegation.getWarmUpPeriodSec();
    }

    public ClusterFlowConfig getClusterConfig() {
        return this.delegation.getClusterConfig() == null ? null : new ClusterFlowConfig(this.delegation.getClusterConfig());
    }

    public static class ClusterFlowConfig extends ObjectDelegation<com.alibaba.csp.sentinel.slots.block.flow.ClusterFlowConfig> {
        public ClusterFlowConfig(com.alibaba.csp.sentinel.slots.block.flow.ClusterFlowConfig delegation) {
            super(delegation);
        }

        public long getResourceTimeout() {
            return this.delegation.getResourceTimeout();
        }

        public int getResourceTimeoutStrategy() {
            return this.delegation.getResourceTimeoutStrategy();
        }

        public int getAcquireRefuseStrategy() {
            return this.delegation.getAcquireRefuseStrategy();
        }

        public long getClientOfflineTime() {
            return this.delegation.getClientOfflineTime();
        }

        public Long getFlowId() {
            return this.delegation.getFlowId();
        }

        public int getThresholdType() {
            return this.delegation.getThresholdType();
        }

        public int getStrategy() {
            return this.delegation.getStrategy();
        }

        public boolean isFallbackToLocalWhenFail() {
            return this.delegation.isFallbackToLocalWhenFail();
        }

        public int getSampleCount() {
            return this.delegation.getSampleCount();
        }

        public int getWindowIntervalMs() {
            return this.delegation.getWindowIntervalMs();
        }
    }
}
