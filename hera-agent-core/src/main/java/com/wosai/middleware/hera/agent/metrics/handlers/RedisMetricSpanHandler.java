package com.wosai.middleware.hera.agent.metrics.handlers;

import com.wosai.middleware.hera.agent.metrics.Action;
import com.wosai.middleware.hera.agent.metrics.HandlerContext;
import com.wosai.middleware.hera.agent.metrics.api.Tag;
import com.wosai.middleware.hera.tracing.internal.handler.MutableSpan;
import com.wosai.middleware.hera.tracing.tag.Tags;
import lombok.experimental.SuperBuilder;
import org.apache.skywalking.apm.util.StringUtil;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * Generate metrics for redis client.
 * 1) cache.access{error="true", op="read|write|", db_instance="redis-server:6379", type="redis"}
 * 2) cache.access:aggr{db_instance="redis-server:6379", type="redis"}
 */
@SuperBuilder
@MetricHandlerMatcher(phase = "tag.component", arguments = {"Jedis", "Lettuce", "Redisson"})
public class RedisMetricSpanHandler extends AbstractComponentHandler {
    private static final Tag TAG_TYPE_REDIS = Tag.of("type", "redis");
    private static final String UNKNOWN_OP = "";
    private final double[] percentiles;

    @Override
    Action handleWithErrorTag(HandlerContext context, Tag errorTag) {
        final MutableSpan span = context.getSpan();
        Tag instanceTag = Tag.of("db_instance", span.remoteServiceName());
        // FIX: issue #37
        Object opVal = span.tag(Tags.CACHE_OP.getKey());
        Tag opTag = Tag.of("op", StringUtil.isEmpty((String) opVal) ? UNKNOWN_OP : (String) opVal);
        createTimer("cache.access", Arrays.asList(errorTag, opTag, instanceTag, TAG_TYPE_REDIS), this.percentiles)
                .record(span.duration(), TimeUnit.MICROSECONDS);
        // not necessary to build per call metric for redis
        // aggregation
        createTimer("cache.access:aggr", Arrays.asList(instanceTag, TAG_TYPE_REDIS), this.percentiles)
                .record(span.duration(), TimeUnit.MICROSECONDS);

        return Action.CONTINUE;
    }

    @Override
    public Tag parseThrowable(Throwable t) {
        if (t == null) return Tag.of("error", "false");

        return Tag.of("error", "true");
    }
}
