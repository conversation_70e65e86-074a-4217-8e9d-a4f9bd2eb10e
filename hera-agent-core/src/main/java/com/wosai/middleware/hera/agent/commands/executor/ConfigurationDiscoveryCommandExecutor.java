package com.wosai.middleware.hera.agent.commands.executor;

import com.wosai.middleware.hera.agent.commands.CommandExecutionException;
import com.wosai.middleware.hera.agent.commands.CommandExecutor;
import com.wosai.middleware.hera.agent.conf.dynamic.MetaConfigurationDiscoveryService;
import com.wosai.middleware.hera.network.component.command.BaseCommand;
import com.wosai.middleware.hera.network.component.command.ConfigurationDiscoveryCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;

@Slf4j
public class ConfigurationDiscoveryCommandExecutor implements CommandExecutor {
    @Override
    public void execute(BaseCommand command) throws CommandExecutionException {
        try {
            ConfigurationDiscoveryCommand agentDynamicConfigurationCommand = (ConfigurationDiscoveryCommand) command;

            ServiceManager.INSTANCE.findService(MetaConfigurationDiscoveryService.class)
                                   .handleConfigurationDiscoveryCommand(agentDynamicConfigurationCommand);
        } catch (Exception e) {
            log.error("Handle ConfigurationDiscoveryCommand error, command:{}", command.toString(), e);
        }
    }
}