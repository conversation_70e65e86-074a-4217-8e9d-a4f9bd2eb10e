package com.wosai.middleware.hera.agent.metrics.handlers;

import com.wosai.middleware.hera.agent.metrics.Action;
import com.wosai.middleware.hera.agent.metrics.HandlerContext;
import com.wosai.middleware.hera.agent.metrics.MetricsSpanHandler;
import com.wosai.middleware.hera.agent.metrics.api.Tag;
import com.wosai.middleware.hera.agent.metrics.api.Timer;
import lombok.experimental.SuperBuilder;

import java.util.List;

@SuperBuilder
public abstract class AbstractComponentHandler implements MetricsSpanHandler {
    protected AbstractComponentHandler() {
    }

    @Override
    public Action handle(HandlerContext context) {
        // Example of adding a correlated tag. Note that in spans, we don't add a negative one (None)
        Tag errorTag = parseThrowable(context.getSpan().error());
        // https://github.com/micrometer-metrics/micrometer/issues/2060
        // Wait for bug fix from upstream, hopefully in micrometer 1.5.1
        return handleWithErrorTag(context, errorTag);
    }

    /**
     * Create a timer with Tags. If percentiles are not null or empty,
     * the given percentiles are registered.
     *
     * @param metricName  name of the metric
     * @param tags        a list of {@link Tag} attached to the metric
     * @param percentiles an optional list of percentiles to be published
     * @return a {@link Timer} created
     */
    protected Timer createTimer(String metricName, List<Tag> tags, double... percentiles) {
        if (percentiles == null || percentiles.length == 0) {
            return Timer.builder(metricName).tags(tags)
                    .build();
        }
        return Timer.builder(metricName).tags(tags)
                // disable histogram publish
                .publishPercentileHistogram(false)
                .publishPercentiles(percentiles)
                .build();
    }

    abstract Action handleWithErrorTag(HandlerContext context, Tag errorTag);
}
