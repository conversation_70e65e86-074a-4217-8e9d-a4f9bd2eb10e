package com.wosai.middleware.hera.agent.metrics.handlers;

import com.wosai.middleware.hera.agent.metrics.Action;
import com.wosai.middleware.hera.agent.metrics.HandlerContext;
import com.wosai.middleware.hera.agent.metrics.MetricsSpanHandler;

import java.util.ArrayList;
import java.util.List;

public abstract class PhaseHandler implements MetricsSpanHandler, Comparable<PhaseHandler> {
    private final int priority;
    private final String phaseName;

    public PhaseHandler(int priority, String name) {
        this.priority = priority;
        this.phaseName = name;
    }

    /**
     * It is up to the impl to decide how to use the arguments
     */
    abstract void registerHandler(MetricsSpanHandler handler, String... arguments);

    public String phaseName() {
        return this.phaseName;
    }

    public int priority() {
        return this.priority;
    }

    @Override
    public int compareTo(PhaseHandler o) {
        if (this.priority() != o.priority()) {
            return this.priority() - o.priority();
        } else {
            return this.phaseName.compareTo(o.phaseName);
        }
    }

    public static PhaseHandler newSimplePhaseHandler(int priority, String name) {
        return new ArrayListPhaseHandler(priority, name);
    }

    public static class ArrayListPhaseHandler extends PhaseHandler {

        private final List<MetricsSpanHandler> handlers = new ArrayList<>();

        private ArrayListPhaseHandler(int priority, String name) {
            super(priority, name);
        }

        @Override
        void registerHandler(MetricsSpanHandler handler, String... arguments) {
            this.handlers.add(handler);
        }

        @Override
        public Action handle(HandlerContext context) {
            for (MetricsSpanHandler handler : handlers) {
                if (handler.handle(context) == Action.ABORT) {
                    return Action.ABORT;
                }
            }
            return Action.CONTINUE;
        }
    }
}
