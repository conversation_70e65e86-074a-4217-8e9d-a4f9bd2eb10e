package com.wosai.middleware.hera.agent.tracing;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.middleware.hera.agent.conf.HeraConfig;
import com.wosai.middleware.hera.tracing.HeraScopeManager;
import com.wosai.middleware.hera.tracing.HeraTracer;
import com.wosai.middleware.hera.tracing.network.NetworkSpan;
import com.wosai.middleware.hera.tracing.spi.Reporter;
import com.wosai.middleware.hera.util.StackElementFilter;
import com.wosai.middleware.hera.util.StackHasher;
import com.wosai.middleware.hera.util.ThrowableConverter;
import io.jaegertracing.internal.clock.SystemClock;
import io.jaegertracing.internal.metrics.Metrics;
import io.jaegertracing.spi.Sampler;
import org.apache.skywalking.apm.agent.core.conf.Config;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.util.StringUtil;

import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TracerProvider implements Supplier<HeraTracer> {
    private static final ILog LOGGER = LogManager.getLogger(TracerProvider.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private final Sampler sampler;
    private final Metrics metrics;
    private final HeraScopeManager scopeManager;
    private final Reporter<NetworkSpan> reporter;

    public TracerProvider(Sampler sampler, Metrics metrics, HeraScopeManager scopeManager, Reporter<NetworkSpan> reporter) {
        this.sampler = sampler;
        this.metrics = metrics;
        this.scopeManager = scopeManager;
        this.reporter = reporter;
    }

    @Override
    public HeraTracer get() {
        List<Pattern> patterns = Stream.of(
                "^org\\.apache\\.catalina",
                "^org\\.apache\\.coyote",
                "^org\\.apache\\.tomcat",
                "^org\\.hibernate",
                "^java\\.lang\\.reflect\\.Method",
                "^sun\\.",
                "^javax\\.servlet",
                "^org\\.eclipse\\.jetty"
        ).map(Pattern::compile).collect(Collectors.toList());
        StackElementFilter filter = StackElementFilter.byPattern(patterns);
        final ThrowableConverter converter = ThrowableConverter.builder()
                .rootCauseFirst(true)
                .inlineHash(true)
                .stackElementFilter(filter)
                .stackHasher(new StackHasher(filter))
                .build();

        Map<String, String> appPropertiesMap = null;
        if (StringUtil.isNotEmpty(Config.Agent.INSTANCE_PROPERTIES_JSON)) {
            try {
                appPropertiesMap = OBJECT_MAPPER.readValue(Config.Agent.INSTANCE_PROPERTIES_JSON, new TypeReference<Map<String, String>>() {
                });
            } catch (Exception e) {
                LOGGER.error("error in jackson");
            }
        }

        HeraTracer.Builder builder = new HeraTracer.Builder(Config.Agent.SERVICE_NAME)
                .withClock(new SystemClock())
                .withTags(appPropertiesMap)
                .withTraceId128Bit(HeraConfig.Tracer.TRACEID_128BIT)
                .withSampler(this.sampler)
                .withMetrics(this.metrics)
                .withScopeManager(this.scopeManager)
                .withReporter(this.reporter)
                .withThrowableSerializer(converter::convert)
                .withExpandExceptionLogs(HeraConfig.Jaeger.EXPAND_EXCEPTION_LOGS);
        com.wosai.middleware.hera.tracing.Configuration.CodecConfiguration
                .fromString(HeraConfig.Jaeger.CODEC_PROPAGATION, HeraConfig.Jaeger.BAGGAGE_ITEMS)
                .apply(builder);
        return builder.build();
    }
}
