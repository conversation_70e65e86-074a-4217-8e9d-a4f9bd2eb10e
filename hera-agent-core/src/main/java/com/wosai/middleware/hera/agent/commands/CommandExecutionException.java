package com.wosai.middleware.hera.agent.commands;

import com.shouqianba.middleware.hera.network.common.v1.Command;

/**
 * Indicates that the execution of a command failed
 */
public class CommandExecutionException extends Throwable {
    private final Command command;

    /**
     * Constructs a new {@code ExecuteFailedException} with null detail message and the command whose execution failed
     *
     * @param command the command whose execution failed
     */
    public CommandExecutionException(final Command command) {
        this(null, command);
    }

    /**
     * Constructs a new {@code ExecuteFailedException} with given detail message and the command whose execution failed
     *
     * @param message the detail message of the exception
     * @param command the command whose execution failed
     */
    public CommandExecutionException(final String message, final Command command) {
        super(message);
        this.command = command;
    }

    public Command command() {
        return command;
    }
}