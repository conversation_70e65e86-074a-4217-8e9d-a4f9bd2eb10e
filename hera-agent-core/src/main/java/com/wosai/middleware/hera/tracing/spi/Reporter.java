package com.wosai.middleware.hera.tracing.spi;

import com.wosai.middleware.hera.tracing.internal.handler.FinishedSpanHandler;

import java.io.Closeable;
import java.io.IOException;

public interface Reporter<S> extends Closeable {
    Reporter<?> NOOP = new Reporter<Object>() {
        @Override
        public void report(Object span) {
        }

        @Override
        public void close() throws IOException {
        }

        @Override
        public String toString() {
            return "NoopReporter{}";
        }
    };

    /**
     * Schedules the span to be sent onto the transport.
     *
     * @param span Span, should not be <code>null</code>.
     */
    void report(S span) throws Exception;

    // create a span handler
    default FinishedSpanHandler createSpanHandler() {
        return FinishedSpanHandler.NOOP;
    }
}
