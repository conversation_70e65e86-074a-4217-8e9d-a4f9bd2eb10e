package com.wosai.middleware.hera.tracing.internal.handler;

import com.wosai.middleware.hera.tracing.Reference;
import com.wosai.middleware.hera.tracing.network.NetworkSpan;
import io.jaegertracing.thrift.internal.reporters.protocols.JaegerThriftSpanConverter;
import io.jaegertracing.thriftjava.Log;
import io.jaegertracing.thriftjava.Span;
import io.jaegertracing.thriftjava.SpanRef;
import io.jaegertracing.thriftjava.SpanRefType;
import io.jaegertracing.thriftjava.Tag;
import io.jaegertracing.thriftjava.TagType;
import io.opentracing.References;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public final class JaegerSpanConverter {

    public static Span convert(NetworkSpan span) {
        boolean oneChildOfParent = span.getReferences().size() == 1
                && References.CHILD_OF.equals(span.getReferences().get(0).getType());

        List<SpanRef> references = oneChildOfParent
                ? Collections.<SpanRef>emptyList()
                : buildReferences(span.getReferences());

        // probably emptyMap
        Map<String, Object> extraTags = span.convertRemoteEndpointAsSpanTags();
        // unmodifiable
        Map<String, Object> tags = span.getTags();

        Map<String, Object> allTags = Stream.concat(tags.entrySet().stream(), extraTags.entrySet().stream())
                // if collision, always use Entry from `extraTags`
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v2, HashMap::new));

        return new Span(
                span.getTraceIDLow(),
                span.getTraceIDHigh(),
                span.getSpanID(),
                oneChildOfParent ? span.getParentID() : 0,
                span.getName(),
                span.getFlags(),
                span.getStartTimestampMicro(),
                span.getDuration())
                .setReferences(references)
                .setTags(JaegerThriftSpanConverter.buildTags(allTags))
                .setLogs(buildLogs(span.getLogs()));
    }

    static List<Log> buildLogs(List<Object> logs) {
        List<Log> thriftLogs = new ArrayList<>();
        if (logs != null) {
            for (int i = 0; i < logs.size(); i += 2) {
                Log thriftLog = new Log();
                thriftLog.setTimestamp((long) logs.get(i));
                MutableSpan.LogEntry logEntry = (MutableSpan.LogEntry) logs.get(i + 1);
                if (logEntry.getFields() != null) {
                    thriftLog.setFields(JaegerThriftSpanConverter.buildTags(logEntry.getFields()));
                } else {
                    List<Tag> tags = new ArrayList<>();
                    if (logEntry.getMessage() != null) {
                        tags.add(buildTag("event", logEntry.getMessage()));
                    }
                    thriftLog.setFields(tags);
                }
                thriftLogs.add(thriftLog);
            }
        }
        return thriftLogs;
    }

    static Tag buildTag(String tagKey, Object tagValue) {
        Tag tag = new Tag();
        tag.setKey(tagKey);
        if (tagValue instanceof Integer || tagValue instanceof Short || tagValue instanceof Long) {
            tag.setVType(TagType.LONG);
            tag.setVLong(((Number) tagValue).longValue());
        } else if (tagValue instanceof Double || tagValue instanceof Float) {
            tag.setVType(TagType.DOUBLE);
            tag.setVDouble(((Number) tagValue).doubleValue());
        } else if (tagValue instanceof Boolean) {
            tag.setVType(TagType.BOOL);
            tag.setVBool((Boolean) tagValue);
        } else {
            buildStringTag(tag, tagValue);
        }
        return tag;
    }

    static void buildStringTag(Tag tag, Object tagValue) {
        tag.setVType(TagType.STRING);
        tag.setVStr(String.valueOf(tagValue));
    }

    static List<SpanRef> buildReferences(List<Reference> references) {
        List<SpanRef> thriftReferences = new ArrayList<SpanRef>(references.size());
        for (Reference reference : references) {
            SpanRefType thriftRefType = References.CHILD_OF.equals(reference.getType()) ? SpanRefType.CHILD_OF :
                    SpanRefType.FOLLOWS_FROM;
            thriftReferences.add(new SpanRef(thriftRefType, reference.getSpanContext().getTraceIdLow(),
                    reference.getSpanContext().getTraceIdHigh(), reference.getSpanContext().getSpanId()));
        }

        return thriftReferences;
    }

    public enum Consumer implements MutableSpan.TagConsumer<NetworkSpan.Builder>, MutableSpan.LogConsumer<NetworkSpan.Builder> {
        INSTANCE;

        @Override
        public void accept(NetworkSpan.Builder target, String key, Object value) {
            target.tag(key, value);
        }

        @Override
        public void accept(NetworkSpan.Builder target, long timestamp, MutableSpan.LogEntry value) {
            target.log(timestamp).log(value);
        }
    }
}
