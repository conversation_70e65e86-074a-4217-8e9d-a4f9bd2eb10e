package com.wosai.middleware.hera.agent.metrics;

import com.sun.net.httpserver.HttpServer;
import io.micrometer.prometheus.PrometheusMeterRegistry;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;

import java.io.Closeable;
import java.io.IOException;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

public class PrometheusExporter implements Closeable {
    private static final ILog LOGGER = LogManager.getLogger(PrometheusExporter.class);
    private final PrometheusMeterRegistry meterRegistry;
    private final ExecutorService executor;
    private final String path;
    private final int port;

    public PrometheusExporter(PrometheusMeterRegistry registry, String path, int port) {
        meterRegistry = registry;
        this.executor = Executors.newFixedThreadPool(5);
        this.path = path;
        this.port = port;
    }

    public void boot() {
        try {
            HttpServer server = HttpServer.create(new InetSocketAddress(this.port), 0);
            LOGGER.info("start endpoint with {} for Prometheus on port {}...", this.path, this.port);
            server.createContext(this.path, httpExchange -> {
                String response = meterRegistry.scrape();
                httpExchange.sendResponseHeaders(200, response.getBytes().length);
                try (OutputStream os = httpExchange.getResponseBody()) {
                    os.write(response.getBytes());
                } catch (IOException ioex) {
                    LOGGER.error(ioex, "fail to write response");
                }
            });
            server.setExecutor(this.executor);
            server.start();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void close() {
        if (executor != null) {
            try {
                executor.shutdown();
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException iex) {
                Thread.interrupted();
                LOGGER.error(iex, "shutdown executor failed with interruption");
            }
        }
    }
}
