package com.wosai.middleware.hera.agent.metrics.api;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.lang.Nullable;

import java.util.Collections;
import java.util.function.Supplier;
import java.util.function.ToDoubleFunction;

public class Gauge extends BaseMeter<io.micrometer.core.instrument.Gauge> {
    public static <T> Gauge.Builder<T> builder(String name, @Nullable T obj, ToDoubleFunction<T> f) {
        return new Gauge.Builder<T>(name, obj, f);
    }

    public static Gauge.Builder<Supplier<Number>> builder(String name, Supplier<Number> f) {
        return new Gauge.Builder<>(name, f, f2 -> {
            Number val = f2.get();
            return val == null ? Double.NaN : val.doubleValue();
        }).strongReference(true);
    }

    Gauge(io.micrometer.core.instrument.Gauge gauge) {
        super(gauge);
    }

    /**
     * The act of observing the value by calling this method triggers sampling of the
     * underlying number or user-defined function that defines the value for the gauge.
     *
     * @return The current value.
     */
    public double value() {
        return this.delegation.value();
    }

    @Override
    public Iterable<Measurement> measure() {
        return Collections.singletonList(new Measurement(this::value, Statistic.VALUE));
    }

    public static class Builder<T> extends AbstractBuilder<Builder<T>, Gauge> {
        @Nullable
        private final T obj;
        private final ToDoubleFunction<T> f;
        private boolean strongReference = false;

        Builder(String name, @Nullable T obj, ToDoubleFunction<T> f) {
            super(name);
            this.obj = obj;
            this.f = f;
        }

        public Builder<T> strongReference(boolean strongReference) {
            this.strongReference = strongReference;
            return this;
        }

        @Override
        protected Gauge create(MeterRegistry registry) {
            return new Gauge(io.micrometer.core.instrument.Gauge.builder(name, obj, f)
                    .strongReference(strongReference)
                    .description(description)
                    .tags(tags)
                    .baseUnit(baseUnit)
                    .register(registry));
        }
    }
}
