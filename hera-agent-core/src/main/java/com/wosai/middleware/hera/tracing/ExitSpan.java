package com.wosai.middleware.hera.tracing;

import com.wosai.middleware.hera.tracing.internal.handler.MutableSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import io.opentracing.propagation.Format;
import io.opentracing.tag.Tag;

public class ExitSpan extends StackBasedSpan implements ExitTypeSpan {
    public ExitSpan(HeraTracer tracer, HeraSpanContext context, MutableSpan state) {
        this(tracer, context, state, Kind.CLIENT);
    }

    public ExitSpan(HeraTracer tracer, HeraSpanContext context, MutableSpan state, Kind kind) {
        super(tracer, context, state);
        this.kind(kind);
    }

    @Override
    public <T> ExitSpan setTag(Tag<T> tag, T value) {
        if (refCount() == 1 || this.isInAsyncMode) {
            super.setTag(tag, value);
        }
        return this;
    }

    @Override
    public ExitSpan setTag(String key, String value) {
        if (refCount() == 1 || this.isInAsyncMode) {
            super.setTag(key, value);
        }
        return this;
    }

    @Override
    public ExitSpan setTag(String key, boolean value) {
        if (refCount() == 1 || this.isInAsyncMode) {
            super.setTag(key, value);
        }
        return this;
    }

    @Override
    public ExitSpan setTag(String key, Number value) {
        if (refCount() == 1 || this.isInAsyncMode) {
            super.setTag(key, value);
        }
        return this;
    }

    @Override
    public ExitSpan error(Throwable t) {
        super.error(t);
        return this;
    }

    @Override
    public ExitSpan setOperationName(String operationName) {
        return (refCount() != 1 && !this.isInAsyncMode) ? this : (ExitSpan) super.setOperationName(operationName);
    }

    @Override
    public String getPeer() {
        String remoteService = this.state.remoteServiceName();
        if (remoteService != null) {
            return remoteService;
        }
        if (this.state.remoteIp() != null) {
            return this.state.remoteIp() + ":" + this.state.remotePort();
        } else {
            return "";
        }
    }

    @Override
    public <T> ExitTypeSpan inject(Format<T> format, T carrier) {
        this.tracer.inject(this.context, format, carrier);
        return this;
    }

    @Override
    boolean isEntry() {
        return false;
    }

    @Override
    public boolean isExit() {
        return true;
    }
}
