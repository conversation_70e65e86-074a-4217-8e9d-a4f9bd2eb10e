package com.wosai.middleware.hera.agent.profiling;

import com.wosai.middleware.hera.agent.commands.CommandService;
import com.wosai.middleware.hera.util.ThreadPoolUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.skywalking.apm.agent.core.boot.BootService;
import org.apache.skywalking.apm.agent.core.boot.DefaultNamedThreadFactory;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;

import java.time.Duration;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

public abstract class AbstractProfilingTaskExecutionService<T extends BaseProfileTask, CONTEXT extends TaskAccessor<T>> implements BootService {
    private static final ILog LOGGER = LogManager.getLogger(AbstractProfilingTaskExecutionService.class);
    // current processing profile task context
    // Only one task can be run at the same time
    protected final AtomicReference<CONTEXT> taskExecutionContext = new AtomicReference<>();

    private final ScheduledExecutorService schedExecutorService;

    protected AbstractProfilingTaskExecutionService(String name) {
        this.schedExecutorService = Executors.newSingleThreadScheduledExecutor(
                new DefaultNamedThreadFactory(name));
    }

    public void addTask(T task) {
        ServiceManager.INSTANCE.findService(CommandService.class)
                .updateLastCommandCreateTime(task.getCreateTime());

        final CheckResult dataError = validateTask(task);

        if (!dataError.isSuccess()) {
            LOGGER.warn("check command error, cannot process this profile task. reason: {}", dataError.getErrorReason());
            return;
        }
        beforeStart(task);

        schedExecutorService.schedule(() -> startTask(task), task.startDelayMillis(), TimeUnit.MILLISECONDS);
    }

    protected void startTask(final T task) {
        // make sure the previous profile task has already been stopped
        stopTask(taskExecutionContext.get());
        CONTEXT context = null;
        try {
            context = doStart(task);
            taskExecutionContext.set(context);
        } catch (Exception ex) {
            LOGGER.error(ex, "fail to start task");
            return;
        }
        final CONTEXT fCtx = context;
        Duration dur = task.getTaskSampleTime();
        // register a task to stop the current task after a specified duration
        schedExecutorService.schedule(
                () -> stopTask(fCtx), dur.getSeconds(), TimeUnit.SECONDS);
    }

    public void stopTask(CONTEXT context) {
        // stop same context only
        if (context == null || !taskExecutionContext.compareAndSet(context, null)) {
            return;
        }
        doStop(context);
    }

    /**
     * Stop the profiling task
     */
    protected abstract void doStop(CONTEXT context);

    /**
     * Start the profiling task
     */
    protected abstract CONTEXT doStart(T task) throws Exception;

    /**
     * Before start the profiling task, can do some processing
     */
    protected void beforeStart(T task) {
    }

    protected abstract CheckResult validateTask(T task);

    protected void onShutdown() {
        ThreadPoolUtil.gracefulShutdown(schedExecutorService, 10);
    }

    /**
     * check profile task is processable
     */
    @RequiredArgsConstructor
    @Getter
    public static class CheckResult {
        private static final CheckResult SUCCESS = new CheckResult(true, null);

        private final boolean success;
        private final String errorReason;

        public static CheckResult withError(final String errorReason) {
            return new CheckResult(false, errorReason);
        }

        public static CheckResult ok() {
            return SUCCESS;
        }
    }
}
