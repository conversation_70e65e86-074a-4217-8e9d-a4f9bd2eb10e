package com.wosai.middleware.hera.agent.sentinel;

import org.apache.skywalking.apm.agent.core.util.CollectionUtil;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class ParamFlowRule extends AbstractRule<com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowRule> {
    public ParamFlowRule(com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowRule paramFlowRule) {
        super(paramFlowRule);
    }

    public int getControlBehavior() {
        return this.delegation.getControlBehavior();
    }

    public int getMaxQueueingTimeMs() {
        return this.delegation.getMaxQueueingTimeMs();
    }

    public int getBurstCount() {
        return this.delegation.getBurstCount();
    }

    public long getDurationInSec() {
        return this.delegation.getDurationInSec();
    }

    public int getGrade() {
        return this.delegation.getGrade();
    }

    public Integer getParamIdx() {
        return this.delegation.getParamIdx();
    }

    public double getCount() {
        return this.delegation.getCount();
    }

    public boolean isClusterMode() {
        return this.delegation.isClusterMode();
    }

    public ParamFlowClusterConfig getClusterConfig() {
        return this.delegation.getClusterConfig() == null ? null : new ParamFlowClusterConfig(this.delegation.getClusterConfig());
    }

    public List<ParamFlowItem> getParamFlowItemList() {
        if (CollectionUtil.isEmpty(this.delegation.getParamFlowItemList())) {
            return Collections.EMPTY_LIST;
        }
        return this.delegation.getParamFlowItemList().stream().map(ParamFlowItem::new).collect(Collectors.toList());
    }

    public static class ParamFlowClusterConfig extends ObjectDelegation<com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowClusterConfig> {
        public ParamFlowClusterConfig(com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowClusterConfig delegation) {
            super(delegation);
        }

        public Long getFlowId() {
            return this.delegation.getFlowId();
        }

        public int getThresholdType() {
            return this.delegation.getThresholdType();
        }

        public boolean isFallbackToLocalWhenFail() {
            return this.delegation.isFallbackToLocalWhenFail();
        }

        public int getSampleCount() {
            return this.delegation.getSampleCount();
        }

        public int getWindowIntervalMs() {
            return this.delegation.getWindowIntervalMs();
        }
    }

    public static class ParamFlowItem extends ObjectDelegation<com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowItem> {
        public ParamFlowItem(com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowItem delegation) {
            super(delegation);
        }

        public String getObject() {
            return this.delegation.getObject();
        }

        public Integer getCount() {
            return this.delegation.getCount();
        }

        public String getClassType() {
            return this.delegation.getClassType();
        }
    }
}
