package com.wosai.middleware.hera.agent.sentinel;

public abstract class AbstractRule<T extends com.alibaba.csp.sentinel.slots.block.AbstractRule> extends ObjectDelegation<T> {
    protected AbstractRule(T delegation) {
        super(delegation);
    }

    public String getResource() {
        return this.delegation.getResource();
    }

    public String getLimitApp() {
        return this.delegation.getLimitApp();
    }

    public Long getId() {
        return this.delegation.getId();
    }

    static AbstractRule<?> create(com.alibaba.csp.sentinel.slots.block.AbstractRule rule) {
        if (rule instanceof com.alibaba.csp.sentinel.slots.block.flow.FlowRule) {
            return new FlowRule((com.alibaba.csp.sentinel.slots.block.flow.FlowRule) rule);
        } else if (rule instanceof com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule) {
            return new DegradeRule((com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule) rule);
        } else if (rule instanceof com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowRule) {
            return new ParamFlowRule((com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowRule) rule);
        }
        throw new UnsupportedOperationException("unsupported rule type: " + rule.getClass());
    }
}
