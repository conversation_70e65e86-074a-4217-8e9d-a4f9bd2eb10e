package com.wosai.middleware.hera.agent.metrics.handlers;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class EmptyAuthenticator implements MetricsEndpointAuthenticator {
    public static final MetricsEndpointAuthenticator INSTANCE = new EmptyAuthenticator();

    @Override
    public boolean checkToken(String requestToken) {
        return true;
    }
}
