package com.wosai.middleware.hera.agent.services;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.protobuf.ByteString;
import com.shouqianba.middleware.hera.network.arthas.v1.ApiAction;
import com.shouqianba.middleware.hera.network.arthas.v1.ApiState;
import com.shouqianba.middleware.hera.network.arthas.v1.CommandRequest;
import com.shouqianba.middleware.hera.network.arthas.v1.ExecStatus;
import com.shouqianba.middleware.hera.network.arthas.v1.JobInfo;
import com.shouqianba.middleware.hera.network.arthas.v1.ResultModel;
import com.wosai.middleware.hera.agent.attach.HeraArthasAgent;
import com.wosai.middleware.hera.agent.commands.RemoteCommandContextKey;
import com.wosai.middleware.hera.network.component.command.RemoteCommandTaskCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.agent.core.boot.AgentPackagePath;
import org.apache.skywalking.apm.agent.core.boot.BootService;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;

import java.arthas.AgentAPI;
import java.io.File;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class RemoteCommandTaskExecutionService implements BootService {
    private static final Gson GSON = new Gson();
    private static final Map<String, String> RESULT_MODEL_TYPE_MAP = ImmutableMap.<String, String>builder()
            .put("classloader", "ClassLoader")
            .put("command", "CommandRequest")
            .put("dump", "DumpClass")
            .put("getstatic", "GetStatic")
            .put("heapdump", "HeapDump")
            .put("input_status", "InputStatus")
            .put("row_affect", "RowAffect")
            .put("perfcounter", "PerfCounter")
            .put("mc", "MemoryCompiler")
            .put("mbean", "MBean")
            .put("jfr", "JFR")
            .put("sc", "SearchClass")
            .put("sm", "SearchMethod")
            .put("sysenv", "SystemEnv")
            .put("sysprop", "SystemProperty")
            .put("tt", "TimeTunnel")
            .put("vmoption", "VMOption")
            .put("vmtool", "VmTool")
            .put("job_status", "JobStatus")
            .build();
    private RemoteCommandService remoteCommandService;

    @Override
    public void prepare() throws Throwable {
        remoteCommandService = ServiceManager.INSTANCE.findService(RemoteCommandService.class);
    }

    @Override
    public void boot() throws Throwable {

    }

    @Override
    public void onComplete() throws Throwable {

    }

    @Override
    public void shutdown() throws Throwable {

    }

    private void attachArthasAgent() {
        try {
            File agentDictionary = AgentPackagePath.getPath();
            HeraArthasAgent.attach(new File(agentDictionary, "arthas").getAbsolutePath(), Preconditions.checkNotNull(AgentAPI.getInstrumentation()));
        } catch (Throwable t) {
            log.error("arthas bootstrap build failure.", t);
        }
    }

    public void executeRemoteCommandTask(RemoteCommandTaskCommand task) {
        log.info("executeRemoteCommandTask: {}", task);

        if (!task.validate()) {
            throw new IllegalArgumentException("remote command is invalid");
        }

        attachArthasAgent();

        final String command = task.getRemoteCommand();
        AgentAPI.AgentRequest req = new AgentAPI.AgentRequest();
        req.setCommand(command);
        req.setAction(getAction(task.getAction()));
        req.setRequestId(task.getSerialNumber());
        req.setSessionId(task.getSessionId());
        final String replyTo = task.attr(RemoteCommandContextKey.REPLY_TO);
        req.setConsumerId(task.getConsumerId());
        AgentAPI.execute(req, new AgentAPI.ResponseHandler() {
            @Override
            public void onCallback(AgentAPI.AgentResponse response) {
                log.info("onCallback: {}", response);
                CommandRequest.Builder bld = CommandRequest.newBuilder();
                if (response.getMessage() != null) {
                    bld.setMessage(response.getMessage());
                }
                switch (response.getState()) {
                    case SUCCEEDED:
                        bld.setState(ApiState.API_STATE_SUCCEEDED);
                        break;
                    case FAILED:
                        bld.setState(ApiState.API_STATE_FAILED);
                        break;
                    case INTERRUPTED:
                        bld.setState(ApiState.API_STATE_INTERRUPTED);
                        break;
                    case REFUSED:
                        bld.setState(ApiState.API_STATE_REFUSED);
                        break;
                    case SCHEDULED:
                        bld.setState(ApiState.API_STATE_SCHEDULED);
                        break;
                }
                String dataStr = response.getBody();
                if (!Strings.isNullOrEmpty(dataStr)) {
                    JsonElement jsonElement = GSON.fromJson(dataStr, JsonElement.class);
                    if (jsonElement.isJsonObject()) {
                        JobInfo.Builder bodyBld = JobInfo.newBuilder();
                        JsonObject data = jsonElement.getAsJsonObject();

                        Optional.ofNullable(data.get("command")).ifPresent(command -> {
                            bodyBld.setCommand(command.getAsString());
                        });
                        Optional.ofNullable(data.get("jobId")).ifPresent(jobId -> {
                            bodyBld.setJobId(jobId.getAsNumber().intValue());
                        });
                        Optional.ofNullable(data.get("jobStatus")).ifPresent(jobStatusStr -> {
                            String jobStatus = jobStatusStr.getAsString();
                            if ("READY".equals(jobStatus)) {
                                bodyBld.setJobStatus(ExecStatus.EXEC_STATUS_READY);
                            } else if ("RUNNING".equals(jobStatus)) {
                                bodyBld.setJobStatus(ExecStatus.EXEC_STATUS_RUNNING);
                            } else if ("STOPPED".equals(jobStatus)) {
                                bodyBld.setJobStatus(ExecStatus.EXEC_STATUS_STOPPED);
                            } else if ("TERMINATED".equals(jobStatus)) {
                                bodyBld.setJobStatus(ExecStatus.EXEC_STATUS_TERMINATED);
                            }
                        });
                        // Null check
                        Optional<JsonArray> results = Optional.ofNullable(data.get("results")).map(JsonElement::getAsJsonArray);
                        if (results.isPresent() && !results.get().isEmpty()) {
                            for (final JsonElement result : results.get()) {
                                // Null check
                                Optional<String> type = Optional.ofNullable(result.getAsJsonObject().get("type")).map(JsonElement::getAsString);
                                if (!type.isPresent()) {
                                    continue;
                                }

                                // upload heap dump to hera-meta
                                if ("heapdump".equals(type.get())) {
                                    JsonObject json = result.getAsJsonObject();
                                    String dumpFilePath = json.get("dumpFile").getAsString();
                                    String url = remoteCommandService.uploadFile(dumpFilePath);
                                    // add MessageModel to show upload result
                                    JsonObject messageModel = new JsonObject();
                                    String message = (url != null && !"".equals(url)) ? "Upload success, download file: " + url : "Upload failed";
                                    messageModel.addProperty("jobId", json.get("jobId").getAsNumber().intValue());
                                    messageModel.addProperty("message", message);
                                    messageModel.addProperty("type", "message");
                                    bodyBld.addResults(ResultModel.newBuilder()
                                            .setTypeUrl("type.googleapis.com/com.taobao.arthas.core.command.model.MessageModel")
                                            .setData(ByteString.copyFromUtf8(GSON.toJson(messageModel)))
                                            .build());
                                }

                                bodyBld.addResults(ResultModel.newBuilder()
                                        .setTypeUrl("type.googleapis.com/com.taobao.arthas.core.command.model." +
                                                RESULT_MODEL_TYPE_MAP.getOrDefault(type.get(), capitalize(type.get())) + "Model")
                                        // Serialize from ResultModel (JSONObject) to ObjectModel (JSON)
                                        .setData(ByteString.copyFromUtf8(GSON.toJson(result)))
                                        .build());
                            }
                        }
                        bld.setJobInfo(bodyBld);
                    }
                }

                bld.setAction(task.getAction());
                bld.setRequestId(response.getRequestId());
                // only INIT_SESSION
                if (!Strings.isNullOrEmpty(response.getConsumerId())) {
                    bld.setConsumerId(response.getConsumerId());
                }
                // for INTERRUPT_JOB, it could be NULL
                if (!Strings.isNullOrEmpty(response.getSessionId())) {
                    bld.setSessionId(response.getSessionId());
                }
                if (!Strings.isNullOrEmpty(command)) {
                    bld.setCommand(command);
                }
                bld.setClientId(replyTo);
                remoteCommandService.replyRemoteCommand(bld);
            }
        });
    }

    static String capitalize(String str) {
        if (Strings.isNullOrEmpty(str)) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    static String getAction(ApiAction apiAction) {
        switch (apiAction) {
            case API_ACTION_EXEC:
                return "exec";
            case API_ACTION_ASYNC_EXEC:
                return "async_exec";
            case API_ACTION_CLOSE_SESSION:
                return "close_session";
            case API_ACTION_INIT_SESSION:
                return "init_session";
            case API_ACTION_SESSION_INFO:
                return "session_info";
            case API_ACTION_JOIN_SESSION:
                return "join_session";
            case API_ACTION_PULL_RESULTS:
                return "pull_results";
            case API_ACTION_INTERRUPT_JOB:
                return "interrupt_job";
            case API_ACTION_SUSPEND_JOB:
                return "suspend_job";
            default:
                throw new IllegalArgumentException("unknown api action: " + apiAction);
        }
    }
}
