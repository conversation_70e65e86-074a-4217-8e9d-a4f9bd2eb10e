package com.wosai.middleware.hera.agent.tracing;

import com.wosai.middleware.hera.agent.services.TraceReportService;
import com.wosai.middleware.hera.tracing.internal.handler.FinishedSpanHandler;
import com.wosai.middleware.hera.tracing.network.JaegerReporter;
import io.jaegertracing.internal.metrics.Metrics;
import io.jaegertracing.thrift.internal.senders.ThriftSender;

public class JaegerBufferedReporter extends JaegerReporter {
    private final TraceReportService traceReportService;

    public JaegerBufferedReporter(TraceReportService traceReportService, ThriftSender sender, int reportQueueSize, int closeEnqueueTimeout, int flushInterval, Metrics metrics) {
        super(sender, reportQueueSize, closeEnqueueTimeout, flushInterval, metrics);
        this.traceReportService = traceReportService;
    }

    @Override
    public FinishedSpanHandler createSpanHandler() {
        return traceReportService;
    }
}
