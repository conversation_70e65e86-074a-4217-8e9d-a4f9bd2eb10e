package com.wosai.middleware.hera.agent.conf.dynamic.watcher;

import com.google.common.collect.ImmutableSet;
import com.wosai.middleware.hera.agent.conf.dynamic.AbstractAgentConfigChangeWatcher;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

import static com.google.common.base.Preconditions.checkNotNull;

public class StatusCheckWatcher extends AbstractAgentConfigChangeWatcher {
    private static final String SC_MAX_RECURSIVE_DEPTH_KEY = "statuscheck.max_recursive_depth";
    private static final String SC_IGNORED_EXCEPTIONS_KEY = "statuscheck.ignored_exceptions";

    private final StatusCheckerConfig defaultConfig;
    private final Listener listener;
    private volatile StatusCheckerConfig currentConfig;

    public StatusCheckWatcher(Listener listener, final StatusCheckerConfig defaultConfig) {
        super(ImmutableSet.of(SC_MAX_RECURSIVE_DEPTH_KEY, SC_IGNORED_EXCEPTIONS_KEY));
        this.listener = checkNotNull(listener, "listener");
        this.defaultConfig = defaultConfig;
        this.currentConfig = this.defaultConfig.toBuilder().build();
    }

    @Override
    public void notify(List<ConfigChangeEvent> events) {
        StatusCheckerConfig.StatusCheckerConfigBuilder builder = this.currentConfig.toBuilder();
        for (ConfigChangeEvent event : events) {
            if (SC_MAX_RECURSIVE_DEPTH_KEY.equals(event.getKey())) {
                if (EventType.DELETE.equals(event.getEventType())) {
                    builder.maxRecursiveDepth(defaultConfig.getMaxRecursiveDepth());
                } else {
                    builder.maxRecursiveDepth(Integer.parseInt(event.getNewValue()));
                }
            } else if (SC_IGNORED_EXCEPTIONS_KEY.equals(event.getKey())) {
                if (EventType.DELETE.equals(event.getEventType())) {
                    builder.ignoredExceptions(defaultConfig.getIgnoredExceptions());
                } else {
                    builder.ignoredExceptions(event.getNewValue());
                }
            }
        }
        final StatusCheckerConfig config = builder.build();
        this.listener.onChange(config);
        this.currentConfig = config;
    }

    @Builder(toBuilder = true)
    @Getter
    public static class StatusCheckerConfig {
        private final int maxRecursiveDepth;
        private final String ignoredExceptions;
    }

    public interface Listener {
        void onChange(StatusCheckerConfig data);
    }
}
