package com.wosai.middleware.hera.tracing;

import com.wosai.middleware.hera.agent.services.ContextManager;
import com.wosai.middleware.hera.tracing.internal.handler.MutableSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import io.opentracing.tag.Tag;

public class EntrySpan extends StackBasedSpan {
    private int currentMaxDepth = 1;

    public EntrySpan(HeraTracer tracer, HeraSpanContext context, MutableSpan state) {
        this(tracer, context, state, Kind.SERVER);
    }

    public EntrySpan(HeraTracer tracer, HeraSpanContext context, MutableSpan state, Kind kind) {
        super(tracer, context, state);
        this.kind(kind);
    }

    @Override
    public EntrySpan setTag(String key, String value) {
        if (this.stackDepth == this.currentMaxDepth || this.isInAsyncMode) {
            super.setTag(key, value);
        }

        return this;
    }

    @Override
    public EntrySpan setTag(String key, boolean value) {
        if (this.stackDepth == this.currentMaxDepth || this.isInAsyncMode) {
            super.setTag(key, value);
        }

        return this;
    }

    @Override
    public EntrySpan setTag(String key, Number value) {
        if (this.stackDepth == this.currentMaxDepth || this.isInAsyncMode) {
            super.setTag(key, value);
        }

        return this;
    }

    @Override
    public <T> EntrySpan setTag(Tag<T> tag, T value) {
        if (this.stackDepth == this.currentMaxDepth || this.isInAsyncMode) {
            super.setTag(tag, value);
        }

        return this;
    }

    @Override
    public EntrySpan setOperationName(String operationName) {
        if (this.stackDepth != this.currentMaxDepth && !this.isInAsyncMode) {
            return this;
        }
        // recheck profiling since operation name may be changed
        ContextManager.profilingRecheck(this, operationName);
        return (EntrySpan) super.setOperationName(operationName);
    }

    @Override
    public int ref() {
        this.clearWhenRestart();
        this.currentMaxDepth++;
        return super.ref();
    }

    void clearWhenRestart() {
        synchronized (this.state) {
            this.state.clear();
        }
    }

    int getCurrentMaxDepth() {
        return currentMaxDepth;
    }

    @Override
    boolean isEntry() {
        return true;
    }

    @Override
    public boolean isExit() {
        return false;
    }
}
