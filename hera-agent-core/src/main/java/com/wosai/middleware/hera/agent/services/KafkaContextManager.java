package com.wosai.middleware.hera.agent.services;

import com.google.common.base.Strings;
import com.wosai.middleware.hera.agent.conf.HeraConfig;
import com.wosai.middleware.hera.agent.mesh.Constants;
import com.wosai.middleware.hera.agent.mesh.kafka.KafkaTopicState;
import com.wosai.middleware.hera.util.CopyOnWriteMap;
import com.wosai.middleware.hera.util.KafkaClusterHasher;
import io.grpc.xds.SimpleControlPlaneBootstrapper;
import org.apache.skywalking.apm.agent.core.boot.BootService;
import org.apache.skywalking.apm.agent.core.boot.DefaultImplementor;
import org.apache.skywalking.apm.agent.core.conf.Config;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import com.google.common.collect.Lists;

import java.util.concurrent.ConcurrentMap;

import static com.wosai.middleware.hera.agent.conf.HeraConfig.Mesh.CONTROL_PLANE;

@DefaultImplementor
public class KafkaContextManager implements BootService {
    private static final ILog LOGGER = LogManager.getLogger(KafkaContextManager.class);
    // By default, it is disabled.
    private static boolean ENABLED = false;
    private static final ConcurrentMap<String, KafkaTopicState> TOPIC_STATE = new CopyOnWriteMap<>();
    private static SimpleControlPlaneBootstrapper BOOTSTRAPPER = null;
    // used between groupID and fromEnv
    private static final String DELIMITER = "_";

    public KafkaContextManager() {
        if (HeraConfig.Mesh.Kafka.ENABLE_SWIMLANE) {
            // TODO: shall we check RunLevel? It may prevent us from debugging locally.
            if (!Strings.isNullOrEmpty(HeraConfig.Kubernetes.Pod.VERSION)) {
                ENABLED = true;
            }
        }
    }

    @Override
    public void prepare() throws Throwable {

    }

    @Override
    public void boot() throws Throwable {
        if (ENABLED && deployedInBaseSwimlane()) {
            BOOTSTRAPPER = new SimpleControlPlaneBootstrapper(Lists.newArrayList(CONTROL_PLANE),
                    Config.Agent.SERVICE_NAME, Config.Agent.INSTANCE_NAME, HeraConfig.Kubernetes.Pod.VERSION);
        }
    }

    @Override
    public void onComplete() throws Throwable {

    }

    @Override
    public void shutdown() throws Throwable {

    }

    public static boolean enabled() {
        return ENABLED;
    }

    /**
     * Get a new group-id if the swimlane feature is enabled.
     * For consumers deployed in the base env, no suffix will be appended.
     *
     * @param originalGroupId the original "group.id" set in the Consumer Properties
     * @return a transformed group.id
     */
    public static String getGroupId(String originalGroupId) {
        if (!ENABLED) {
            return originalGroupId;
        }
        if (Strings.isNullOrEmpty(originalGroupId) || originalGroupId.endsWith(DELIMITER + HeraConfig.Kubernetes.Pod.VERSION)) {
            return originalGroupId;
        }
        if (Constants.ENV_BASE.equals(HeraConfig.Kubernetes.Pod.VERSION)) {
            LOGGER.info("use original group.id for consumers in the base swimlane");
            return originalGroupId;
        }
        LOGGER.info("use a group-id suffix _{} in swimlane", HeraConfig.Kubernetes.Pod.VERSION);
        return getGroupId(originalGroupId, HeraConfig.Kubernetes.Pod.VERSION);
    }

    static String getGroupId(String originalGroupId, String env) {
        return originalGroupId + DELIMITER + env;
    }

    /**
     * Subscribe topic.
     * {@link KafkaTopicState} will not be created if this feature has been disabled or the current
     * instance is not deployed in the base swimlane.
     *
     * @param topic            the topic to be subscribed.
     * @param bootstrapServers the bootstrap servers of the brokers.
     */
    public static void subscribe(String topic, String bootstrapServers) {
        if (!ENABLED || !deployedInBaseSwimlane()) {
            return;
        }
        LOGGER.debug("subscribe {} on brokers {}", topic, bootstrapServers);
        TOPIC_STATE.computeIfAbsent(formatStateKey(topic, bootstrapServers), kct ->
                new KafkaTopicState(topic, KafkaClusterHasher.hash(bootstrapServers), BOOTSTRAPPER)).ref();
    }

    /**
     * Unsubscribe topic.
     *
     * @param topic            the topic to be subscribed.
     * @param bootstrapServers the bootstrap servers of the brokers.
     */
    public static void unsubscribe(String topic, String bootstrapServers) {
        if (!ENABLED || !deployedInBaseSwimlane()) {
            return;
        }
        LOGGER.debug("subscribe {} on brokers {}", topic, bootstrapServers);
        KafkaTopicState state = TOPIC_STATE.get(formatStateKey(topic, bootstrapServers));
        if (state == null) {
            LOGGER.warn("unsubscribe called on a unknown topic {}", topic);
            return;
        }
        if (state.unref()) {
            LOGGER.debug("TODO: we should destroy the state");
            // TODO: check refCount in order to delete the entry
            // state.close();
        }
    }

    /**
     * This method checks if there is a KafkaConsumer deployed in the specific swimlane
     * is currently an active member of the given topic.
     *
     * @param clusterID       the clusterID of the brokers. This must be precalculated.
     * @param topic           the kafka topic which is currently consuming
     * @param originalGroupId the original group-id
     * @param fromEnv         the env-flag set in the message header
     * @return if we should consume the given message
     */
    public static boolean shouldConsume(String clusterID, String topic, String originalGroupId, String fromEnv) {
        if (!ENABLED) {
            // do not skip any message if this feature is disabled
            return true;
        }
        // It could happen if the upstream does not enable env-flag, `base` can be assumed
        if (Strings.isNullOrEmpty(fromEnv)) {
            fromEnv = Constants.ENV_BASE;
        }
        // if the message is from the same swimlane
        if (fromEnv.equals(HeraConfig.Kubernetes.Pod.VERSION)) {
            return true;
        }
        // 1. If the application is deployed in the non-base swimlane
        if (!Constants.ENV_BASE.equals(HeraConfig.Kubernetes.Pod.VERSION)) {
            return Constants.ENV_BASE.equals(fromEnv) && HeraConfig.Mesh.Kafka.FORCE_CONSUME_BASE; // if the message is from base and force consume base
        }
        // 2. It is deployed in the base swimlane, we have to check the state of the consumer group
        KafkaTopicState state = TOPIC_STATE.get(assembleStateKey(topic, clusterID));
        if (state == null) {
            return true;
        }
        // check if consumer group ({group_id}{delimiter}{fromEnv}) exists
        return !state.memberExists(getGroupId(originalGroupId, fromEnv));
    }

    static boolean deployedInBaseSwimlane() {
        return Constants.ENV_BASE.equals(HeraConfig.Kubernetes.Pod.VERSION);
    }

    static String formatStateKey(String topic, String bootstrapServers) {
        return assembleStateKey(topic, KafkaClusterHasher.hash(bootstrapServers));
    }

    public static String assembleStateKey(String topic, String clusterID) {
        return topic + "@" + clusterID;
    }
}
