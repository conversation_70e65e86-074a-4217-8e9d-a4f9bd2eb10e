package com.wosai.middleware.hera.agent.services;

import com.wosai.middleware.hera.agent.conf.HeraConfig;
import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.tracing.ReporterProvider;
import com.wosai.middleware.hera.agent.tracing.SamplerProvider;
import com.wosai.middleware.hera.agent.tracing.TracerProvider;
import com.wosai.middleware.hera.tracing.HeraScopeManager;
import com.wosai.middleware.hera.tracing.HeraTracer;
import com.wosai.middleware.hera.tracing.internal.handler.FinishedSpanHandler;
import com.wosai.middleware.hera.tracing.internal.handler.JaegerFinishedSpanHandler;
import com.wosai.middleware.hera.tracing.internal.handler.MutableSpan;
import com.wosai.middleware.hera.tracing.internal.handler.NoopAwareFinishedSpanHandler;
import com.wosai.middleware.hera.tracing.network.NetworkSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import com.wosai.middleware.hera.tracing.spi.Reporter;
import io.jaegertracing.internal.metrics.Metrics;
import org.apache.skywalking.apm.agent.core.boot.BootService;
import org.apache.skywalking.apm.agent.core.boot.DefaultImplementor;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.commons.datacarrier.DataCarrier;
import org.apache.skywalking.apm.commons.datacarrier.buffer.BufferStrategy;
import org.apache.skywalking.apm.commons.datacarrier.consumer.IConsumer;

import java.io.IOException;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.google.common.base.Preconditions.checkNotNull;

@DefaultImplementor
public class TraceReportService extends FinishedSpanHandler implements BootService, IConsumer<TraceReportService.Payload> {
    private static final ILog LOGGER = LogManager.getLogger(TraceReportService.class);
    private DataCarrier<Payload> buffer;
    private Reporter<NetworkSpan> reporter;
    private Metrics metrics;
    private HeraTracer heraTracer;
    private FinishedSpanHandler handler;
    private final Set<FinishedSpanHandler> registerHandlers = Collections.synchronizedSet(new LinkedHashSet<>());
    private final AtomicBoolean sealed = new AtomicBoolean(false);

    @Override
    public void init(Properties properties) {
    }

    @Override
    public void prepare() throws Throwable {
    }

    @Override
    public void boot() {
        if (!this.sealed.compareAndSet(false, true)) {
            throw new IllegalStateException("invalid seal state");
        }
        metrics = checkNotNull(ServiceManager.INSTANCE.findService(MetricsHandler.class)).getJaegerMetricsRegistry();
        reporter = new ReporterProvider(this, metrics).get();
        AtomicBoolean noop = new AtomicBoolean();
        handler = reportingFinishedSpanHandler(registerHandlers, new JaegerFinishedSpanHandler(reporter), noop);
        LOGGER.info("create DataCarrier with {} channels and {} buffer capacity", HeraConfig.Jaeger.Reporter.CHANNEL_SIZE, HeraConfig.Jaeger.Reporter.MAX_QUEUE_SIZE);
        buffer = new DataCarrier<>(HeraConfig.Jaeger.Reporter.CHANNEL_SIZE, HeraConfig.Jaeger.Reporter.MAX_QUEUE_SIZE, BufferStrategy.IF_POSSIBLE);
        // start consumers
        buffer.consume(this, 1);
    }

    @Override
    public void onComplete() throws Throwable {
        heraTracer = new TracerProvider(new SamplerProvider(metrics).get(),
                metrics,
                HeraScopeManager.getInstance(),
                reporter).get();
        // set tracer to ReportService
        ContextManager.installTracer(heraTracer);
    }

    @Override
    public void shutdown() {
        LOGGER.info("terminating DataCarrier...");
        buffer.shutdownConsumers();
        try {
            LOGGER.info("closing span reporter...");
            this.reporter.close();
        } catch (IOException ioe) {
            LOGGER.error(ioe, "shutdown reporter failed");
        }
    }

    @Override
    public void consume(List<Payload> list) {
        for (Payload data : list) {
            try {
                final HeraSpanContext context = data.context;
                final MutableSpan span = data.span;

                // call handler
                this.handler.handle(context, span);
            } catch (Exception sex) {
                LOGGER.error("Append span failed", sex);
            }
        }
    }

    @Override
    public void onError(List<Payload> data, Throwable t) {
        LOGGER.error(t, "Try to process {} trace segments to collector, with unexpected exception.", data.size());
    }

    @Override
    public void onExit() {
    }

    @Override
    public boolean handle(HeraSpanContext context, MutableSpan span) {
        if (!this.buffer.produce(Payload.of(context, span))) {
            LOGGER.warn("fail to produce payload, dropped");
        }
        return true;
    }

    public void registerFinishedHandler(FinishedSpanHandler finishedSpanHandler) {
        if (sealed.get()) {
            throw new IllegalStateException("the handler set is already sealed");
        }
        this.registerHandlers.add(finishedSpanHandler);
    }

    static final class Payload {
        private final HeraSpanContext context;
        private final MutableSpan span;

        private Payload(HeraSpanContext context, MutableSpan span) {
            this.context = context;
            this.span = span;
        }

        static Payload of(HeraSpanContext context, MutableSpan span) {
            return new Payload(context, span);
        }
    }

    static FinishedSpanHandler reportingFinishedSpanHandler(
            Set<FinishedSpanHandler> input, FinishedSpanHandler reportHandler, AtomicBoolean noop) {
        Set<FinishedSpanHandler> spanHandlers = new LinkedHashSet<>(input);
        // When present, the Zipkin handler is invoked after the user-supplied finished span handlers.
        if (reportHandler != null) {
            spanHandlers.add(reportHandler);
        }

        // Make sure any exceptions caused by handlers don't crash callers
        FinishedSpanHandler finishedSpanHandler = NoopAwareFinishedSpanHandler.create(spanHandlers, noop);
        if (finishedSpanHandler == FinishedSpanHandler.NOOP) {
            finishedSpanHandler = new HeraTracer.LogFinishedSpanHandler();
        }
        return finishedSpanHandler;
    }
}
