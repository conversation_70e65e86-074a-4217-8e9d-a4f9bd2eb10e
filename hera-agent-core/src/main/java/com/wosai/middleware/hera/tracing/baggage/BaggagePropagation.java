package com.wosai.middleware.hera.tracing.baggage;

import brave.internal.collect.Lists;
import com.google.common.collect.ImmutableSet;
import com.wosai.middleware.hera.tracing.internal.baggage.BaggageCodec;
import com.wosai.middleware.hera.tracing.internal.baggage.BaggageFields;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import com.wosai.middleware.hera.tracing.propagation.TraceContextOrSamplingFlags;
import com.wosai.middleware.hera.tracing.spi.Codec;
import io.opentracing.propagation.TextMap;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Spliterator;
import java.util.Spliterators;
import java.util.stream.StreamSupport;

public class BaggagePropagation implements Codec<TextMap> {
    /**
     * Wraps an underlying propagation implementation, pushing one or more fields.
     */
    public static BaggagePropagation.Builder newBuilder(Codec<TextMap> delegate) {
        return new BaggagePropagation.Builder(delegate);
    }

    public static class Builder {
        final Codec<TextMap> delegate;
        final List<String> extractKeyNames = new ArrayList<>();
        final Set<BaggagePropagationConfig> configs = new LinkedHashSet<>();

        Builder(Codec<TextMap> delegate) {
            if (delegate == null) throw new NullPointerException("delegate == null");
            this.delegate = delegate;
        }

        /**
         * Returns an immutable copy of the current {@linkplain #add(BaggagePropagationConfig)
         * configuration}. This allows those who can't create the builder to reconfigure this builder.
         *
         * @see #clear()
         * @since 5.11
         */
        public Set<BaggagePropagationConfig> configs() {
            return Collections.unmodifiableSet(new LinkedHashSet<>(configs));
        }

        /**
         * Clears all state. This allows those who can't create the builder to reconfigure fields.
         *
         * @see #configs()
         * @since 5.11
         */
        public Builder clear() {
            extractKeyNames.clear();
            configs.clear();
            return this;
        }

        /**
         * @since 5.11
         */
        public Builder add(BaggagePropagationConfig config) {
            if (config == null) throw new NullPointerException("config == null");
            if (configs.contains(config)) {
                throw new IllegalArgumentException(config + " already added");
            }
            for (String extractKeyName : config.baggageCodec.extractKeyNames()) {
                if (extractKeyNames.contains(extractKeyName)) {
                    throw new IllegalArgumentException("Propagation key already in use: " + extractKeyName);
                }
                extractKeyNames.add(extractKeyName);
            }

            configs.add(config);
            return this;
        }

        /**
         * Returns the delegate if there are no fields to propagate.
         */
        public Codec<TextMap> build() {
            if (configs.isEmpty()) return delegate;
            return new BaggagePropagation(this);
        }
    }

    /**
     * Stored in {@link TraceContextOrSamplingFlags#extra()} or {@link HeraSpanContext#extra()}
     */
    static final class Extra {
        final List<String> extractKeyNames;

        Extra(List<String> extractKeyNames) {
            this.extractKeyNames = extractKeyNames;
        }
    }

    @Override
    public TraceContextOrSamplingFlags extract(TextMap carrier) {
        TraceContextOrSamplingFlags.Builder builder = this.delegate.extract(carrier).toBuilder();
        BaggageFields extra = this.baggageFactory.create();
        builder.addExtra(extra);

        if (this.extra == null) return builder.build();

        for (BaggagePropagationConfig config : configs) {
            if (config.baggageCodec == BaggageCodec.NOOP) continue; // local field

            // filter keys from carrier with those included in baggageCodec
            Iterator<Map.Entry<String, String>> iterator = carrier.iterator();

            // first create a keySet
            Set<String> keySet = ImmutableSet.copyOf(config.baggageCodec.injectKeyNames());

            // then iterator over a large TextMap (probably) to filter keys within the KeySet
            // and then find the first element
            StreamSupport.stream(Spliterators.spliteratorUnknownSize(iterator, Spliterator.ORDERED), false)
                    .filter(entry -> keySet.contains(entry.getKey().toLowerCase()))
                    .filter(entry -> entry.getValue() != null)
                    .findFirst().ifPresent(entry -> config.baggageCodec.decode(extra, carrier, entry.getValue()));
        }

        return builder.addExtra(this.extra).build();
    }

    @Override
    public void inject(HeraSpanContext context, TextMap carrier) {
        delegate.inject(context, carrier);
        BaggageFields extra = context.findExtra(BaggageFields.class);
        if (extra == null) return;
        Map<String, String> values =
                extra.toMapFilteringFieldNames(this.localFieldNames);
        if (values.isEmpty()) return;

        for (BaggagePropagationConfig config : configs) {
            if (config.baggageCodec == BaggageCodec.NOOP) continue; // local field

            String value = config.baggageCodec.encode(values, context, carrier);
            if (value == null) continue;

            List<String> keys = config.baggageCodec.injectKeyNames();
            for (int i = 0, length = keys.size(); i < length; i++) {
                carrier.put(keys.get(i), value);
            }
        }
    }

    final BaggageFields.Factory baggageFactory;
    final Codec<TextMap> delegate;
    final BaggagePropagationConfig[] configs;
    final String[] localFieldNames;
    @Nullable
    final Extra extra;

    BaggagePropagation(Builder builder) {
        this.delegate = builder.delegate;
        // Don't add another "extra" if there are only local fields
        // Don't add another "extra" if there are only local fields
        List<String> extractKeyNames = Lists.ensureImmutable(builder.extractKeyNames);
        this.extra = !extractKeyNames.isEmpty() ? new Extra(extractKeyNames) : null;

        // Associate baggage fields with any remote propagation keys
        this.configs = builder.configs.toArray(new BaggagePropagationConfig[0]);

        List<BaggageField> fields = new ArrayList<>();
        Set<String> localFieldNames = new LinkedHashSet<>();
        int maxDynamicFields = 0;
        for (BaggagePropagationConfig config : builder.configs) {
            maxDynamicFields += config.maxDynamicFields;
            if (config instanceof BaggagePropagationConfig.SingleBaggageField) {
                BaggageField field = ((BaggagePropagationConfig.SingleBaggageField) config).field;
                fields.add(field);
                if (config.baggageCodec == BaggageCodec.NOOP) localFieldNames.add(field.name());
            }
        }
        this.baggageFactory = BaggageFields.newFactory(fields, maxDynamicFields);
        this.localFieldNames = localFieldNames.toArray(new String[0]);
    }

    @Override
    public HeraSpanContext decorate(HeraSpanContext context) {
        HeraSpanContext result = delegate.decorate(context);
        return this.baggageFactory.decorate(result);
    }

//    public static List<String> allKeyNames(Codec<TextMap> codec) {
//        if (codec == null) throw new NullPointerException("propagation == null");
//        // When baggage or similar is in use, the result != TraceContextOrSamplingFlags.EMPTY
//        TraceContextOrSamplingFlags emptyExtraction =
//                codec.extract(Boolean.TRUE);
//        List<String> baggageKeyNames = getAllKeyNames(emptyExtraction);
//        if (baggageKeyNames.isEmpty()) return codec.keys();
//
//        List<String> result = new ArrayList<>(codec.keys().size() + baggageKeyNames.size());
//        result.addAll(codec.keys());
//        result.addAll(baggageKeyNames);
//        return Collections.unmodifiableList(result);
//    }
}
