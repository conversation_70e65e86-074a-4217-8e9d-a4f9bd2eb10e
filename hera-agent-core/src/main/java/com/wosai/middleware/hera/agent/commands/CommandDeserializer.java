package com.wosai.middleware.hera.agent.commands;

import com.shouqianba.middleware.hera.network.common.v1.Command;
import com.wosai.middleware.hera.network.component.command.AsyncProfileTaskCommand;
import com.wosai.middleware.hera.network.component.command.BaseCommand;
import com.wosai.middleware.hera.network.component.command.CommandExecutionContext;
import com.wosai.middleware.hera.network.component.command.ConfigurationDiscoveryCommand;
import com.wosai.middleware.hera.network.component.command.ProfileTaskCommand;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CommandDeserializer {
    public static BaseCommand deserialize(final CommandExecutionContext context, final Command command) {
        final String commandName = command.getCommand();
        switch (commandName) {
            case ProfileTaskCommand.NAME:
                return ProfileTaskCommand.DESERIALIZER.deserialize(context, command);
            case AsyncProfileTaskCommand.NAME:
                return AsyncProfileTaskCommand.DESERIALIZER.deserialize(context, command);
            case ConfigurationDiscoveryCommand.NAME:
                return ConfigurationDiscoveryCommand.DESERIALIZER.deserialize(context, command);
            default:
                throw new UnsupportedCommandException(command);
        }
    }
}