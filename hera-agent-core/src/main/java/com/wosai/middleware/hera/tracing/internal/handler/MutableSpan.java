package com.wosai.middleware.hera.tracing.internal.handler;

import brave.internal.codec.IpLiteral;
import com.google.common.annotations.VisibleForTesting;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.Reference;
import com.wosai.middleware.hera.tracing.network.Endpoint;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import javax.annotation.concurrent.NotThreadSafe;
import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@ToString
@NotThreadSafe
public final class MutableSpan implements Cloneable {
    static final Object[] EMPTY_ARRAY = new Object[0];

    @EqualsAndHashCode
    @ToString
    public static class LogEntry {
        @Getter
        private final String message;
        private final Map<String, ?> fields;

        public LogEntry(String message, Map<String, ?> fields) {
            this.message = message;
            this.fields = fields;
        }

        public Map<String, ?> getFields() {
            if (fields == null) return Collections.emptyMap();
            return Collections.unmodifiableMap(fields);
        }
    }

    public interface TagConsumer<T> {
        void accept(T target, String key, Object value);
    }

    public interface LogConsumer<T> {
        void accept(T target, long timestamp, LogEntry entry);
    }

    public interface TagUpdater {
        Object update(String key, Object value);
    }

    public interface LogUpdater {
        LogEntry update(long timestamp, LogEntry entry);
    }

    /*
     * One of these objects is allocated for each in-flight span, so we try to be parsimonious on things
     * like array allocation and object reference size.
     */
    AbstractHeraSpan.Kind kind;
    boolean shared;
    // timestamp related
    final long startTimestamp;
    final long startTimeNanoTicks;
    final boolean computeDurationViaNanoTicks;
    long duration;
    @ToString.Exclude
    Endpoint localEndpoint;
    String name;
    String remoteServiceName;
    String remoteIp;
    int remotePort;
    @ToString.Exclude
    @Getter
    final List<Reference> references;

    //
    // The below use object arrays instead of ArrayList. The intent is not for safe sharing
    // (copy-on-write), as this type is externally synchronized. In other words, this isn't
    // copy-on-write. We just grow arrays as we need to similar to how ArrayList does it.
    //
    // tags [(key, value)] annotations [(timestamp, value)]
    @ToString.Exclude
    Object[] tags = EMPTY_ARRAY;
    @ToString.Exclude
    Object[] logs = EMPTY_ARRAY;
    int tagCount;
    int logCount;

    @ToString.Exclude
    Throwable error;

    @VisibleForTesting
    public MutableSpan(long startTimestamp) {
        this(startTimestamp, 0L, false, Collections.emptyList());
    }

    public MutableSpan(long startTimestamp, long startTimeNanoTicks, boolean computeDurationViaNanoTicks) {
        this(startTimestamp, startTimeNanoTicks, computeDurationViaNanoTicks, Collections.emptyList());
    }

    public MutableSpan(long startTimestamp, long startTimeNanoTicks, boolean computeDurationViaNanoTicks, List<Reference> references) {
        this.startTimestamp = startTimestamp;
        this.startTimeNanoTicks = startTimeNanoTicks;
        this.computeDurationViaNanoTicks = computeDurationViaNanoTicks;
        this.references = references;
    }

    public boolean isEmpty() {
        return kind == null
                && startTimestamp == 0L
                && startTimeNanoTicks == 0L
                && !shared
                && localEndpoint == null
                && duration == 0L
                && name == null
                && remoteServiceName == null
                && remoteIp == null
                && remotePort == 0
                && tagCount == 0
                && logCount == 0
                && references.isEmpty()
                && error == null;
    }

    public String name() {
        return name;
    }

    public void name(String name) {
        if (name == null) throw new NullPointerException("name == null");
        this.name = name;
    }

    /**
     * @return start timestamp of the span. Unit is microseconds.
     */
    public long startTimestamp() {
        return startTimestamp;
    }

    public long startTimeNanoTicks() {
        return startTimeNanoTicks;
    }

    public long duration() {
        return duration;
    }

    public void duration(long durationMicro) {
        this.duration = durationMicro;
    }

    public AbstractHeraSpan.Kind kind() {
        return kind;
    }

    public void kind(AbstractHeraSpan.Kind kind) {
        this.kind = kind;
    }

    public String remoteServiceName() {
        return remoteServiceName;
    }

    public void remoteServiceName(String remoteServiceName) {
        if (remoteServiceName == null || remoteServiceName.isEmpty()) {
            throw new NullPointerException("remoteServiceName is empty");
        }
        this.remoteServiceName = remoteServiceName;
    }

    public Endpoint localEndpoint() {
        return this.localEndpoint;
    }

    public void localEndpoint(Endpoint localEndpoint) {
        this.localEndpoint = localEndpoint;
    }

    public String remoteIp() {
        return remoteIp;
    }

    public int remotePort() {
        return remotePort;
    }

    public boolean remoteIpAndPort(String remoteIp, int remotePort) {
        if (remoteIp == null) return false;
        this.remoteIp = IpLiteral.ipOrNull(remoteIp);
        if (this.remoteIp == null) return false;
        if (remotePort > 0xffff) throw new IllegalArgumentException("invalid port " + remotePort);
        if (remotePort < 0) remotePort = 0;
        this.remotePort = remotePort;
        return true;
    }

    public void log(long timestamp, LogEntry value) {
        if (value == null) throw new NullPointerException("value == null");
        if (timestamp == 0L) return; // silently ignore data Zipkin would drop
        logs = add(logs, logCount * 2, timestamp, value); // Annotations are always add.
        logCount++;
    }

    public Throwable error() {
        return error;
    }

    public void error(Throwable error) {
        this.error = error;
    }

    /**
     * Returns the last value associated with the key or null
     */
    public Object tag(String key) {
        if (key == null) throw new NullPointerException("key == null");
        if (key.isEmpty()) throw new IllegalArgumentException("key is empty");
        for (int i = 0, length = tagCount * 2; i < length; i += 2) {
            if (key.equals(tags[i])) return tags[i + 1];
        }
        return null;
    }

    public void tag(String key, Object value) {
        if (key == null) throw new NullPointerException("key == null");
        if (key.isEmpty()) throw new IllegalArgumentException("key is empty");
        if (value == null) throw new NullPointerException("value of " + key + " == null");
        int i = 0;
        for (int length = tagCount * 2; i < length; i += 2) {
            if (key.equals(tags[i])) {
                update(tags, i, value);
                return;
            }
        }
        tags = add(tags, i, key, value);
        tagCount++;
    }

    public <T> void forEachTag(TagConsumer<T> tagConsumer, T target) {
        for (int i = 0, length = tagCount * 2; i < length; i += 2) {
            tagConsumer.accept(target, (String) tags[i], tags[i + 1]);
        }
    }

    /**
     * Allows you to update values for redaction purposes
     */
    public void forEachTag(TagUpdater tagUpdater) {
        for (int i = 0, length = tagCount * 2; i < length; i += 2) {
            Object value = tags[i + 1];
            Object newValue = tagUpdater.update((String) tags[i], value);
            if (newValue != null) {
                update(tags, i, newValue);
            } else {
                remove(tags, i);
                length -= 2;
                tagCount--;
                i -= 2;
            }
        }
    }

    public <T> void forEachLog(LogConsumer<T> logConsumer, T target) {
        for (int i = 0, length = logCount * 2; i < length; i += 2) {
            long timestamp = (long) logs[i];
            logConsumer.accept(target, timestamp, (LogEntry) logs[i + 1]);
        }
    }

    /**
     * Allows you to update values for redaction purposes
     */
    public void forEachLog(LogUpdater logUpdater) {
        for (int i = 0, length = logCount * 2; i < length; i += 2) {
            LogEntry value = (LogEntry) logs[i + 1];
            LogEntry newValue = logUpdater.update((long) logs[i], value);
            if (newValue != null) {
                update(logs, i, newValue);
            } else {
                remove(logs, i);
                length -= 2;
                logCount--;
                i -= 2;
            }
        }
    }

    public void clear() {
        this.tags = EMPTY_ARRAY;
        this.tagCount = 0;
        if (logCount > 0) {
            this.logs = EMPTY_ARRAY;
            this.logCount = 0;
        }
        this.error = null;
    }

    public boolean shared() {
        return shared;
    }

    public void setShared() {
        shared = true;
    }

    public boolean containsLogMessage(String value) {
        if (value == null) throw new NullPointerException("value == null");
        for (int i = 0, length = logCount * 2; i < length; i += 2) {
            if (value.equals(logs[i + 1])) return true;
        }
        return false;
    }

    public int tagCount() {
        return tagCount;
    }

    public int logCount() {
        return logCount;
    }

    public boolean computeDurationViaNanoTicks() {
        return this.computeDurationViaNanoTicks;
    }

    @ToString.Exclude
    volatile int hashCode; // Lazily initialized and cached.

    @Override
    public int hashCode() {
        int h = hashCode;
        if (h == 0) {
            h = 1000003;
            h ^= kind == null ? 0 : kind.hashCode();
            h *= 1000003;
            h ^= (int) ((startTimestamp >>> 32) ^ startTimestamp);
            h *= 1000003;
            h ^= (int) ((startTimeNanoTicks >>> 32) ^ startTimeNanoTicks);
            h *= 1000003;
            h ^= Boolean.hashCode(computeDurationViaNanoTicks);
            h *= 1000003;
            h ^= (int) ((duration >>> 32) ^ duration);
            h *= 1000003;
            h ^= Boolean.hashCode(shared);
            h *= 1000003;
            h ^= name == null ? 0 : name.hashCode();
            h *= 1000003;
            h ^= localEndpoint == null ? 0 : localEndpoint.hashCode();
            h *= 1000003;
            h ^= references == null ? 0 : references.hashCode();
            h *= 1000003;
            h ^= remoteServiceName == null ? 0 : remoteServiceName.hashCode();
            h *= 1000003;
            h ^= remoteIp == null ? 0 : remoteIp.hashCode();
            h *= 1000003;
            h ^= remotePort;
            h *= 1000003;
            h ^= entriesHashCode(tags, tagCount);
            h *= 1000003;
            h ^= entriesHashCode(logs, logCount);
            h *= 1000003;
            h ^= error == null ? 0 : error.hashCode();
            hashCode = h;
        }
        return h;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) return true;
        // Hack that allows WeakConcurrentMap to lookup without allocating a new object.
        if (o instanceof WeakReference) o = ((WeakReference) o).get();
        if (!(o instanceof MutableSpan)) return false;

        MutableSpan that = (MutableSpan) o;
        return kind == that.kind
                && shared == that.shared
                && equal(localEndpoint, that.localEndpoint)
                && startTimestamp == that.startTimestamp
                && startTimeNanoTicks == that.startTimeNanoTicks
                && computeDurationViaNanoTicks == that.computeDurationViaNanoTicks
                && duration == that.duration
                && equal(name, that.name)
                && equal(remoteServiceName, that.remoteServiceName)
                && equal(remoteIp, that.remoteIp)
                && remotePort == that.remotePort
                && entriesEqual(tags, tagCount, that.tags, that.tagCount)
                && entriesEqual(logs, logCount, that.logs, that.logCount)
                && equal(references, that.references)
                && equal(error, that.error);
    }

    static boolean equal(Object a, Object b) {
        return a == null ? b == null : a.equals(b); // Java 6 can't use Objects.equals()
    }

    static boolean entriesEqual(Object[] left, int leftCount, Object[] right, int rightCount) {
        if (leftCount != rightCount) return false;
        for (int i = 0; i < leftCount * 2; i++) {
            if (!equal(left[i], right[i])) return false;
        }
        return true;
    }

    static int entriesHashCode(Object[] entries, int count) {
        int h = 1000003;
        for (int i = 0; i < count * 2; i++) {
            h ^= entries[i] == null ? 0 : entries[i].hashCode();
            h *= 1000003;
        }
        return h;
    }

    static Object[] add(Object[] input, int i, Object key, Object value) {
        Object[] result;
        if (i == input.length) {
            result = Arrays.copyOf(input, i + 2); // grow for one more entry
        } else {
            result = input;
        }
        result[i] = key;
        result[i + 1] = value;
        return result;
    }

    // this is externally synchronized, so we can edit it directly
    static void update(Object[] input, int i, Object value) {
        if (value.equals(input[i + 1])) return;
        input[i + 1] = value;
    }

    // This shifts and back-fills nulls so that we don't thrash copying arrays
    // when deleting. UnsafeArray will still work as it skips on first null key.
    static void remove(Object[] input, int i) {
        int j = i + 2;
        for (; j < input.length; i += 2, j += 2) {
            if (input[j] == null) break; // found null key
            input[i] = input[j];
            input[i + 1] = input[j + 1];
        }
        input[i] = input[i + 1] = null;
    }
}