package com.wosai.middleware.hera.tracing;

import io.opentracing.Scope;
import io.opentracing.ScopeManager;
import io.opentracing.Span;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayDeque;

@Slf4j
public class HeraScopeManager implements ScopeManager {
    // allow creating new instance in the tests
    HeraScopeManager() {
    }

    final ThreadLocal<ArrayDeque<HeraScope>> scopeStack = ThreadLocal.withInitial(ArrayDeque::new);

    public HeraScope activeScope() {
        return scopeStack.get().peek();
    }

    void push(HeraScope scope) {
        this.scopeStack.get().push(scope);
    }

    HeraScope pop() {
        return this.scopeStack.get().pop();
    }

    @Override
    public AbstractHeraSpan activeSpan() {
        HeraScope scope = scopeStack.get().peek();
        return scope == null ? null : scope.unwrap();
    }

    @Override
    public Scope activate(Span span) {
        if (span instanceof AbstractHeraSpan) {
            AbstractHeraSpan parentSpan = activeSpan();
            if (parentSpan == null) {
                return new HeraScope(this, (AbstractHeraSpan) span);
            }
            if (parentSpan.context().getSpanId() == ((AbstractHeraSpan) span).context().getSpanId()) {
                HeraScope scope = scopeStack.get().peek();
                // since we have a parentSpan, here should not be null!
                assert scope != null;
                scope.ref();
                return scope;
            }
            return new HeraScope(this, (AbstractHeraSpan) span);
        }
        throw new IllegalArgumentException("cannot handle a span which is not an instance of ReferenceCountSpan");
    }

    private static class HeraScopeManagerHolder {
        private static final HeraScopeManager INSTANCE = new HeraScopeManager();
    }

    public static HeraScopeManager getInstance() {
        return HeraScopeManagerHolder.INSTANCE;
    }

    public Boolean isEmptyScopeStack() {
        return scopeStack.get().isEmpty();
    }
}

