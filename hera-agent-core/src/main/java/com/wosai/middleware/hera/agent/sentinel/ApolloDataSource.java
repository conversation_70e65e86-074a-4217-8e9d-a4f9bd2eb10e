package com.wosai.middleware.hera.agent.sentinel;

import com.alibaba.csp.sentinel.Constants;
import com.alibaba.csp.sentinel.datasource.AbstractDataSource;
import com.alibaba.csp.sentinel.datasource.Converter;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.wosai.middleware.hera.agent.conf.HeraConfig;
import com.wosai.middleware.hera.agent.conf.apollo.ApolloClient;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.util.StringUtil;

import static com.wosai.middleware.hera.agent.sentinel.SentinelConfigConstant.CONFIG_KEY;

public class ApolloDataSource<T> extends AbstractDataSource<String, T> implements SentinelConfigWatcher.Listener {

    private static final ILog LOGGER = LogManager.getLogger(ApolloDataSource.class);

    private final ApolloClient apolloClient;

    private final String ruleKey;
    private final String defaultRuleValue;

    /**
     * Constructs the Apollo data source
     *
     * @param namespaceName    the namespace name in Apollo, should not be null or empty
     * @param ruleKey          the rule key in the namespace, should not be null or empty
     * @param defaultRuleValue the default rule value when the ruleKey is not found or any error
     *                         occurred
     * @param parser           the parser to transform string configuration to actual flow rules
     */
    public ApolloDataSource(String namespaceName, String ruleKey, String defaultRuleValue,
                            Converter<String, T> parser, ApolloClient apolloClient) {
        super(parser);

        Preconditions.checkArgument(!Strings.isNullOrEmpty(namespaceName), "Namespace name could not be null or empty");
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ruleKey), "RuleKey could not be null or empty!");

        this.ruleKey = ruleKey;
        this.defaultRuleValue = defaultRuleValue;
        this.apolloClient = apolloClient;

        initialize();
        LOGGER.info("Initialized rule for namespace: {}, rule key: {}", namespaceName, ruleKey);
    }

    private void initialize() {
        initializeConfigChangeListener();
        loadAndUpdateRules();
    }

    private void loadAndUpdateRules() {
        try {
            T newValue = loadConfig();
            if (newValue == null) {
                LOGGER.warn("[ApolloDataSource] WARN: rule config is null, you may have to check your data source");
            }
            LOGGER.debug("[ApolloDataSource] loadAndUpdateRules ruleKey is {} and change value {}", ruleKey, newValue);
            getProperty().updateValue(newValue);
        } catch (Throwable ex) {
            LOGGER.warn("[ApolloDataSource] Error when loading rule config", ex);
        }
    }

    private void initializeConfigChangeListener() {
        SentinelConfigWatcher sentinelConfigWatcher = new SentinelConfigWatcher(this, defaultRuleValue, ruleKey);
        apolloClient.register(sentinelConfigWatcher);
    }

    @Override
    public String readSource() {
        if (apolloClient.getConfigMap().containsKey(ruleKey)) {
            return (String) apolloClient.getConfigMap().get(ruleKey);
        }
        return defaultRuleValue;
    }

    @Override
    public void close() throws Exception {
    }

    @Override
    public void onChange(String newValue) {
        loadAndUpdateRules();
        if (ruleKey.endsWith(CONFIG_KEY)) {
            resetConfig();
        }
    }

    /**
     * reset sentinel apollo config to turn switch on/off
     */
    void resetConfig() {
        try {
            SentinelConfig sentinelConfig = (SentinelConfig) loadConfig();
            if (StringUtil.isNotBlank(sentinelConfig.getBlockMessage())) {
                HeraConfig.Sentinel.BLOCK_MESSAGE = sentinelConfig.getBlockMessage();
            }
            HeraConfig.Sentinel.GLOBAL_SWITCH = sentinelConfig.isGlobalSwitch();
            Constants.ON = HeraConfig.Sentinel.SWITCH_FLAG && HeraConfig.Sentinel.GLOBAL_SWITCH;
        } catch (Throwable ex) {
            LOGGER.warn("[ApolloDataSource] Error when loading configs", ex);
        }
    }
}
