package com.wosai.middleware.hera.tracing.tag;

/**
 * The following span tags are recommended for instrumentors who are trying to capture more
 * semantic information about the spans. Tracers may expose additional features based on these
 * standardized data points. Tag names follow a general structure of namespacing.
 *
 * @see <a href="https://github.com/opentracing/specification/blob/master/semantic_conventions.md">https://github.com/opentracing/specification/blob/master/semantic_conventions.md</a>
 */
public final class Tags {
    private Tags() {
    }

    /**
     * A constant for setting the span kind to indicate that it represents a server span.
     */
    public static final String SPAN_KIND_SERVER = "server";

    /**
     * A constant for setting the span kind to indicate that it represents a client span.
     */
    public static final String SPAN_KIND_CLIENT = "client";

    /**
     * A constant for setting the span kind to indicate that it represents a producer span, in a messaging scenario.
     */
    public static final String SPAN_KIND_PRODUCER = "producer";

    /**
     * A constant for setting the span kind to indicate that it represents a consumer span, in a messaging scenario.
     */
    public static final String SPAN_KIND_CONSUMER = "consumer";

    /**
     * HTTP_URL records the url of the incoming request.
     */
    public static final StringTag HTTP_URL = new StringTag("http.url");

    /**
     * HTTP_STATUS records the http status code of the response.
     */
    public static final IntTag HTTP_STATUS = new IntTag("http.status_code");

    /**
     * HTTP_METHOD records the http method. Case-insensitive.
     */
    public static final StringTag HTTP_METHOD = new StringTag("http.method");

    /**
     * PEER_HOST_IPV4 records IPv4 host address of the peer.
     */
    public static final IntOrStringTag PEER_HOST_IPV4 = new IntOrStringTag("peer.ipv4");

    /**
     * PEER_HOST_IPV6 records the IPv6 host address of the peer.
     */
    public static final StringTag PEER_HOST_IPV6 = new StringTag("peer.ipv6");

    /**
     * PEER_SERVICE records the service name of the peer.
     */
    public static final StringTag PEER_SERVICE = new StringTag("peer.service");

    /**
     * PEER_HOSTNAME records the host name of the peer.
     */
    public static final StringTag PEER_HOSTNAME = new StringTag("peer.hostname");

    /**
     * PEER_PORT records the port number of the peer.
     */
    public static final IntTag PEER_PORT = new IntTag("peer.port");

    /**
     * SAMPLING_PRIORITY determines the priority of sampling this Span.
     */
    public static final IntTag SAMPLING_PRIORITY = new IntTag("sampling.priority");

    /**
     * SPAN_KIND hints at the relationship between spans, e.g. client/server.
     */
    public static final StringTag SPAN_KIND = new StringTag("span.kind");

    /**
     * COMPONENT is a low-cardinality identifier of the module, library, or package that is instrumented.
     */
    public static final StringTag COMPONENT = new StringTag("component");

    /**
     * ERROR indicates whether a Span ended in an error state.
     */
    public static final BooleanTag ERROR = new BooleanTag("error");

    /**
     * DB_TYPE indicates the type of Database.
     * For any SQL database, "sql". For others, the lower-case database category, e.g. "cassandra", "hbase"
     */
    public static final StringTag DB_TYPE = new StringTag("db.type");

    /**
     * DB_INSTANCE indicates the instance name of Database.
     * If the jdbc.url="*************************************", instance name is "customers".
     */
    public static final StringTag DB_INSTANCE = new StringTag("db.instance");

    /**
     * DB_USER indicates the user name of Database, e.g. "readonly_user" or "reporting_user"
     */
    public static final StringTag DB_USER = new StringTag("db.user");

    /**
     * DB_STATEMENT records a database statement for the given database type.
     * For db.type="SQL", "SELECT * FROM wuser_table". For db.type="redis", "SET mykey "WuValue".
     */
    public static final StringTag DB_STATEMENT = new StringTag("db.statement");

    /**
     * MESSAGE_BUS_DESTINATION records an address at which messages can be exchanged.
     * E.g. A Kafka record has an associated "topic name" that can be extracted by the instrumented
     * producer or consumer and stored using this tag.
     */
    public static final StringTag MESSAGE_BUS_DESTINATION = new StringTag("message_bus.destination");

    /**
     * CACHE_TYPE records cache type, such as jedis
     */
    public static final StringTag CACHE_TYPE = new StringTag("cache.type");

    /**
     * CACHE_OP represent a command is used for "write" or "read"
     * It's better that adding this tag to span , so OAP would analysis write/read metric accurately
     * Reference org.apache.skywalking.apm.plugin.jedis.v4.AbstractConnectionInterceptor#parseOperation
     * BTW "op" means Operation
     */
    public static final StringTag CACHE_OP = new StringTag("cache.op");

    /**
     * CACHE_TYPE records the cache command
     */
    public static final StringTag CACHE_CMD = new StringTag("cache.cmd");

    /**
     * CACHE_TYPE records the cache key
     */
    public static final StringTag CACHE_KEY = new StringTag("cache.key");

    /**
     * LOCK_NAME records the lock name such as redisson lock name
     */
    public static final StringTag LOCK_NAME = new StringTag("lock.name");

    /**
     * LEASE_TIME represents the maximum time to hold the lock after it's acquisition
     * in redisson plugin,it's unit is ms
     */
    public static final StringTag LEASE_TIME = new StringTag("lease.time");

    /**
     * THREAD_ID records the thread id
     */
    public static final StringTag THREAD_ID = new StringTag("thread.id");

    /**
     * CACHE_INSTANCE records the cache instance
     */
    public static final StringTag CACHE_INSTANCE = new StringTag("cache.instance");
}

