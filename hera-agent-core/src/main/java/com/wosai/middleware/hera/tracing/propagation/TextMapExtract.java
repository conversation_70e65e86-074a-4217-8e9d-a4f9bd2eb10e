package com.wosai.middleware.hera.tracing.propagation;

import io.opentracing.propagation.TextMap;

import java.util.Iterator;
import java.util.Map;

import static com.google.common.base.Preconditions.checkNotNull;

@FunctionalInterface
public interface TextMapExtract {
    Iterator<Map.Entry<String, String>> iterator();

    class TextMapExtractAdapter implements TextMap {
        private final TextMapExtract textMapExtract;

        public TextMapExtractAdapter(TextMapExtract textMapExtract) {
            this.textMapExtract = checkNotNull(textMapExtract, "TextMap Extract");
        }

        @Override
        public Iterator<Map.Entry<String, String>> iterator() {
            return textMapExtract.iterator();
        }

        public void put(String key, String value) {
            throw new UnsupportedOperationException(
                    "HeadersMapExtractAdapter should only be used with Tracer.extract()");
        }
    }
}
