package com.wosai.middleware.hera.agent.metrics;

import io.jaegertracing.internal.metrics.Gauge;

import java.util.concurrent.atomic.AtomicLong;

public class JaegerMicrometerGaugeAdapter implements Gauge {

    private final AtomicLong ref;

    private JaegerMicrometerGaugeAdapter(AtomicLong ref) {
        this.ref = ref;
    }

    @Override
    public void update(long amount) {
        this.ref.set(amount);
    }

    public static Gauge create(AtomicLong ref) {
        return new JaegerMicrometerGaugeAdapter(ref);
    }
}
