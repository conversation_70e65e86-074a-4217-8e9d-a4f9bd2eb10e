package com.wosai.middleware.hera.agent.sentinel;

import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowException;
import com.alibaba.csp.sentinel.slots.system.SystemBlockException;

public class BlockException extends Exception {
    private final com.alibaba.csp.sentinel.slots.block.BlockException delegation;

    public BlockException(com.alibaba.csp.sentinel.slots.block.BlockException blockException) {
        super(blockException.getMessage());
        this.delegation = blockException;
    }

    public boolean isFlowException() {
        return this.delegation instanceof FlowException;
    }

    public boolean isParamFlowException() {
        return this.delegation instanceof ParamFlowException;
    }

    public boolean isDegradeException() {
        return this.delegation instanceof DegradeException;
    }

    public boolean isSystemBlockException() {
        return this.delegation instanceof SystemBlockException;
    }

    public String getRuleLimitApp() {
        return this.delegation.getRuleLimitApp();
    }

    public AbstractRule<?> getRule() {
        // lazy create rule!
        return AbstractRule.create(this.delegation.getRule());
    }

    public String getResourceName() {
        if (isSystemBlockException()) {
            return ((SystemBlockException) this.delegation).getResourceName();
        }
        return ((ParamFlowException) this.delegation).getResourceName();
    }

    public RuntimeException toRuntimeException() {
        return this.delegation.toRuntimeException();
    }
}
