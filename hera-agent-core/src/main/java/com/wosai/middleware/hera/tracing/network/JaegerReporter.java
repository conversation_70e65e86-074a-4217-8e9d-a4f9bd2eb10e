package com.wosai.middleware.hera.tracing.network;

import com.google.common.annotations.VisibleForTesting;
import com.wosai.middleware.hera.tracing.internal.handler.FinishedSpanHandler;
import com.wosai.middleware.hera.tracing.internal.handler.JaegerFinishedSpanHandler;
import com.wosai.middleware.hera.tracing.internal.handler.JaegerSpanConverter;
import com.wosai.middleware.hera.tracing.spi.Reporter;
import io.jaegertracing.internal.exceptions.SenderException;
import io.jaegertracing.internal.metrics.Metrics;
import io.jaegertracing.thrift.internal.reporters.protocols.JaegerThriftSpanConverter;
import io.jaegertracing.thrift.internal.reporters.protocols.ThriftUdpTransport;
import io.jaegertracing.thrift.internal.senders.ThriftSender;
import io.jaegertracing.thrift.internal.senders.ThriftSenderBase;
import io.jaegertracing.thriftjava.Process;
import io.jaegertracing.thriftjava.Span;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * In the original JaegerTracing Java Client, {@link ThriftSender} contains a buffer to gather a batch
 * of {@link io.jaegertracing.internal.JaegerSpan} to be sent. But since {@link io.jaegertracing.spi.Sender}
 * interface only exposes an API which allows to store a single {@link io.jaegertracing.internal.JaegerSpan},
 * we cannot use it here. Thus, we use it as a direct sender while moving buffer in this reporter.
 */
@Slf4j
public class JaegerReporter implements Reporter<NetworkSpan> {
    private Process process;
    private final Metrics metrics;
    @ToString.Exclude
    @VisibleForTesting
    final BlockingQueue<Command> commandQueue;
    @ToString.Exclude
    @VisibleForTesting
    final Thread queueProcessorThread;
    @ToString.Exclude
    @VisibleForTesting
    final QueueProcessor queueProcessor;
    @ToString.Exclude
    @VisibleForTesting
    final Timer flushTimer;
    private final ThriftSender delegate;
    private final List<Span> spanBuffer;
    private final int closeEnqueueTimeout;
    private int processBytesSize;
    private int spanBytesSize;
    private final int maxBatchBytes;

    public JaegerReporter(ThriftSender sender, int reportQueueSize, int closeEnqueueTimeout, int flushInterval, Metrics metrics) {
        // for jaeger-type tracer, we don't know port
        this.delegate = sender;
        if (reportQueueSize == 0) {
            reportQueueSize = 100;
        }
        spanBuffer = new ArrayList<>();
        commandQueue = new LinkedBlockingQueue<>(reportQueueSize);
        // start a thread to append spans
        queueProcessor = new QueueProcessor();
        queueProcessorThread = new Thread(queueProcessor, "JaegerReporter-QueueProcessor");
        queueProcessorThread.setDaemon(true);
        queueProcessorThread.start();
        this.closeEnqueueTimeout = closeEnqueueTimeout;
        this.metrics = metrics;
        maxBatchBytes = ThriftUdpTransport.MAX_PACKET_SIZE - ThriftSenderBase.EMIT_BATCH_OVERHEAD;

        flushTimer = new Timer("JaegerReporter-FlushTimer", true /* isDaemon */);
        flushTimer.schedule(
                new TimerTask() {
                    @Override
                    public void run() {
                        flush();
                    }
                },
                flushInterval,
                flushInterval);
    }

    @Override
    public void report(NetworkSpan networkSpan) throws SenderException {
        // It's better to drop spans, than to block here
        boolean added = commandQueue.offer(new AppendCommand(networkSpan));
        if (!added) {
            metrics.reporterDropped.inc(1);
        }
    }

    @Override
    public void close() throws IOException {
        try {
            // best-effort: if we can't add CloseCommand in this time then it probably will never happen
            boolean added = commandQueue
                    .offer(new CloseCommand(), closeEnqueueTimeout, TimeUnit.MILLISECONDS);
            if (added) {
                queueProcessorThread.join(10000);
            } else {
                log.warn("Unable to cleanly close JaegerReporter, command queue is full - probably the"
                        + " sender is stuck");
            }
        } catch (InterruptedException e) {
            log.error("Interrupted", e);
        } finally {
            try {
                int n = doFlush();
                metrics.reporterSuccess.inc(n);
            } catch (SenderException e) {
                metrics.reporterFailure.inc(e.getDroppedSpanCount());
                log.error("Remote reporter error", e);
            }
            // close flushTimer to avoid test failures
            flushTimer.cancel();
        }
    }

    void flush() {
        // to reduce the number of updateGauge stats, we only emit queue length on flush
        metrics.reporterQueueLength.update(commandQueue.size());

        // We can safely drop FlushCommand when the queue is full - sender should take care of flushing
        // in such case
        commandQueue.offer(new FlushCommand());
    }

    /*
     * The code below implements the command pattern.  This pattern is useful for
     * situations where multiple threads would need to synchronize on a resource,
     * but are fine with executing sequentially.  The advantage is simplified code where
     * tasks are put onto a blocking queue and processed sequentially by another thread.
     */
    interface Command {
        int execute() throws SenderException;
    }

    @RequiredArgsConstructor
    class AppendCommand implements Command {
        private final NetworkSpan span;

        @Override
        public int execute() throws SenderException {
            return append(span);
        }

        public int append(NetworkSpan networkSpan) throws SenderException {
            if (process == null) {
                process = new Process(networkSpan.getLocalEndpoint().serviceName);
                process.setTags(JaegerThriftSpanConverter.buildTags(networkSpan.getLocalEndpoint().tags));
                processBytesSize = calculateProcessSize(process);
            }

            Span thriftSpan = JaegerSpanConverter.convert(networkSpan);
            int spanSize = calculateSpanSize(thriftSpan);
            if (spanSize > getMaxSpanBytes()) {
                throw new SenderException(String.format("ThriftSender received a span that was too large, size = %d, max = %d",
                        spanSize, getMaxSpanBytes()), null, 1);
            }

            spanBytesSize += spanSize;
            if (spanBytesSize <= getMaxSpanBytes()) {
                spanBuffer.add(thriftSpan);
                if (spanBytesSize < getMaxSpanBytes()) {
                    return 0;
                }
                return doFlush();
            }

            int n;
            try {
                n = doFlush();
            } catch (SenderException e) {
                // +1 for the span not submitted in the buffer above
                throw new SenderException(e.getMessage(), e.getCause(), e.getDroppedSpanCount() + 1);
            }

            spanBuffer.add(thriftSpan);
            spanBytesSize = spanSize;
            return n;
        }
    }

    class CloseCommand implements Command {
        @Override
        public int execute() throws SenderException {
            queueProcessor.close();
            return 0;
        }
    }

    class FlushCommand implements Command {
        @Override
        public int execute() throws SenderException {
            return doFlush();
        }
    }

    /**
     * @return how many spans are sent to the remote endpoint.
     * @throws SenderException thrown from the underlying delegation.
     */
    private int doFlush() throws SenderException {
        if (spanBuffer.isEmpty()) {
            return 0;
        }

        int n = spanBuffer.size();
        try {
            delegate.send(process, spanBuffer);
        } catch (SenderException e) {
            throw new SenderException("Failed to flush spans.", e, n);
        } finally {
            spanBuffer.clear();
            spanBytesSize = 0;
        }
        return n;
    }

    protected int getMaxSpanBytes() {
        return this.maxBatchBytes - processBytesSize;
    }

    protected int calculateProcessSize(Process proc) throws SenderException {
        try {
            return this.delegate.getSize(proc);
        } catch (Exception e) {
            throw new SenderException("ThriftSender failed writing Process to memory buffer.", e, 1);
        }
    }

    protected int calculateSpanSize(Span span) throws SenderException {
        try {
            return this.delegate.getSize(span);
        } catch (Exception e) {
            throw new SenderException("ThriftSender failed writing Span to memory buffer.", e, 1);
        }
    }

    /*
     * This class creates a Runnable that is responsible for appending spans using a sender.
     */
    @ToString
    class QueueProcessor implements Runnable {
        // merge from https://github.com/jaegertracing/jaeger-client-java/pull/763
        private volatile boolean open = true;
        private final Set<Class<?>> commandFailedBefore = new HashSet<Class<?>>();

        @Override
        public void run() {
            while (open) {
                try {
                    Command command = commandQueue.take();
                    Class<? extends Command> commandClass = command.getClass();
                    boolean failedBefore = commandFailedBefore.contains(commandClass);

                    try {
                        int sentSpanCount = command.execute();
                        if (sentSpanCount > 0) {
                            metrics.reporterSuccess.inc(sentSpanCount);
                            if (failedBefore) {
                                log.info(commandClass.getSimpleName() + " is working again!");
                                commandFailedBefore.remove(commandClass);
                            }
                        }
                    } catch (SenderException e) {
                        metrics.reporterFailure.inc(e.getDroppedSpanCount());
                        if (!failedBefore) {
                            log.warn(commandClass.getSimpleName()
                                    + " execution failed! Repeated errors of this command will not be logged.", e);
                            commandFailedBefore.add(commandClass);
                        }
                    }
                } catch (Exception e) {
                    log.error("QueueProcessor error:", e);
                    // Do nothing, and try again on next span.
                }
            }
        }

        public void close() {
            open = false;
        }
    }

    @Override
    public FinishedSpanHandler createSpanHandler() {
        return new JaegerFinishedSpanHandler(this);
    }
}
