package com.wosai.middleware.hera.agent.conf.bootstrap;

/**
 * {@link RunLevel} is the entry point for the configuration bootstrap.
 * Similar to the <a href="https://en.wikipedia.org/wiki/Runlevel">Runlevel</a> in Linux.
 * Only a single entry point will be chosen. Some well known run-levels,
 * - Kubernetes
 */
public interface RunLevel<C extends Context> {
    String FALLBACK = "Fallback";

    /**
     * Mode is scanned order by priority.
     * Higher priority will be checked first.
     */
    int priority();

    /**
     * @return name of the mode
     */
    String getName();

    /**
     * @return whether this mode should be run
     */
    boolean shouldRun();

    /**
     * @return context associated with the run level
     */
    C context();

    enum Preset implements RunLevel<Context> {
        FALLBACK {
            @Override
            public int priority() {
                return Integer.MIN_VALUE;
            }

            @Override
            public String getName() {
                return RunLevel.FALLBACK;
            }

            @Override
            public boolean shouldRun() {
                return true;
            }

            @Override
            public Context context() {
                return new Context();
            }
        }
    }
}
