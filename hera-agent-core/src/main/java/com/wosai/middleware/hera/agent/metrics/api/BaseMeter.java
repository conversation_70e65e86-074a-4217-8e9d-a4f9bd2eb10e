package com.wosai.middleware.hera.agent.metrics.api;

import io.micrometer.core.instrument.Meter;
import lombok.Getter;

public abstract class BaseMeter<T extends Meter> {
    @Getter
    protected final T delegation;

    public BaseMeter(T delegation) {
        this.delegation = delegation;
    }

    /**
     * @return A unique combination of name and tags
     */
    public MeterId getId() {
        return MeterId.from(this.delegation.getId());
    }

    /**
     * Get a set of measurements. Should always return the same number of measurements and
     * in the same order, regardless of the level of activity or the lack thereof.
     *
     * @return The set of measurements that represents the instantaneous value of this
     * meter.
     */
    public abstract Iterable<Measurement> measure();

    public static BaseMeter<?> from(Meter meter) {
        switch (meter.getId().getType()) {
            case GAUGE:
                return new Gauge((io.micrometer.core.instrument.Gauge) meter);
            case LONG_TASK_TIMER:
                return new LongTaskTimer((io.micrometer.core.instrument.LongTaskTimer) meter);
            case COUNTER:
                if (meter instanceof io.micrometer.core.instrument.FunctionCounter) {
                    return new FunctionCounter((io.micrometer.core.instrument.FunctionCounter) meter);
                } else {
                    return new Counter((io.micrometer.core.instrument.Counter) meter);
                }
            case TIMER:
                if (meter instanceof io.micrometer.core.instrument.FunctionTimer) {
                    return new FunctionTimer((io.micrometer.core.instrument.FunctionTimer) meter);
                } else {
                    return new Timer((io.micrometer.core.instrument.Timer) meter);
                }
            case DISTRIBUTION_SUMMARY:
                return new DistributionSummary((io.micrometer.core.instrument.DistributionSummary) meter);
            case OTHER:
            default:
                // TODO: seems used in KafkaMetrics?
                throw new IllegalStateException(meter.getClass() + " is not support");
        }
    }
}
