package com.wosai.middleware.hera.agent.metrics.api;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Iterables;
import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

public abstract class AbstractBuilder<BUILDER extends AbstractBuilder<?, ?>, METER extends BaseMeter<?>> {
    private static MeterRegistry METER_REGISTRY;

    protected final String name;

    protected Tags tags;

    protected String description;

    protected String baseUnit;

    protected AbstractBuilder(String name) {
        if (name == null) {
            throw new IllegalArgumentException("Meter name cannot be null");
        }
        this.name = name;
        this.tags = Tags.empty();
    }

    /**
     * append new tag to this meter
     */
    public BUILDER tag(String key, String value) {
        tags = tags.and(key, value);
        return (BUILDER) this;
    }

    public BUILDER tags(String... keyValues) {
        tags = tags.and(keyValues);
        return (BUILDER) this;
    }

    public BUILDER tags(Iterable<Tag> keyValues) {
        tags = tags.and(Iterables.transform(keyValues, Tag::build));
        return (BUILDER) this;
    }

    public BUILDER description(String desc) {
        this.description = desc;
        return (BUILDER) this;
    }

    public BUILDER baseUnit(String newBaseUnit) {
        this.baseUnit = newBaseUnit;
        return (BUILDER) this;
    }

    /**
     * Create a meter adapter
     */
    protected abstract METER create(MeterRegistry registry);

    public METER build() {
        if (METER_REGISTRY == null) {
            METER_REGISTRY = MetricsHandler.getMeterRegistry();
        }
        final METER adapter = this.create(METER_REGISTRY);
        return (METER) adapter;
    }

    @VisibleForTesting
    public static void resetMeterRegistry() {
        METER_REGISTRY = null;
    }
}
