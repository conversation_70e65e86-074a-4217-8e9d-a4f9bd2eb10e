package com.wosai.middleware.hera.tracing;

import com.wosai.middleware.hera.tracing.internal.handler.MutableSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;

public abstract class StackBasedSpan extends AbstractHeraSpan implements ReferenceCountObject {
    protected int stackDepth;

    public StackBasedSpan(HeraTracer tracer,
                          HeraSpanContext context,
                          MutableSpan state) {
        super(tracer, context, state);
        this.stackDepth = 1;
    }

    @Override
    public void finish() {
        if (unref()) {
            super.finish();
        }
    }

    @Override
    protected boolean isFinishedInMainThread() {
        return this.stackDepth == 0 && super.isFinishedInMainThread();
    }

    @Override
    public int refCount() {
        return stackDepth;
    }

    @Override
    public int ref() {
        return ++stackDepth;
    }

    @Override
    public boolean unref() {
        return --stackDepth == 0;
    }
}
