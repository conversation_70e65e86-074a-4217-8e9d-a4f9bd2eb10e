package com.wosai.middleware.hera.agent.metrics.api;

import io.micrometer.core.instrument.MeterRegistry;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

public class LongTaskTimer extends BaseMeter<io.micrometer.core.instrument.LongTaskTimer> {
    public static LongTaskTimer.Builder builder(String name) {
        return new LongTaskTimer.Builder(name);
    }

    LongTaskTimer(io.micrometer.core.instrument.LongTaskTimer delegation) {
        super(delegation);
    }

    public Sample start() {
        return new Sample(this.delegation.start());
    }

    public double duration(TimeUnit unit) {
        return this.delegation.duration(unit);
    }

    public int activeTasks() {
        return this.delegation.activeTasks();
    }

    public double max(TimeUnit unit) {
        return this.delegation.max(unit);
    }

    public TimeUnit baseTimeUnit() {
        return this.delegation.baseTimeUnit();
    }

    @Override
    public Iterable<Measurement> measure() {
        return Arrays.asList(new Measurement(() -> (double) activeTasks(), Statistic.ACTIVE_TASKS),
                new Measurement(() -> duration(baseTimeUnit()), Statistic.DURATION));
    }

    public static class Builder extends AbstractBuilder<LongTaskTimer.Builder, LongTaskTimer> {
        private Boolean enableHistogram = false;
        private double[] percentiles;

        Builder(String name) {
            super(name);
        }

        public Builder publishPercentileHistogram(Boolean enable) {
            this.enableHistogram = enable;
            return this;
        }

        public Builder publishPercentiles(double... percentiles) {
            this.percentiles = percentiles;
            return this;
        }

        @Override
        protected LongTaskTimer create(MeterRegistry registry) {
            return new LongTaskTimer(io.micrometer.core.instrument.LongTaskTimer.builder(name)
                    .description(description)
                    .tags(tags)
                    .publishPercentileHistogram(this.enableHistogram)
                    .publishPercentiles(this.percentiles)
                    .register(registry));
        }
    }

    public static class Sample {
        private final io.micrometer.core.instrument.LongTaskTimer.Sample sample;

        Sample(io.micrometer.core.instrument.LongTaskTimer.Sample sample) {
            this.sample = sample;
        }

        public long stop() {
            return sample.stop();
        }

        public double duration(TimeUnit unit) {
            return sample.duration(unit);
        }
    }
}