package com.wosai.middleware.hera.tracing;

import com.wosai.middleware.hera.tracing.internal.handler.MutableSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import com.wosai.middleware.hera.tracing.propagation.TraceContextOrSamplingFlags;

import java.util.List;

public class HeraObjectFactory {
    // For eager class loading
    private static final TraceContextOrSamplingFlags ECL = TraceContextOrSamplingFlags.SAMPLED;

    public HeraTracer.SpanBuilder createSpanBuilder(HeraTracer tracer, String operationName) {
        return tracer.new SpanBuilder(operationName);
    }

    public LocalSpan createLocalSpan(
            HeraTracer tracer,
            HeraSpanContext context,
            MutableSpan state) {
        return new LocalSpan(
                tracer,
                context,
                state);
    }

    public EntrySpan createEntrySpan(HeraTracer tracer,
                                     HeraSpanContext context,
                                     MutableSpan state,
                                     AbstractHeraSpan.Kind kind) {
        return new EntrySpan(tracer, context, state, kind);
    }

    public ExitSpan createExitSpan(HeraTracer tracer,
                                   HeraSpanContext context,
                                   MutableSpan state,
                                   AbstractHeraSpan.Kind kind) {
        return new ExitSpan(tracer, context, state, kind);
    }

    public HeraSpanContext createSpanContext(
            long traceIdHigh,
            long traceIdLow,
            long spanId,
            long parentId,
            int flags,
            List<Object> extra,
            String debugId) {
        return InternalPropagation.INSTANCE.newTraceContext(traceIdHigh, traceIdLow, spanId, parentId, flags, extra, debugId);
    }
}
