package com.wosai.middleware.hera.agent.conf.dynamic;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Set;

public abstract class AbstractAgentConfigChangeWatcher {
    @Getter
    private final Set<String> keySetOfInterest;

    protected AbstractAgentConfigChangeWatcher(Set<String> keySetOfInterest) {
        this.keySetOfInterest = Collections.unmodifiableSet(keySetOfInterest);
    }

    /**
     * notify the watcher that a change has been made.
     *
     * @param events a list of events
     */
    public abstract void notify(List<ConfigChangeEvent> events);

    @Getter
    @RequiredArgsConstructor
    public static class ConfigChangeEvent {
        private final String key;
        private final String newValue;
        private final EventType eventType;
    }

    public enum EventType {
        ADD, MODIFY, DELETE
    }
}
