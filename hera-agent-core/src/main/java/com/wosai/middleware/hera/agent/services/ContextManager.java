package com.wosai.middleware.hera.agent.services;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.wosai.middleware.hera.agent.profiling.trace.ProfileStatusContext;
import com.wosai.middleware.hera.agent.profiling.trace.ProfileTaskExecutionService;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.HeraScopeManager;
import com.wosai.middleware.hera.tracing.HeraTracer;
import com.wosai.middleware.hera.tracing.ReferenceCountObject;
import com.wosai.middleware.hera.tracing.baggage.BaggageField;
import com.wosai.middleware.hera.tracing.internal.baggage.BaggageFields;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import com.wosai.middleware.hera.tracing.propagation.TextMapExtract;
import com.wosai.middleware.hera.tracing.propagation.TextMapInject;
import io.jaegertracing.internal.clock.Clock;
import io.opentracing.References;
import io.opentracing.Scope;
import io.opentracing.SpanContext;
import io.opentracing.propagation.Format;
import io.opentracing.tag.Tags;
import org.apache.skywalking.apm.agent.core.boot.BootService;
import org.apache.skywalking.apm.agent.core.boot.DefaultImplementor;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;
import org.apache.skywalking.apm.agent.core.context.RuntimeContext;
import org.apache.skywalking.apm.util.StringUtil;

import java.util.List;
import java.util.Optional;

import static com.wosai.middleware.hera.tracing.internal.baggage.ExtraBaggageContext.findExtra;

@DefaultImplementor
public class ContextManager implements BootService {
    private static final ThreadLocal<RuntimeContext> RUNTIME_CONTEXT = new ThreadLocal<>();

    private static HeraScopeManager SCOPE_MANAGER;
    private static HeraTracer TRACER;
    private static ProfileTaskExecutionService PROFILE_TASK_EXECUTION_SERVICE;

    public ContextManager() {
    }

    @VisibleForTesting
    public static void installTracer(HeraTracer heraTracer) {
        ContextManager.TRACER = heraTracer;
        ContextManager.SCOPE_MANAGER = heraTracer.scopeManager();
    }

    public static AbstractHeraSpan markError(AbstractHeraSpan span) {
        return span.setTag(Tags.ERROR, true);
    }

    // a shorthand for ContextManager.logError(ContextManager.activeSpan(), t);
    public static AbstractHeraSpan logError(Throwable t) {
        return logError(activeSpan(), t);
    }

    public static AbstractHeraSpan logError(AbstractHeraSpan span, Throwable t) {
        // for Jaeger, you only need to put an error object, which will be expanded later by JaegerSpan itself
        return span.error(t);
    }

    public static Clock getClock() {
        return TRACER.getClock();
    }

    public static AbstractHeraSpan prepareAsyncSpan(AbstractHeraSpan span) {
        if (ContextManager.activeSpan() != span) {
            throw new RuntimeException("Span is not the active in current context.");
        }
        return span.prepareForAsync();
    }

    public static AbstractHeraSpan createEntrySpan(String operationName) {
        return createEntrySpan(operationName, 0);
    }

    public static AbstractHeraSpan createEntrySpan(String operationName, SpanContext... parentSpans) {
        return createEntrySpan(operationName, 0, parentSpans);
    }

    public static AbstractHeraSpan createEntrySpan(String operationName, long startTime, SpanContext... parentSpans) {
        HeraTracer.SpanBuilder spanBuilder = TRACER.buildSpan(operationName).withStartTimestamp(startTime)
                .withTag(Tags.SPAN_KIND, Tags.SPAN_KIND_SERVER);
        for (SpanContext parentSpan : parentSpans) {
            spanBuilder.addReference(References.FOLLOWS_FROM, parentSpan);
        }
        AbstractHeraSpan span = spanBuilder.start();
        TRACER.activateSpan(span);
        return span;
    }

    public static AbstractHeraSpan createEntrySpan(String operationName, SpanContext spanContext) {
        AbstractHeraSpan span = TRACER.buildSpan(operationName)
                .withTag(Tags.SPAN_KIND, Tags.SPAN_KIND_SERVER)
                .asChildOf(spanContext).start();
        TRACER.activateSpan(span);
        return span;
    }

    public static AbstractHeraSpan createLocalSpan(String operationName) {
        return createLocalSpan(operationName, null);
    }

    public static AbstractHeraSpan createLocalSpan(String operationName, SpanContext spanContext) {
        HeraTracer.SpanBuilder spanBuilder = TRACER.buildSpan(operationName);
        if (spanContext != null) {
            spanBuilder.asChildOf(spanContext);
        }
        AbstractHeraSpan span = spanBuilder.start();
        TRACER.activateSpan(span);
        return span;
    }

    public static AbstractHeraSpan createExitSpan(String operationName, String remotePeerAddr, int remotePeerPort) {
        return createExitSpan(operationName, Tags.SPAN_KIND_CLIENT, remotePeerAddr, remotePeerPort);
    }

    public static AbstractHeraSpan createExitSpan(String operationName, String spanKind, String remotePeerAddr, int remotePeerPort) {
        if (!Tags.SPAN_KIND_PRODUCER.equals(spanKind) && !Tags.SPAN_KIND_CLIENT.equals(spanKind)) {
            throw new IllegalArgumentException("illegal span kind");
        }

        AbstractHeraSpan span = TRACER.buildSpan(operationName)
                .withTag(Tags.SPAN_KIND, Tags.SPAN_KIND_CLIENT)
                .withTag(Tags.PEER_PORT, remotePeerPort).start().remoteIpAndPort(remotePeerAddr, remotePeerPort);
        TRACER.activateSpan(span);
        return span;
    }

    public static AbstractHeraSpan createExitSpan(String operationName, String remotePeer) {
        return createExitSpan(operationName, Tags.SPAN_KIND_CLIENT, remotePeer);
    }

    public static AbstractHeraSpan createExitSpan(String operationName, String spanKind, String remotePeer) {
        return createExitSpan(operationName, spanKind, remotePeer, null);
    }

    public static AbstractHeraSpan createExitSpan(String operationName, String remotePeer, SpanContext spanContext) {
        return createExitSpan(operationName, Tags.SPAN_KIND_CLIENT, remotePeer, spanContext);
    }

    public static AbstractHeraSpan createExitSpan(String operationName, String spanKind, String remotePeer, SpanContext spanContext) {
        if (!Tags.SPAN_KIND_PRODUCER.equals(spanKind) && !Tags.SPAN_KIND_CLIENT.equals(spanKind)) {
            throw new IllegalArgumentException("illegal span kind");
        }

        HeraTracer.SpanBuilder spanBuilder = TRACER.buildSpan(operationName).withTag(Tags.SPAN_KIND, spanKind);
        if (spanContext != null) {
            spanBuilder.asChildOf(spanContext);
        }
        AbstractHeraSpan span = spanBuilder.start();
        if (!StringUtil.isEmpty(remotePeer)) {
            span.remoteServiceName(remotePeer);
        }
        TRACER.activateSpan(span);
        return span;
    }

    public static HeraSpanContext extract(TextMapExtract textMapExtract) {
        return TRACER.extract(Format.Builtin.TEXT_MAP, new TextMapExtract.TextMapExtractAdapter(textMapExtract));
    }

    public static AbstractHeraSpan activeSpan() {
        return TRACER.activeSpan();
    }

    public static String getTraceId() {
        AbstractHeraSpan span = TRACER.activeSpan();
        return span == null ? "N/A" : span.context().toTraceId();
    }

    /**
     * Find the item with a given key in the context
     *
     * @param key          the key in the context
     * @param defaultValue the default value to be used if the key does not exist
     * @return the value of the baggage item identified by the given key, or defaultValue if no such item could be found
     */
    public static String getBaggageItem(String key, String defaultValue) {
        AbstractHeraSpan span = TRACER.activeSpan();
        if (span == null) {
            return defaultValue;
        }
        String baggage = span.getBaggageItem(key);
        if (Strings.isNullOrEmpty(baggage)) {
            return defaultValue;
        }
        return baggage;
    }

    public static void stopSpan() {
        AbstractHeraSpan span = TRACER.activeSpan();
        Scope scope = SCOPE_MANAGER.activeScope();
        if (scope != null) {
            scope.close();
        }
        stopSpan(span);
    }

    private static void stopSpan(AbstractHeraSpan span) {
        span.finish();
        // not really stopped
        if (span instanceof ReferenceCountObject && ((ReferenceCountObject) span).refCount() != 0) {
            return;
        }
        if (SCOPE_MANAGER.isEmptyScopeStack()) {
            RUNTIME_CONTEXT.remove();
        }
    }

    public static boolean isActive() {
        return TRACER.activeSpan() != null;
    }

    public static void profilingRecheck(AbstractHeraSpan span, String operationName) {
        PROFILE_TASK_EXECUTION_SERVICE.profilingRecheck(span,
                String.valueOf(span.context().toSpanId()), operationName);
    }

    /**
     * Try to continue trace profiling in a new thread.
     *
     * @param span                the newly created span in the current thread.
     * @param parentStatusContext the profiling status context of the parent span from another thread.
     */
    public static void continued(AbstractHeraSpan span, ProfileStatusContext parentStatusContext) {
        if (span.context().getProfileStatus().continued(parentStatusContext)) {
            PROFILE_TASK_EXECUTION_SERVICE.continueProfiling(span, String.valueOf(span.context().toSpanId()));
        }
    }

    public static ProfileStatusContext addProfiling(AbstractHeraSpan span, String operationName) {
        return PROFILE_TASK_EXECUTION_SERVICE.addProfiling(span,
                String.valueOf(span.context().toSpanId()), operationName);
    }

    public static RuntimeContext getRuntimeContext() {
        RuntimeContext runtimeContext = RUNTIME_CONTEXT.get();
        if (runtimeContext == null) {
            runtimeContext = new RuntimeContext(RUNTIME_CONTEXT);
            RUNTIME_CONTEXT.set(runtimeContext);
        }
        return runtimeContext;
    }

    public static Optional<String> setAdditionalBaggage(String key, String value) {
        AbstractHeraSpan span = activeSpan();
        if (span == null) {
            return Optional.empty();
        }
        BaggageField f = BaggageField.getByName(span.context(), key);
        if (f == null) {
            f = BaggageField.create(key);
            List<Object> extraList = span.context().extra();
            BaggageFields baggageFields = findExtra(BaggageFields.class, extraList);
            if (extraList != null && baggageFields != null) {
                baggageFields.addBaggage(f, value);
            }
            return Optional.empty();
        }
        String oldValue = f.getValue(span.context());
        f.updateValue(value);
        return Optional.ofNullable(oldValue);
    }

    public static void inject(AbstractHeraSpan span, TextMapInject carrier) {
        TRACER.inject(span.context(), Format.Builtin.TEXT_MAP, new TextMapInject.TextMapInjectAdapter(carrier));
    }

    @Override
    public void shutdown() {
    }

    @Override
    public void prepare() throws Throwable {
        PROFILE_TASK_EXECUTION_SERVICE = ServiceManager.INSTANCE.findService(ProfileTaskExecutionService.class);
    }

    @Override
    public void boot() throws Throwable {
    }

    @Override
    public void onComplete() throws Throwable {
    }
}
