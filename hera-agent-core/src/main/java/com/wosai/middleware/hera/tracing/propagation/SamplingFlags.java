package com.wosai.middleware.hera.tracing.propagation;

import com.wosai.middleware.hera.tracing.InternalPropagation;

import java.util.List;

import static com.wosai.middleware.hera.tracing.InternalPropagation.FLAG_DEBUG;
import static com.wosai.middleware.hera.tracing.InternalPropagation.FLAG_DEFERRED;
import static com.wosai.middleware.hera.tracing.InternalPropagation.FLAG_SAMPLED;

public class SamplingFlags {
    static final SamplingFlags NOT_SAMPLED = new SamplingFlags(0);
    static final SamplingFlags SAMPLED = new SamplingFlags(FLAG_SAMPLED);
    static final SamplingFlags DEFERRED = new SamplingFlags(FLAG_DEFERRED);
    static final SamplingFlags DEBUG = new SamplingFlags(SAMPLED.flags | FLAG_DEBUG);

    static {
        InternalPropagation.INSTANCE = new InternalPropagation() {
            public int flags(SamplingFlags flags) {
                return flags.flags;
            }

            @Override
            public HeraSpanContext newTraceContext(long traceIdHigh, long traceIdLow, long spanId, long parentId, int flags, List<Object> extra, String debugId) {
                return new HeraSpanContext(traceIdHigh, traceIdLow, spanId, parentId, flags, extra, debugId);
            }

            @Override
            public HeraSpanContext withFlags(HeraSpanContext context, int flags) {
                return context.withFlags(flags);
            }

            @Override
            public HeraSpanContext shallowCopy(HeraSpanContext context) {
                return context.shallowCopy();
            }

            @Override
            public HeraSpanContext withExtra(HeraSpanContext context, List<Object> extra) {
                return context.withExtra(extra);
            }
        };
    }

    protected final int flags;

    protected SamplingFlags(int flags) {
        this.flags = flags;
    }

    public final Boolean sampled() {
        // not deferred and sampled
        return (flags & FLAG_DEFERRED) == FLAG_DEFERRED ? null : (flags & FLAG_SAMPLED) == FLAG_SAMPLED;
    }

    public final boolean isDebug() {
        return (flags & FLAG_DEBUG) == FLAG_DEBUG;
    }

    // only use for test
    int getFlags() {
        return flags;
    }

    static SamplingFlags toSamplingFlags(int flags) {
        switch (flags) {
            case 0:
            case FLAG_DEBUG:
                return NOT_SAMPLED;
            case FLAG_SAMPLED:
            case FLAG_DEFERRED | FLAG_SAMPLED:
                // down cast
                return SAMPLED;
            case FLAG_SAMPLED | FLAG_DEBUG:
                return DEBUG;
            case FLAG_DEFERRED:
            case FLAG_DEFERRED | FLAG_DEBUG:
                // down cast
                return DEFERRED;
            default:
                assert false; // programming error, but build anyway
                return new SamplingFlags(flags);
        }
    }

    @Override
    public String toString() {
        return toString(flags);
    }

    static String toString(int flags) {
        StringBuilder result = new StringBuilder();
        if ((flags & FLAG_DEBUG) == FLAG_DEBUG) {
            result.append("DEBUG");
        } else if ((flags & FLAG_SAMPLED) == FLAG_SAMPLED) {
            if ((flags & FLAG_SAMPLED) == FLAG_SAMPLED) {
                result.append("SAMPLED_REMOTE");
            } else {
                result.append("NOT_SAMPLED_REMOTE");
            }
        }
        return result.toString();
    }
}
