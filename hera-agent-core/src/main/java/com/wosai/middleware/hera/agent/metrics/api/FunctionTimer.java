package com.wosai.middleware.hera.agent.metrics.api;

import io.micrometer.core.instrument.MeterRegistry;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.function.ToDoubleFunction;
import java.util.function.ToLongFunction;

public class FunctionTimer extends BaseMeter<io.micrometer.core.instrument.FunctionTimer> {
    public static <T> FunctionTimer.Builder<T> builder(String name, T obj, ToLongFunction<T> countFunction,
                                                       ToDoubleFunction<T> totalTimeFunction, TimeUnit totalTimeFunctionUnit) {
        return new FunctionTimer.Builder<T>(name, obj, countFunction, totalTimeFunction, totalTimeFunctionUnit);
    }

    FunctionTimer(io.micrometer.core.instrument.FunctionTimer delegation) {
        super(delegation);
    }

    /**
     * @return The base time unit of the timer to which all published metrics will be
     * scaled
     */
    public TimeUnit baseTimeUnit() {
        return delegation.baseTimeUnit();
    }

    /**
     * @return The total number of occurrences of the timed event.
     */
    public double count() {
        return delegation.count();
    }

    /**
     * The total time of all occurrences of the timed event.
     *
     * @param unit The base unit of time to scale the total to.
     * @return The total time of all occurrences of the timed event.
     */
    public double totalTime(TimeUnit unit) {
        return delegation.totalTime(unit);
    }

    @Override
    public Iterable<Measurement> measure() {
        return Arrays.asList(new Measurement(this::count, Statistic.COUNT),
                new Measurement(() -> totalTime(baseTimeUnit()), Statistic.TOTAL_TIME));
    }

    public static class Builder<T> extends AbstractBuilder<Builder<T>, FunctionTimer> {
        private final T obj;
        private final ToLongFunction<T> countFunction;
        private final ToDoubleFunction<T> totalTimeFunction;
        private final TimeUnit totalTimeFunctionUnit;

        Builder(String name, T obj, ToLongFunction<T> countFunction,
                ToDoubleFunction<T> totalTimeFunction, TimeUnit totalTimeFunctionUnit) {
            super(name);
            this.obj = obj;
            this.countFunction = countFunction;
            this.totalTimeFunction = totalTimeFunction;
            this.totalTimeFunctionUnit = totalTimeFunctionUnit;
        }

        @Override
        protected FunctionTimer create(MeterRegistry registry) {
            return new FunctionTimer(io.micrometer.core.instrument.FunctionTimer.builder(name,
                            obj, countFunction, totalTimeFunction, totalTimeFunctionUnit)
                    .tags(tags)
                    .description(description)
                    .register(registry));
        }
    }
}
