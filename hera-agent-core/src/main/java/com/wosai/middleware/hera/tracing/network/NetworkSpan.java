package com.wosai.middleware.hera.tracing.network;

import com.wosai.middleware.hera.tracing.Reference;
import io.opentracing.tag.Tags;
import lombok.Builder;
import lombok.Getter;
import lombok.Singular;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Builder(builderClassName = "Builder")
public class NetworkSpan {
    @Getter
    private final long traceIDHigh;
    @Getter
    private final long traceIDLow;
    @Getter
    private final long spanID;
    @Getter
    private final long parentID;
    private final boolean debug;
    @Getter
    private final int flags;
    @Getter
    private final String name;
    @Getter
    private final long duration;
    @Getter
    private final long startTimestampMicro;
    private final boolean shared;
    @Getter
    @Singular
    private final Map<String, Object> tags; // unmodified!!!
    @Getter
    @Singular
    private final List<Object> logs;
    @Getter
    private final Endpoint remoteEndpoint;
    @Getter
    private final Endpoint localEndpoint;
    @Getter
    private final List<Reference> references;

    public Map<String, Object> convertRemoteEndpointAsSpanTags() {
        if (remoteEndpoint != null) {
            Map<String, Object> extraMap = new HashMap<>();
            if (remoteEndpoint.ipv4() != null) {
                extraMap.put(Tags.PEER_HOST_IPV4.getKey(), remoteEndpoint.ipv4);
            } else if (remoteEndpoint.ipv6() != null) {
                extraMap.put(Tags.PEER_HOST_IPV6.getKey(), remoteEndpoint.ipv6);
            }
            if (remoteEndpoint.port() != null) {
                extraMap.put(Tags.PEER_PORT.getKey(), remoteEndpoint.port());
            }
            if (remoteEndpoint.serviceName() != null) {
                extraMap.put(Tags.PEER_SERVICE.getKey(), remoteEndpoint.serviceName());
            }
            return extraMap;
        }
        return Collections.emptyMap();
    }

    public boolean isDebug() {
        return debug;
    }

    public boolean isShared() {
        return shared;
    }
}
