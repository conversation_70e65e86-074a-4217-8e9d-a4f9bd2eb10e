package com.wosai.middleware.hera.agent.conf.apollo;

import com.google.common.base.Strings;
import com.wosai.middleware.hera.agent.conf.apollo.io.BOMInputStream;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

public class DefaultServerProvider implements ServerProvider {
    private static final ILog LOGGER = LogManager.getLogger(DefaultServerProvider.class);
    static final String DEFAULT_SERVER_PROPERTIES_PATH_ON_LINUX = "/opt/settings/server.properties";
    static final String DEFAULT_SERVER_PROPERTIES_PATH_ON_WINDOWS = "C:/opt/settings/server.properties";

    private final Properties serverProperties = new Properties();

    String getServerPropertiesPath() {
        final String serverPropertiesPath = getCustomizedServerPropertiesPath();

        if (!Strings.isNullOrEmpty(serverPropertiesPath)) {
            return serverPropertiesPath;
        }

        return isOSWindows() ? DEFAULT_SERVER_PROPERTIES_PATH_ON_WINDOWS
                : DEFAULT_SERVER_PROPERTIES_PATH_ON_LINUX;
    }

    private String getCustomizedServerPropertiesPath() {
        // 1. Get from System Property
        final String serverPropertiesPathFromSystemProperty = System
                .getProperty("apollo.path.server.properties");
        if (!Strings.isNullOrEmpty(serverPropertiesPathFromSystemProperty)) {
            return serverPropertiesPathFromSystemProperty;
        }

        // 2. Get from OS environment variable
        final String serverPropertiesPathFromEnvironment = System
                .getenv("APOLLO_PATH_SERVER_PROPERTIES");
        if (!Strings.isNullOrEmpty(serverPropertiesPathFromEnvironment)) {
            return serverPropertiesPathFromEnvironment;
        }

        // last, return null if there is no custom value
        return null;
    }

    @Override
    public void initialize() {
        try {
            File file = new File(this.getServerPropertiesPath());
            if (file.exists() && file.canRead()) {
                LOGGER.info("Loading {}", file.getAbsolutePath());
                FileInputStream fis = new FileInputStream(file);
                initialize(fis);
                return;
            }

            initialize(null);
        } catch (Throwable ex) {
            LOGGER.error("Initialize hera.apollo.DefaultServerProvider failed.", ex);
        }
    }

    @Override
    public void initialize(InputStream in) {
        try {
            if (in != null) {
                try {
                    serverProperties.load(new InputStreamReader(new BOMInputStream(in), StandardCharsets.UTF_8));
                } finally {
                    in.close();
                }
            }

            // TODO: init env and datacenter?
        } catch (Throwable ex) {
            LOGGER.error("Initialize DefaultServerProvider failed.", ex);
        }
    }

    @Override
    public String getProperty(String name, String defaultValue) {
        String val = serverProperties.getProperty(name, defaultValue);
        return val == null ? defaultValue : val.trim();
    }

    @Override
    public Class<? extends ServerProvider> getType() {
        return ServerProvider.class;
    }

    public static boolean isBlank(String str) {
        return Strings.nullToEmpty(str).trim().isEmpty();
    }

    public static boolean isOSWindows() {
        String osName = System.getProperty("os.name");
        if (isBlank(osName)) {
            return false;
        }
        return osName.startsWith("Windows");
    }
}
