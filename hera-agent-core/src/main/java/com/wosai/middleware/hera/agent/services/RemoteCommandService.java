package com.wosai.middleware.hera.agent.services;

import com.google.common.annotations.VisibleForTesting;
import com.google.protobuf.ByteString;
import com.shouqianba.middleware.hera.network.arthas.v1.CommandRequest;
import com.shouqianba.middleware.hera.network.arthas.v1.CommandResponse;
import com.shouqianba.middleware.hera.network.arthas.v1.FileData;
import com.shouqianba.middleware.hera.network.arthas.v1.FileMetaData;
import com.shouqianba.middleware.hera.network.arthas.v1.FileServiceGrpc;
import com.shouqianba.middleware.hera.network.arthas.v1.RemoteCommandTaskGrpc;
import com.shouqianba.middleware.hera.network.arthas.v1.UploadResponse;
import com.shouqianba.middleware.hera.network.arthas.v1.UploadStatus;
import com.wosai.middleware.hera.agent.commands.CommandService;
import com.wosai.middleware.hera.agent.commands.RemoteCommandContextKey;
import com.wosai.middleware.hera.agent.remote.HeraGRPCChannelManager;
import com.wosai.middleware.hera.agent.remote.ReactiveGrpcThrowableHandler;
import com.wosai.middleware.hera.util.ThreadPoolUtil;
import com.wosai.middleware.hera.network.component.command.CommandExecutionContext;
import io.grpc.Channel;
import io.grpc.stub.StreamObserver;
import org.apache.skywalking.apm.agent.core.boot.BootService;
import org.apache.skywalking.apm.agent.core.boot.DefaultImplementor;
import org.apache.skywalking.apm.agent.core.boot.DefaultNamedThreadFactory;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;
import org.apache.skywalking.apm.agent.core.conf.Config;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.remote.GRPCChannelListener;
import org.apache.skywalking.apm.agent.core.remote.GRPCChannelStatus;
import org.apache.skywalking.apm.util.RunnableWithExceptionProtection;

import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Files;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@DefaultImplementor
public class RemoteCommandService implements BootService, Runnable, GRPCChannelListener {
    private static final ILog LOGGER = LogManager.getLogger(RemoteCommandService.class);

    // default: 12M
    private static final int CHUNK_SIZE = 12 * 1024 * 1024;

    @VisibleForTesting
    volatile StreamObserver<CommandRequest> publisher;

    @VisibleForTesting
    volatile GRPCChannelStatus status = GRPCChannelStatus.DISCONNECT;

    private volatile ScheduledFuture<?> remoteCommandDaemonServiceFuture;

    @VisibleForTesting
    volatile RemoteCommandTaskGrpc.RemoteCommandTaskStub remoteCommandTaskStub;

    private ScheduledExecutorService scheduledExecutorService;

    public RemoteCommandService() {
    }

    @Override
    public void prepare() throws Throwable {
        ServiceManager.INSTANCE.findService(HeraGRPCChannelManager.class).addChannelListener(this);
    }

    @Override
    public void boot() throws Throwable {
        scheduledExecutorService = Executors.newSingleThreadScheduledExecutor(new DefaultNamedThreadFactory("RemoteCommandDaemonService"));
    }

    @Override
    public void onComplete() throws Throwable {

    }

    @Override
    public void shutdown() throws Throwable {
        if (remoteCommandDaemonServiceFuture != null) {
            remoteCommandDaemonServiceFuture.cancel(true);
        }
        ThreadPoolUtil.gracefulShutdown(this.scheduledExecutorService, 10);
    }

    @Override
    public void run() {
        if (GRPCChannelStatus.CONNECTED.equals(status)) {
            final CountDownLatch latch = new CountDownLatch(1);
            this.publisher = remoteCommandTaskStub.executeCommand(new StreamObserver<CommandResponse>() {
                @Override
                public void onNext(CommandResponse response) {
                    final String replyTo = response.getClientId();
                    final CommandExecutionContext context = CommandExecutionContext.create();
                    context.setAttr(RemoteCommandContextKey.REPLY_TO, replyTo);
                    ServiceManager.INSTANCE.findService(CommandService.class).receiveCommands(context, response);
                }

                @Override
                public void onError(Throwable t) {
                    latch.countDown();
                    ReactiveGrpcThrowableHandler.handleThrowable(t, "remote command service connection disconnect");
                }

                @Override
                public void onCompleted() {
                    latch.countDown();
                }
            });
            try {
                // initiate a remote command request when connected
                this.publisher.onNext(CommandRequest.newBuilder()
                        .setService(Config.Agent.SERVICE_NAME)
                        .setServiceInstance(Config.Agent.INSTANCE_NAME).build());
            } catch (Throwable t) {
                LOGGER.error("fail to send remote command request", t);
            }

            try {
                latch.await();
            } catch (InterruptedException ex) {
                LOGGER.warn("latch await interrupted");
            }
        }
        LOGGER.warn("remote-command background service terminated");
    }

    @Override
    public void statusChanged(GRPCChannelStatus status) {
        if (GRPCChannelStatus.CONNECTED.equals(status)) {
            Channel ch = ServiceManager.INSTANCE.findService(HeraGRPCChannelManager.class).getChannel();
            remoteCommandTaskStub = RemoteCommandTaskGrpc.newStub(ch);
            // schedule a background job on connected
            remoteCommandDaemonServiceFuture = scheduledExecutorService.schedule(
                    new RunnableWithExceptionProtection(this, t -> LOGGER.error("RemoteCommandDaemonService run failure.", t)),
                    0,
                    TimeUnit.SECONDS
            );
        }
        this.status = status;
    }

    public void replyRemoteCommand(CommandRequest.Builder bld) {
        if (GRPCChannelStatus.CONNECTED.equals(this.status)) {
            // add missing metadata
            bld.setService(Config.Agent.SERVICE_NAME)
                    .setServiceInstance(Config.Agent.INSTANCE_NAME);

            // reply
            publisher.onNext(bld.build());
        }
    }

    public String uploadFile(String filepath) {
        String url = "";
        CompletableFuture<UploadResponse> responseFuture = new CompletableFuture<>();
        StreamObserver<UploadResponse> responseObserver = new StreamObserver<UploadResponse>() {
            @Override
            public void onNext(UploadResponse uploadResponse) {
                responseFuture.complete(uploadResponse);
            }

            @Override
            public void onError(Throwable th) {
                responseFuture.completeExceptionally(th);
            }

            @Override
            public void onCompleted() {
                if (!responseFuture.isDone()) {
                    responseFuture.completeExceptionally(new RuntimeException("completed without response"));
                }
            }
        };
        Channel channel = ServiceManager.INSTANCE.findService(HeraGRPCChannelManager.class).getChannel();
        FileServiceGrpc.FileServiceStub fileServiceStub = FileServiceGrpc.newStub(channel);
        StreamObserver<FileData> requestObserver = fileServiceStub.upload(responseObserver);
        try (FileInputStream fis = new FileInputStream(filepath)) {
            File file = new File(filepath);
            FileMetaData metaData = FileMetaData.newBuilder().setService(Config.Agent.SERVICE_NAME)
                    .setServiceInstance(Config.Agent.INSTANCE_NAME)
                    .setFilename(file.getName())
                    .setContentSize(Math.toIntExact(Files.size(file.toPath())))
                    .build();
            requestObserver.onNext(FileData.newBuilder().setMetaData(metaData).build());
            byte[] buffer = new byte[CHUNK_SIZE];
            int read;
            while ((read = fis.read(buffer)) != -1) {
                requestObserver.onNext(
                        FileData.newBuilder().setContent(ByteString.copyFrom(buffer, 0, read)).build()
                );
            }
        } catch (Exception e) {
            requestObserver.onError(e);
            LOGGER.error("upload file error: " + filepath, e);
        }
        requestObserver.onCompleted();
        try {
            UploadResponse uploadResponse = responseFuture.get();
            if (UploadStatus.UPLOAD_SUCCESS.equals(uploadResponse.getType())) {
                url = uploadResponse.getUrl();
            } else {
                throw new RuntimeException(uploadResponse.getMessage());
            }
        } catch (Exception e) {
            LOGGER.error("upload file error: " + filepath, e);
        }
        return url;
    }
}
