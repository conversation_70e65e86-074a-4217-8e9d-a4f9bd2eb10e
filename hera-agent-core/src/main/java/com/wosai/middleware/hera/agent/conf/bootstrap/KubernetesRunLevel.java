package com.wosai.middleware.hera.agent.conf.bootstrap;

import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.wosai.middleware.hera.agent.conf.HeraConfig;
import lombok.EqualsAndHashCode;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.FileSystem;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Properties;

@EqualsAndHashCode(callSuper = false)
public class KubernetesRunLevel extends FileSystemRunLevel<KubernetesContext> {
    private static final ILog LOGGER = LogManager.getLogger(KubernetesRunLevel.class);
    public static final String NAME = "Kubernetes";
    public static final String DEFAULT_NS = "sqb";

    static final String SERVICE_ACCOUNT_TOKEN = "/var/run/secrets/kubernetes.io/serviceaccount/token";
    static final String KUBERNETES_NAMESPACE = "/var/run/secrets/kubernetes.io/serviceaccount/namespace";

    private String namespace = DEFAULT_NS;

    private volatile KubernetesContext context;

    public KubernetesRunLevel() {
        this(FileSystems.getDefault());
    }

    KubernetesRunLevel(FileSystem fs) {
        super(fs);
    }

    @Override
    public int priority() {
        return 100;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public boolean shouldRun() {
        final Path saTokenPath = fs.getPath(SERVICE_ACCOUNT_TOKEN);
        if (!Files.isReadable(saTokenPath)) {
            return false;
        }
        final Path nsPath = fs.getPath(KUBERNETES_NAMESPACE);
        if (!Files.isReadable(nsPath)) {
            return false;
        }
        try {
            // NOTE: we cannot use com.google.common.io.Files.asCharSource to read file since JimfsPath cannot be converted to File
            final String ns = new String(Files.readAllBytes(nsPath), Charset.defaultCharset());
            // NOTE: we MUST not set namespace to Config.Agent.Namespace in order not to break the entire system
            if (Strings.isNullOrEmpty(ns)) {
                return false;
            }
            this.namespace = ns;
        } catch (IOException ioEx) {
            LOGGER.error("fail to read namespace", ioEx);
            return false;
        }
        return true;
    }

    Properties readDownwardAPI(final String downwardAPIPath) {
        Properties podProperties = new Properties();
        for (final String filePath : Splitter.on(";").omitEmptyStrings().split(downwardAPIPath)) {
            final Path path = fs.getPath(filePath);
            if (Files.isReadable(path)) {
                try (BufferedReader bufferedReader = Files.newBufferedReader(path)) {
                    podProperties.load(bufferedReader);
                    for (String key : podProperties.stringPropertyNames()) {
                        if (Strings.isNullOrEmpty(podProperties.getProperty(key))) {
                            continue;
                        }
                        // strip whitespace
                        // as define here https://util.unicode.org/UnicodeJsps/list-unicodeset.jsp?a=%5Cp%7Bwhitespace%7D
                        // CharMatcher.whitespace() includes both \t (ASCII 9) and whitespace (ASCII 20)
                        String value = CharMatcher.whitespace().trimFrom(podProperties.getProperty(key));
                        // remove double quotes
                        value = CharMatcher.is('\"').trimFrom(value);
                        podProperties.put(key, value);
                    }
                    // only read the first available file
                    break;
                } catch (IOException e) {
                    LOGGER.error("Parse download API failed", e);
                }
            }
        }
        return podProperties;
    }

    @Override
    public KubernetesContext context() {
        if (context == null) {
            this.context = new KubernetesContext(this.namespace, fs.getPath(SERVICE_ACCOUNT_TOKEN),
                    this.readDownwardAPI(HeraConfig.Kubernetes.DOWNWARD_API_LABELS_PATH));
        }
        return this.context;
    }
}
