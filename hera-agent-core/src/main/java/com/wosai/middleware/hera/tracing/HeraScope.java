package com.wosai.middleware.hera.tracing;

import io.opentracing.Scope;

public class HeraScope implements Scope, ReferenceCountObject {
    final HeraScopeManager scopeManager;
    final AbstractHeraSpan delegate;
    private int stackDepth;

    public HeraScope(HeraScopeManager scopeManager, AbstractHeraSpan span) {
        this.scopeManager = scopeManager;
        this.delegate = span;
        this.stackDepth = 1;

        scopeManager.push(this);
    }

    @Override
    public int refCount() {
        return this.stackDepth;
    }

    @Override
    public int ref() {
        return ++this.stackDepth;
    }

    @Override
    public boolean unref() {
        return --this.stackDepth == 0;
    }

    @Override
    public void close() {
        if (scopeManager.activeScope() != this) {
            // This shouldn't happen if users call methods in the expected order. Bail out.
            throw new IllegalStateException("this scope is not on the top of the stack!");
        }
        if (unref()) {
            // pop this scope out of stack
            scopeManager.pop();
        }
    }

    AbstractHeraSpan unwrap() {
        return delegate;
    }

}
