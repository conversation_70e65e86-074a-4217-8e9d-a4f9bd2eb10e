package com.wosai.middleware.hera.agent.metrics.api;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import io.micrometer.core.instrument.distribution.DistributionStatisticConfig;

public final class MeterFactory {
    private MeterFactory() {
    }

    /**
     * Tracks a monotonically increasing value.
     *
     * @param name The base metric name
     * @param tags Sequence of dimensions for breaking down the name.
     * @return A new or existing counter.
     */
    public static Counter counter(String name, Iterable<Tag> tags) {
        return Counter.builder(name).tags(tags).build();
    }

    /**
     * Tracks a monotonically increasing value.
     *
     * @param name The base metric name
     * @param tags MUST be an even number of arguments representing key/value pairs of
     *             tags.
     * @return A new or existing counter.
     */
    public static Counter counter(String name, String... tags) {
        return counter(name, Tags.of(tags));
    }

    public static TimeWindowMaxHolder defaultTimeWindowMax() {
        return new TimeWindowMaxHolder(MetricsHandler.getMeterRegistry().config().clock(), DistributionStatisticConfig.DEFAULT);
    }
}
