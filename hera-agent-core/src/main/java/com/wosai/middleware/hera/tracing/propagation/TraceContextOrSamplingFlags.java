package com.wosai.middleware.hera.tracing.propagation;

import com.wosai.middleware.hera.tracing.InternalPropagation;

import java.util.List;

import static brave.internal.collect.Lists.ensureImmutable;
import static com.wosai.middleware.hera.tracing.propagation.HeraSpanContext.ensureExtraAdded;
import static java.util.Collections.emptyList;

public class TraceContextOrSamplingFlags {
    public static final TraceContextOrSamplingFlags
            NOT_SAMPLED = new TraceContextOrSamplingFlags(3, SamplingFlags.NOT_SAMPLED, emptyList()),
            SAMPLED = new TraceContextOrSamplingFlags(3, SamplingFlags.SAMPLED, emptyList()),
            DEFERRED = new TraceContextOrSamplingFlags(3, SamplingFlags.DEFERRED, emptyList()),
            DEBUG = new TraceContextOrSamplingFlags(3, SamplingFlags.DEBUG, emptyList());

    public static TraceContextOrSamplingFlags.Builder newBuilder() {
        return new Builder(0, null, emptyList());
    }

    public static TraceContextOrSamplingFlags.Builder newBuilder(HeraSpanContext context) {
        if (context == null) throw new NullPointerException("context == null");
        return new TraceContextOrSamplingFlags.Builder(1, context, context.extra());
    }

    public static TraceContextOrSamplingFlags.Builder newBuilder(SamplingFlags flags) {
        if (flags == null) throw new NullPointerException("flags == null");
        return new TraceContextOrSamplingFlags.Builder(3, flags, emptyList());
    }

    public Boolean sampled() {
        return value.sampled();
    }

    public TraceContextOrSamplingFlags sampled(boolean sampled) {
        int flags = InternalPropagation.sampled(sampled, value.flags);
        if (flags == value.flags) return this; // save effort if no change
        return withFlags(flags);
    }

    public HeraSpanContext context() {
        return type == 1 ? (HeraSpanContext) value : null;
    }

    public SamplingFlags samplingFlags() {
        return type == 3 ? value : null;
    }

    public final TraceContextOrSamplingFlags.Builder toBuilder() {
        return new Builder(type, value, effectiveExtra());
    }

    public static TraceContextOrSamplingFlags create(HeraSpanContext context) {
        return new TraceContextOrSamplingFlags(1, context, emptyList());
    }

    public static TraceContextOrSamplingFlags create(SamplingFlags flags) {
        // reuses constants to avoid excess allocation
        if (flags == SamplingFlags.SAMPLED) return SAMPLED;
        if (flags == SamplingFlags.NOT_SAMPLED) return NOT_SAMPLED;
        if (flags == SamplingFlags.DEBUG) return DEBUG;
        return new TraceContextOrSamplingFlags(3, flags, emptyList());
    }

    public static TraceContextOrSamplingFlags create(Boolean sampled, boolean debug) {
        if (debug) return DEBUG;
        if (sampled == null) return DEFERRED;
        return sampled ? SAMPLED : NOT_SAMPLED;
    }

    final int type;
    final SamplingFlags value;
    final List<Object> extraList;

    TraceContextOrSamplingFlags(int type, SamplingFlags value, List<Object> extraList) {
        if (value == null) throw new NullPointerException("value == null");
        if (extraList == null) throw new NullPointerException("baggage == null");
        this.type = type;
        this.value = value;
        this.extraList = extraList;
    }

    /**
     * Returns a list of additional state extracted from the request. Will be non-empty when {@link
     * #context()} is {@code null}.
     *
     * @see HeraSpanContext#extra()
     * @since 4.9
     */
    public final List<Object> extra() {
        return extraList;
    }

    List<Object> effectiveExtra() {
        return type == 1 ? ((HeraSpanContext) value).extra() : extraList;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) return true;
        if (!(o instanceof TraceContextOrSamplingFlags)) return false;
        TraceContextOrSamplingFlags that = (TraceContextOrSamplingFlags) o;
        return type == that.type && value.equals(that.value)
                && effectiveExtra().equals(that.effectiveExtra());
    }

    @Override
    public int hashCode() {
        int h = 1;
        h *= 1000003;
        h ^= type;
        h *= 1000003;
        h ^= value.hashCode();
        h *= 1000003;
        h ^= effectiveExtra().hashCode();
        return h;
    }

    public static final class Builder {
        int type;
        SamplingFlags value;
        List<Object> extraList;

        Builder(int type, SamplingFlags value, List<Object> extraList) {
            this.type = type;
            this.value = value;
            this.extraList = extraList;
        }

        public final Builder context(HeraSpanContext context) {
            if (context == null) throw new NullPointerException("context == null");
            type = 1;
            value = context;
            return this;
        }

        public final Builder samplingFlags(SamplingFlags samplingFlags) {
            if (samplingFlags == null) throw new NullPointerException("samplingFlags == null");
            type = 3;
            value = samplingFlags;
            return this;
        }

        public Builder addExtra(Object extra) {
            extraList = ensureExtraAdded(extraList, extra);
            return this;
        }

        /**
         * Returns an immutable result from the values currently in the builder
         */
        public final TraceContextOrSamplingFlags build() {
            if (value == null) {
                throw new IllegalArgumentException(
                        "Value unset. Use a non-deprecated newBuilder method instead.");
            }

            final TraceContextOrSamplingFlags result;

            if (!extraList.isEmpty() && type == 1) { // move extra to the trace context
                HeraSpanContext context = (HeraSpanContext) value;
                context = InternalPropagation.INSTANCE.withExtra(context, ensureImmutable(extraList));
                result = new TraceContextOrSamplingFlags(type, context, emptyList());
            } else {
                // make sure the extra state is immutable and unmodifiable
                result = new TraceContextOrSamplingFlags(type, value, ensureImmutable(extraList));
            }

            return result; // save effort if no change
        }
    }

    TraceContextOrSamplingFlags withFlags(int flags) {
        switch (type) {
            case 1:
                HeraSpanContext context = InternalPropagation.INSTANCE.withFlags((HeraSpanContext) value, flags);
                return new TraceContextOrSamplingFlags(type, context, extraList);
            case 2:
                throw new UnsupportedOperationException("unsupported type 2");
            case 3:
                SamplingFlags samplingFlags = SamplingFlags.toSamplingFlags(flags);
                if (extraList.isEmpty()) return create(samplingFlags);
                return new TraceContextOrSamplingFlags(type, samplingFlags, extraList);
        }
        throw new AssertionError("programming error");
    }

    @Override
    public String toString() {
        List<Object> extra = effectiveExtra();
        StringBuilder result = new StringBuilder("Extracted{");
        String valueClass = value.getClass().getSimpleName();
        // Lowercase first char of class name
        result.append(Character.toLowerCase(valueClass.charAt(0)));
        result.append(valueClass, 1, valueClass.length()).append('=').append(value);
        if (type != 3) {
            String flagsString = SamplingFlags.toString(value.flags);
            if (!flagsString.isEmpty()) result.append(", samplingFlags=").append(flagsString);
        }
        if (!extra.isEmpty()) result.append(", extra=").append(extra);
        // NOTE: it would be nice to rename this type, but it would cause a major Api break:
        // This is the result of Extractor::extract which is used in a lot of 3rd party code.
        return result.append('}').toString();
    }
}
