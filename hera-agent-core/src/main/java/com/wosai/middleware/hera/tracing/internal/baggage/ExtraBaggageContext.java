/*
 * Copyright 2013-2020 The OpenZipkin Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
 * in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 */

package com.wosai.middleware.hera.tracing.internal.baggage;

import com.wosai.middleware.hera.tracing.baggage.BaggageField;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import com.wosai.middleware.hera.tracing.propagation.TraceContextOrSamplingFlags;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Most commonly, field storage is inside {@link com.wosai.middleware.hera.tracing.propagation.HeraSpanContext#extra()}.
 *
 * @see BaggageFields
 */
public final class ExtraBaggageContext extends BaggageContext {
  static final ExtraBaggageContext INSTANCE = new ExtraBaggageContext();

  public static BaggageContext get() {
    return INSTANCE;
  }

  public static List<BaggageField> getAllFields(TraceContextOrSamplingFlags extracted) {
    if (extracted.context() != null) return getAllFields(extracted.context());
    return getAllFields(extracted.extra());
  }

  public static List<BaggageField> getAllFields(HeraSpanContext context) {
    return getAllFields(context.extra());
  }

  public static Map<String, String> getAllValues(TraceContextOrSamplingFlags extracted) {
    if (extracted.context() != null) return getAllValues(extracted.context());
    return getAllValues(extracted.extra());
  }

  public static Map<String, String> getAllValues(HeraSpanContext context) {
    return getAllValues(context.extra());
  }

  @Nullable
  public static BaggageField getFieldByName(TraceContextOrSamplingFlags extracted, String name) {
    if (extracted.context() != null) return getFieldByName(extracted.context(), name);
    return getFieldByName(getAllFields(extracted.extra()), name);
  }

  @Nullable public static BaggageField getFieldByName(HeraSpanContext context, String name) {
    return getFieldByName(getAllFields(context.extra()), name);
  }

  @Override public String getValue(BaggageField field, TraceContextOrSamplingFlags extracted) {
    if (extracted.context() != null) return getValue(field, extracted.context());
    return getValue(field, extracted.extra());
  }

  @Override public String getValue(BaggageField field, HeraSpanContext context) {
    return getValue(field, context.extra());
  }

  @Override public boolean updateValue(BaggageField field, TraceContextOrSamplingFlags extracted,
    @Nullable String value) {
    if (extracted.context() != null) return updateValue(field, extracted.context(), value);
    return updateValue(field, extracted.extra(), value);
  }

  @Override public boolean updateValue(BaggageField field, HeraSpanContext context, String value) {
    return updateValue(field, context.extra(), value);
  }

  static List<BaggageField> getAllFields(List<Object> extraList) {
    BaggageFields extra = findExtra(BaggageFields.class, extraList);
    if (extra == null) return Collections.emptyList();
    return extra.getAllFields();
  }

  static Map<String, String> getAllValues(List<Object> extraList) {
    BaggageFields extra = findExtra(BaggageFields.class, extraList);
    if (extra == null) return Collections.emptyMap();
    return extra.getAllValues();
  }

  @Nullable static BaggageField getFieldByName(List<BaggageField> fields, String name) {
    if (name == null) throw new NullPointerException("name == null");
    name = name.trim();
    if (name.isEmpty()) throw new IllegalArgumentException("name is empty");
    for (BaggageField field : fields) {
      if (name.equals(field.name())) {
        return field;
      }
    }
    return null;
  }

  @Nullable static String getValue(BaggageField field, List<Object> extraList) {
    BaggageFields extra = findExtra(BaggageFields.class, extraList);
    if (extra == null) return null;
    return extra.getValue(field);
  }

  static boolean updateValue(BaggageField field, List<Object> extraList, @Nullable String value) {
    BaggageFields extra = findExtra(BaggageFields.class, extraList);
    return extra != null && extra.updateValue(field, value);
  }

  public static <T> T findExtra(Class<T> type, List<Object> extra) {
    if (type == null) throw new NullPointerException("type == null");
    for (int i = 0, length = extra.size(); i < length; i++) {
      Object nextExtra = extra.get(i);
      if (nextExtra.getClass() == type) return (T) nextExtra;
    }
    return null;
  }
}
