package com.wosai.middleware.hera.agent.metrics.api;

import io.micrometer.core.instrument.MeterRegistry;

import java.util.Collections;

public class Counter extends BaseMeter<io.micrometer.core.instrument.Counter> {
    public static Counter.Builder builder(String name) {
        return new Counter.Builder(name);
    }

    Counter(io.micrometer.core.instrument.Counter counter) {
        super(counter);
    }

    public void increment() {
        increment(1.0);
    }

    public double count() {
        return delegation.count();
    }

    public void increment(double amount) {
        delegation.increment(amount);
    }

    @Override
    public Iterable<Measurement> measure() {
        return Collections.singletonList(new Measurement(this::count, Statistic.COUNT));
    }

    public static class Builder extends AbstractBuilder<Builder, Counter> {
        Builder(String name) {
            super(name);
        }

        @Override
        protected Counter create(MeterRegistry registry) {
            return new Counter(io.micrometer.core.instrument.Counter.builder(name)
                    .tags(tags)
                    .description(description)
                    .baseUnit(baseUnit)
                    .register(registry));
        }
    }
}
