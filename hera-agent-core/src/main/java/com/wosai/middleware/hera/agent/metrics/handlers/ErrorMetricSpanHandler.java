package com.wosai.middleware.hera.agent.metrics.handlers;

import com.google.common.collect.ImmutableSet;
import com.wosai.middleware.hera.agent.metrics.Action;
import com.wosai.middleware.hera.agent.metrics.HandlerContext;
import com.wosai.middleware.hera.agent.metrics.MetricsSpanHandler;
import com.wosai.middleware.hera.agent.metrics.api.Counter;
import com.wosai.middleware.hera.agent.metrics.api.Tag;
import com.wosai.middleware.hera.tracing.internal.handler.MutableSpan;
import io.opentracing.tag.Tags;
import lombok.Builder;

import java.util.Set;

@Builder
@MetricHandlerMatcher(phase = "post")
public class ErrorMetricSpanHandler implements MetricsSpanHandler {
    private static final Set<String> HTTP_CLIENT_COMPONENT =
            ImmutableSet.of("JsonRpcClient", "HttpClient", "OKHttp", "SpringRestTemplate");

    @Override
    public Action handle(HandlerContext context) {
        final MutableSpan span = context.getSpan();
        String operationName = "UNKNOWN";
        if (HTTP_CLIENT_COMPONENT.contains(context.get("tag.component"))) {
            final Object httpUrl = span.tag(Tags.HTTP_URL.getKey());
            if (httpUrl instanceof String) {
                operationName = (String) httpUrl;
            }
        } else {
            operationName = span.name();
        }
        Tag errorTag = parseThrowable(span.error());
        Counter.builder("error_spans").description("Error Counter for each span")
                .tags(com.wosai.middleware.hera.agent.metrics.api.Tags.of(errorTag).and("operation", operationName))
                .build().increment();
        return Action.CONTINUE;
    }
}
