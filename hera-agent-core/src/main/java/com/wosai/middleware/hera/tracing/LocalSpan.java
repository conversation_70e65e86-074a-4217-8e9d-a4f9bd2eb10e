package com.wosai.middleware.hera.tracing;

import com.wosai.middleware.hera.tracing.internal.handler.MutableSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;

public class LocalSpan extends AbstractHeraSpan {
    public LocalSpan(HeraTracer tracer, HeraSpanContext context, MutableSpan state) {
        super(tracer, context, state);
        this.kind(Kind.LOCAL);
    }

    @Override
    boolean isEntry() {
        return false;
    }

    @Override
    public boolean isExit() {
        return false;
    }
}
