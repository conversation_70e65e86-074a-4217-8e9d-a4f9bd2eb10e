package com.wosai.middleware.hera.agent.metrics.handlers;

import com.google.common.collect.ImmutableSet;
import com.wosai.middleware.hera.agent.metrics.Action;
import com.wosai.middleware.hera.agent.metrics.HandlerContext;
import com.wosai.middleware.hera.agent.metrics.api.Outcome;
import com.wosai.middleware.hera.agent.metrics.api.Tag;
import com.wosai.middleware.hera.tracing.internal.handler.MutableSpan;
import io.opentracing.tag.Tags;
import lombok.experimental.SuperBuilder;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@SuperBuilder
@MetricHandlerMatcher(phase = "tag.component", arguments = {"JsonRpcClient", "HttpClient", "OKHttp", "SpringRestTemplate", "DubboConsumer"})
public class RemoteCallMetricSpanHandler extends AbstractComponentHandler {
    // HTTP_CLIENT_COMPONENT contains pure http client except rpc clients
    private static final Set<String> HTTP_CLIENT_COMPONENT =
            ImmutableSet.of("HttpClient", "OKHttp", "SpringRestTemplate");
    private final double[] percentiles;
    private final boolean enableHttpClient;

    @Override
    Action handleWithErrorTag(HandlerContext context, Tag errorTag) {
        String component = context.get("tag.component");
        if (!enableHttpClient && HTTP_CLIENT_COMPONENT.contains(component)) {
            return Action.ABORT;
        }
        final MutableSpan span = context.getSpan();
        // Tags:
        // - exception
        // - method
        // - outcome
        // - status
        // - uri: Scheme://Host:Port/Path[.RpcMethod]
        Tag methodTag = Tag.of("method", (String) span.tag(Tags.HTTP_METHOD.getKey()));
        // it is possible that http_status does not exist since it is set only if status_code >= 400
        Object httpStatus = span.tag(Tags.HTTP_STATUS.getKey());
        if (httpStatus == null) {
            httpStatus = AbstractComponentHandler.EXCEPTION_NONE_VALUE.equals(errorTag.getValue()) ? 200 : 0;
        }
        Tag outcomeTag = Outcome.forStatus((int) httpStatus).asTag();
        Tag statusTag = Tag.of("status", String.valueOf(httpStatus));
        Tag uriTag = Tag.of("uri", (String) span.tag(Tags.HTTP_URL.getKey()));

        createTimer("http.client.requests", Arrays.asList(errorTag, methodTag, outcomeTag, statusTag, uriTag))
                .record(span.duration(), TimeUnit.MICROSECONDS);
        // per http call
        createTimer("http.client.requests:call", Arrays.asList(methodTag, uriTag),
                this.percentiles)
                .record(span.duration(), TimeUnit.MICROSECONDS);
        // aggregation
        createTimer("http.client.requests:aggr", Collections.emptyList(),
                this.percentiles)
                .record(span.duration(), TimeUnit.MICROSECONDS);

        return Action.CONTINUE;
    }
}
