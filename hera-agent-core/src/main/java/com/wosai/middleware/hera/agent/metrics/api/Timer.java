package com.wosai.middleware.hera.agent.metrics.api;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.lang.Nullable;

import java.time.Duration;
import java.util.Arrays;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

public class Timer extends BaseMeter<io.micrometer.core.instrument.Timer> {
    public static Timer.Builder builder(String name) {
        return new Timer.Builder(name);
    }

    Timer(io.micrometer.core.instrument.Timer delegation) {
        super(delegation);
    }

    /**
     * Wrap a {@link Runnable} so that it is timed when invoked.
     *
     * @param f The Runnable to time when it is invoked.
     * @return The wrapped Runnable.
     */
    public Runnable wrap(Runnable f) {
        return () -> record(f);
    }

    /**
     * Wrap a {@link Callable} so that it is timed when invoked.
     *
     * @param f   The Callable to time when it is invoked.
     * @param <T> The return type of the callable.
     * @return The wrapped callable.
     */
    public <T> Callable<T> wrap(Callable<T> f) {
        return () -> recordCallable(f);
    }

    public void record(long amount, TimeUnit unit) {
        this.delegation.record(amount, unit);
    }

    public <T> T record(Supplier<T> f) {
        return this.delegation.record(f);
    }

    public <T> T recordCallable(Callable<T> f) throws Exception {
        return this.delegation.recordCallable(f);
    }

    public void record(Runnable f) {
        this.delegation.record(f);
    }

    public long count() {
        return this.delegation.count();
    }

    public double totalTime(TimeUnit unit) {
        return this.delegation.totalTime(unit);
    }

    public double max(TimeUnit unit) {
        return this.delegation.max(unit);
    }

    public TimeUnit baseTimeUnit() {
        return this.delegation.baseTimeUnit();
    }

    @Override
    public Iterable<Measurement> measure() {
        return Arrays.asList(new Measurement(() -> (double) count(), Statistic.COUNT),
                new Measurement(() -> totalTime(baseTimeUnit()), Statistic.TOTAL_TIME),
                new Measurement(() -> max(baseTimeUnit()), Statistic.MAX));
    }

    public static Sample start() {
        return new Sample(io.micrometer.core.instrument.Timer.start(MetricsHandler.getMeterRegistry()));
    }

    public static class Builder extends AbstractBuilder<Builder, Timer> {
        private Boolean enableHistogram = false;
        private double[] percentiles;

        private Duration[] serviceLevelObjectives;

        private Duration minimumExpectedValue;

        private Duration maximumExpectedValue;

        Builder(String name) {
            super(name);
        }

        public Builder publishPercentileHistogram(Boolean enable) {
            this.enableHistogram = enable;
            return this;
        }

        public Builder serviceLevelObjectives(@Nullable long... slos) {
            if (slos == null || slos.length == 0) {
                this.serviceLevelObjectives = null;
                return this;
            }
            this.serviceLevelObjectives = Arrays.stream(slos)
                    .mapToObj(Duration::ofMillis)
                    .toArray(Duration[]::new);
            return this;
        }

        public Builder minimumExpectedValue(long min) {
            this.minimumExpectedValue = Duration.ofMillis(min);
            return this;
        }

        public Builder maximumExpectedValue(long max) {
            this.maximumExpectedValue = Duration.ofMillis(max);
            return this;
        }

        public Builder publishPercentiles(double... percentiles) {
            this.percentiles = percentiles;
            return this;
        }

        @Override
        protected Timer create(MeterRegistry registry) {
            return new Timer(io.micrometer.core.instrument.Timer.builder(name)
                    .tags(tags)
                    .description(description)
                    .publishPercentileHistogram(enableHistogram)
                    .publishPercentiles(percentiles)
                    .serviceLevelObjectives(serviceLevelObjectives)
                    .minimumExpectedValue(minimumExpectedValue)
                    .maximumExpectedValue(maximumExpectedValue)
                    .register(registry));
        }
    }

    public static class Sample {
        private final io.micrometer.core.instrument.Timer.Sample sample;

        Sample(io.micrometer.core.instrument.Timer.Sample sample) {
            this.sample = sample;
        }

        public long stop(Timer timer) {
            return sample.stop(timer.delegation);
        }
    }
}
