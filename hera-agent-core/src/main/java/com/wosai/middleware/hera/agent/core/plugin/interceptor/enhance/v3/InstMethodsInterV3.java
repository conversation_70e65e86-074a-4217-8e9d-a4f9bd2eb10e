package com.wosai.middleware.hera.agent.core.plugin.interceptor.enhance.v3;

import net.bytebuddy.implementation.bind.annotation.AllArguments;
import net.bytebuddy.implementation.bind.annotation.Origin;
import net.bytebuddy.implementation.bind.annotation.RuntimeType;
import net.bytebuddy.implementation.bind.annotation.SuperCall;
import net.bytebuddy.implementation.bind.annotation.This;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.plugin.PluginException;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.EnhancedInstance;
import org.apache.skywalking.apm.agent.core.plugin.interceptor.enhance.v2.MethodInvocationContext;
import org.apache.skywalking.apm.agent.core.plugin.loader.InterceptorInstanceLoader;

import java.lang.reflect.Method;
import java.util.concurrent.Callable;

public class InstMethodsInterV3 {
    private static final ILog LOGGER = LogManager.getLogger(InstMethodsInterV3.class);

    private InstanceMethodsAroundInterceptorV3 interceptor;

    public InstMethodsInterV3(String instanceMethodsAroundInterceptorClassName, ClassLoader classLoader) {
        try {
            interceptor = InterceptorInstanceLoader.load(instanceMethodsAroundInterceptorClassName, classLoader);
        } catch (Throwable t) {
            throw new PluginException("Can't create InstanceMethodsAroundInterceptor.", t);
        }
    }

    @RuntimeType
    public Object intercept(@This Object obj, @AllArguments Object[] allArguments, @SuperCall Callable<?> zuper,
                            @Origin Method method) throws Throwable {
        EnhancedInstance targetObject = (EnhancedInstance) obj;

        MethodInvocationContext context = new MethodInvocationContext();
        Object ret = null;
        try {
            interceptor.beforeMethod(targetObject, method, allArguments, method.getParameterTypes(), context);
            if (!context.isContinue()) {
                ret = context._ret();
            } else {
                ret = zuper.call();
            }
        } catch (Throwable t) {
            return interceptor.handleMethodException(targetObject, method, allArguments, method.getParameterTypes(), t, context);
        } finally {
            try {
                ret = interceptor.afterMethod(targetObject, method, allArguments, method.getParameterTypes(), ret, context);
            } catch (Throwable t) {
                LOGGER.error(t, "class[{}] after method[{}] intercept failure", obj.getClass(), method.getName());
            }
        }
        return ret;
    }
}
