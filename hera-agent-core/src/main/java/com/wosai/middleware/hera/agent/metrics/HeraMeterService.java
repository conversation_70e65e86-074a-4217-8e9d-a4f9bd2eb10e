package com.wosai.middleware.hera.agent.metrics;

import org.apache.skywalking.apm.agent.core.boot.OverrideImplementor;
import org.apache.skywalking.apm.agent.core.meter.BaseMeter;
import org.apache.skywalking.apm.agent.core.meter.MeterService;

@OverrideImplementor(MeterService.class)
public class HeraMeterService extends MeterService {
    public <T extends BaseMeter> T register(T meter) {
        if (meter == null) {
            return null;
        }
        // TODO: what we receive here is a SkyWalking native BaseMeter instance,
        //       we have to convert it to our own meter instance.
        return meter;
    }

    @Override
    public void prepare() {
    }

    @Override
    public void boot() {
    }

    @Override
    public void run() {
    }

    @Override
    public void shutdown() {
    }
}
