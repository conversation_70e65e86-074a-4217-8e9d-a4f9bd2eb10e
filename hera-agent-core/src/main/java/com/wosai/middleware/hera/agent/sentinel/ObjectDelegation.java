package com.wosai.middleware.hera.agent.sentinel;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class ObjectDelegation<T> {
    @Getter(AccessLevel.PACKAGE)
    protected final T delegation;

    @Override
    public String toString() {
        return this.delegation.toString();
    }

    @Override
    public int hashCode() {
        return this.delegation.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj instanceof ObjectDelegation) {
            return this.delegation.equals(((ObjectDelegation<?>) obj).getDelegation());
        }

        return this.delegation.equals(obj);
    }
}
