package com.wosai.middleware.hera.tracing.propagation;

import io.opentracing.propagation.TextMap;

import java.util.Iterator;
import java.util.Map;

import static com.google.common.base.Preconditions.checkNotNull;

@FunctionalInterface
public interface TextMapInject {
    void put(String key, String value);

    class TextMapInjectAdapter implements TextMap {
        private final TextMapInject delegate;

        public TextMapInjectAdapter(TextMapInject delegate) {
            this.delegate = checkNotNull(delegate, "TextMap inject");
        }

        @Override
        public Iterator<Map.Entry<String, String>> iterator() {
            throw new UnsupportedOperationException("iterator should never be used with Tracer.inject()");
        }

        @Override
        public void put(String key, String value) {
            delegate.put(key, value);
        }
    }
}
