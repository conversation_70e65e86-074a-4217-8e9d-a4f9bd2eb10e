package com.wosai.middleware.hera.agent.tracing;

import com.wosai.middleware.hera.agent.conf.HeraConfig;
import io.jaegertracing.internal.metrics.Metrics;
import io.jaegertracing.internal.samplers.ConstSampler;
import io.jaegertracing.internal.samplers.HttpSamplingManager;
import io.jaegertracing.internal.samplers.ProbabilisticSampler;
import io.jaegertracing.internal.samplers.RateLimitingSampler;
import io.jaegertracing.internal.samplers.RemoteControlledSampler;
import io.jaegertracing.spi.Sampler;
import org.apache.skywalking.apm.agent.core.conf.Config;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;

import java.util.function.Supplier;

public class SamplerProvider implements Supplier<Sampler> {
    private static final ILog LOGGER = LogManager.getLogger(SamplerProvider.class);
    private final Metrics metrics;

    public SamplerProvider(Metrics metrics) {
        this.metrics = metrics;
    }

    @Override
    public Sampler get() {
        final Number samplerParam = HeraConfig.Jaeger.Sampler.PARAM;
        switch (HeraConfig.Jaeger.Sampler.TYPE) {
            case ConstSampler.TYPE:
                LOGGER.info("Use ConstSampler with const {}", samplerParam.intValue());
                return new ConstSampler(samplerParam.intValue() != 0);
            case ProbabilisticSampler.TYPE:
                LOGGER.info("Use ProbabilisticSampler with parameter {}", samplerParam.doubleValue());
                return new ProbabilisticSampler(samplerParam.doubleValue());
            case RateLimitingSampler.TYPE:
                LOGGER.info("Use RateLimitingSampler with parameter {}", samplerParam.intValue());
                return new RateLimitingSampler(samplerParam.intValue());
            case RemoteControlledSampler.TYPE:
                LOGGER.info("Use RemoteControlledSampler from url = {}", HeraConfig.Jaeger.Sampler.MANAGER_HOST_PORT);
                return new RemoteControlledSampler.Builder(Config.Agent.SERVICE_NAME)
                        .withSamplingManager(new HttpSamplingManager(HeraConfig.Jaeger.Sampler.MANAGER_HOST_PORT))
                        .withInitialSampler(new ProbabilisticSampler(samplerParam.doubleValue()))
                        .withMetrics(metrics)
                        .build();
            default:
                throw new IllegalArgumentException("Invalid sampling strategy " + HeraConfig.Jaeger.Sampler.TYPE);
        }
    }
}
