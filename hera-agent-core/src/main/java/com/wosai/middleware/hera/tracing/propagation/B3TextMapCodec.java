/*
 * Copyright (c) 2018, The Jaeger Authors
 * Copyright (c) 2017, Uber Technologies, Inc
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
 * in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License
 * is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
 * or implied. See the License for the specific language governing permissions and limitations under
 * the License.
 */

package com.wosai.middleware.hera.tracing.propagation;

import com.wosai.middleware.hera.tracing.HeraObjectFactory;
import com.wosai.middleware.hera.tracing.InternalPropagation;
import com.wosai.middleware.hera.tracing.spi.Codec;
import io.opentracing.propagation.TextMap;

import java.util.Collections;
import java.util.Map;

public class B3TextMapCodec implements Codec<TextMap> {
    protected static final String TRACE_ID_NAME = "X-B3-TraceId";
    protected static final String SPAN_ID_NAME = "X-B3-SpanId";
    protected static final String PARENT_SPAN_ID_NAME = "X-B3-ParentSpanId";
    protected static final String SAMPLED_NAME = "X-B3-Sampled";
    protected static final String FLAGS_NAME = "X-B3-Flags";
    // NOTE: uber's flags aren't the same as B3/Finagle ones
    protected static final byte SAMPLED_FLAG = InternalPropagation.FLAG_SAMPLED;
    // NOTE: should be valid debug + sampled
    protected static final byte DEBUG_FLAG = InternalPropagation.FLAG_DEBUG | InternalPropagation.FLAG_SAMPLED;
    private final HeraObjectFactory objectFactory;

    /**
     * @deprecated use {@link Builder} instead
     */
    @Deprecated
    public B3TextMapCodec() {
        this(new Builder());
    }

    private B3TextMapCodec(Builder builder) {
        this.objectFactory = builder.objectFactory;
    }

    @Override
    public void inject(HeraSpanContext spanContext, TextMap carrier) {
        carrier.put(TRACE_ID_NAME, // Use HexCode instead of getTraceId to ensure zipkin compatibility
                HexCodec.toLowerHex(spanContext.getTraceIdHigh(), spanContext.getTraceIdLow()));
        if (spanContext.getParentId() != 0L) { // Conventionally, parent id == 0 means the root span
            carrier.put(PARENT_SPAN_ID_NAME, HexCodec.toLowerHex(spanContext.getParentId()));
        }
        carrier.put(SPAN_ID_NAME, HexCodec.toLowerHex(spanContext.getSpanId()));
        carrier.put(SAMPLED_NAME, spanContext.sampled() ? "1" : "0");
        if (spanContext.isDebug()) {
            carrier.put(FLAGS_NAME, "1");
        }
    }

    @Override
    public TraceContextOrSamplingFlags extract(TextMap carrier) {
        Long traceIdLow = null;
        Long traceIdHigh = 0L; // It's enough to check for a null low trace id
        Long spanId = null;
        Long parentId = 0L; // Conventionally, parent id == 0 means the root span
        Boolean sampleV = null;
        boolean debug = false;
        for (Map.Entry<String, String> entry : carrier) {
            if (entry.getKey().equalsIgnoreCase(SAMPLED_NAME)) {
                String value = entry.getValue();
                if ("1".equals(value) || "true".equalsIgnoreCase(value)) {
                    sampleV = true;
                } else if ("0".equals(value) || "false".equalsIgnoreCase(value)) {
                    sampleV = false;
                }
            } else if (entry.getKey().equalsIgnoreCase(TRACE_ID_NAME)) {
                traceIdLow = HexCodec.lowerHexToUnsignedLong(entry.getValue());
                traceIdHigh = HexCodec.higherHexToUnsignedLong(entry.getValue());
            } else if (entry.getKey().equalsIgnoreCase(PARENT_SPAN_ID_NAME)) {
                parentId = HexCodec.lowerHexToUnsignedLong(entry.getValue());
            } else if (entry.getKey().equalsIgnoreCase(SPAN_ID_NAME)) {
                spanId = HexCodec.lowerHexToUnsignedLong(entry.getValue());
            } else if (entry.getKey().equalsIgnoreCase(FLAGS_NAME)) {
                if (entry.getValue().equals("1")) {
                    debug = true;
                }
            }
        }

        if (null != traceIdLow && null != parentId && null != spanId) {
            int flags = InternalPropagation.sampled(sampleV, 0);
            flags = InternalPropagation.debug(debug, flags);
            HeraSpanContext spanContext = objectFactory.createSpanContext(
                    traceIdHigh,
                    traceIdLow,
                    spanId,
                    parentId,
                    flags,
                    Collections.emptyList(),
                    null // debugId
            );
            return TraceContextOrSamplingFlags.create(spanContext);
        } else if (sampleV != null) {
            // It is ok to go without a trace ID, if sampling or debug is set
            return TraceContextOrSamplingFlags.create(sampleV, debug);
        }
        // trace context is malformed so return empty, which is equivalently a deferred decision without baggages
        return TraceContextOrSamplingFlags.DEFERRED;
    }

    public static class Builder {
        private HeraObjectFactory objectFactory = new HeraObjectFactory();

        /**
         * Specify JaegerSpanContext factory. Used for creating new span contexts. The default factory
         * is an instance of {@link HeraObjectFactory}.
         */
        public Builder withObjectFactory(HeraObjectFactory objectFactory) {
            this.objectFactory = objectFactory;
            return this;
        }

        public B3TextMapCodec build() {
            return new B3TextMapCodec(this);
        }
    }
}
