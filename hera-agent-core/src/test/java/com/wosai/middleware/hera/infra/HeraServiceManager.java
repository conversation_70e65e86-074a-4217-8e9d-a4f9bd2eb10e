package com.wosai.middleware.hera.infra;

import org.apache.skywalking.apm.agent.core.boot.BootService;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;
import org.junit.Assert;
import org.junit.rules.ExternalResource;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

public class HeraServiceManager extends ExternalResource {
    private final Map<Class, BootService> bootServices = new HashMap<>();

    @Override
    protected void after() {
        ServiceManager.INSTANCE.shutdown();
    }

    public void loadService(Class<? extends BootService> serviceClass, BootService service) {
        if (bootServices.putIfAbsent(serviceClass, service) != null) {
            Assert.fail("Duplicated service [ " + serviceClass.getName() + "] loaded.");
        }
    }

    public void loadAll() {
        // set minimal-required services to the ServiceManager
        try {
            Field field = ServiceManager.class.getDeclaredField("bootedServices");
            field.setAccessible(true);
            field.set(ServiceManager.INSTANCE, bootServices);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            Assert.fail("ServiceManager try to set bootServices fail.");
        }
        delegate("prepare");
        delegate("startup");
        delegate("onComplete");
    }

    private void delegate(final String method) {
        try {
            Method m = ServiceManager.class.getDeclaredMethod(method);
            m.setAccessible(true);
            m.invoke(ServiceManager.INSTANCE);
        } catch (NoSuchMethodException e) {
            Assert.fail("Cannot find method [ " + method + "] in the ServiceManager.");
        } catch (Exception e) {
            Assert.fail("Cannot invoke method [ " + method + "] on the ServiceManager.");
        }
    }
}
