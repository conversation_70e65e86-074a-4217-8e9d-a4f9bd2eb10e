package com.wosai.middleware.hera.agent.conf.bootstrap;

import org.junit.After;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class RunLevelManagerTest {
    @After
    public void teardown() {
        RunLevelManager.INSTANCE.reset();
    }

    @Test
    public void testRunLevelManager() {
        RunLevelManager.INSTANCE.registerRunLevel(new KubernetesRunLevel(), new DockerRunLevel(), RunLevel.Preset.FALLBACK);
        assertThat(RunLevelManager.INSTANCE.runLevelSet)
                .hasSize(3)
                .containsExactly(new KubernetesRunLevel(), new DockerRunLevel(), RunLevel.Preset.FALLBACK);
    }
}
