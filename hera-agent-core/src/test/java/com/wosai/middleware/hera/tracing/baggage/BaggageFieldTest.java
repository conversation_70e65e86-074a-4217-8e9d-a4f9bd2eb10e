package com.wosai.middleware.hera.tracing.baggage;

import com.wosai.middleware.hera.infra.HeraAgentServiceRule;
import com.wosai.middleware.hera.tracing.HeraTracer;
import com.wosai.middleware.hera.tracing.internal.baggage.BaggageContext;
import com.wosai.middleware.hera.tracing.internal.baggage.ExtraBaggageContext;
import com.wosai.middleware.hera.tracing.propagation.B3TextMapCodec;
import com.wosai.middleware.hera.tracing.propagation.Format;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import com.wosai.middleware.hera.tracing.propagation.TraceContextOrSamplingFlags;
import com.wosai.middleware.hera.tracing.spi.Codec;
import io.opentracing.Scope;
import io.opentracing.propagation.TextMap;
import io.opentracing.propagation.TextMapAdapter;
import io.opentracing.util.GlobalTracer;
import io.opentracing.util.GlobalTracerTestUtil;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;

public class BaggageFieldTest {
    static final BaggageField REQUEST_ID = BaggageField.create("requestId");
    static final BaggageField AMZN_TRACE_ID = BaggageField.create("x-amzn-trace-id");

    @Rule
    public HeraAgentServiceRule agentServiceRule = new HeraAgentServiceRule();

    Codec<TextMap> baggageCodec = BaggagePropagation.newBuilder(new B3TextMapCodec.Builder().build())
            .add(BaggagePropagationConfig.SingleBaggageField.newBuilder(REQUEST_ID).addKeyName("x-vcap-request-id").build())
            .add(BaggagePropagationConfig.SingleBaggageField.remote(AMZN_TRACE_ID)).build();

    String requestId = "abcdef";
    TraceContextOrSamplingFlags requestIdExtraction =
            baggageCodec.extract(new TextMapAdapter(Collections.singletonMap("x-vcap-request-id", requestId)));

    TraceContextOrSamplingFlags emptyExtraction = baggageCodec.extract(new TextMapAdapter(Collections.emptyMap()));
    HeraSpanContext context = HeraSpanContext.newBuilder().traceIdLow(1).spanId(2).build();
    HeraSpanContext emptyContext = baggageCodec.decorate(context);
    TraceContextOrSamplingFlags extraction = TraceContextOrSamplingFlags.create(emptyContext);
    HeraSpanContext requestIdContext =
            context.toBuilder().addExtra(requestIdExtraction.extra().get(0)).build();

    @Before
    public void init() {
        GlobalTracerTestUtil.setGlobalTracerUnconditionally(new HeraTracer.Builder("svc").build());
    }

    @After
    public void after() {
        assertThat(GlobalTracer.get().activeSpan()).isNull();
    }

    @Test
    public void internalStorage() {
        assertThat(BaggageField.create("foo").context)
                .isSameAs(ExtraBaggageContext.get());

        BaggageContext context = mock(BaggageContext.class);
        assertThat(new BaggageField("context", context).context)
                .isSameAs(context);
    }

    @Test
    public void getAll_extracted() {
        assertThat(BaggageField.getAll(emptyExtraction))
                .containsExactly(REQUEST_ID, AMZN_TRACE_ID)
                .containsExactlyElementsOf(BaggageField.getAll(extraction));
    }

    @Test
    public void getAll() {
        assertThat(BaggageField.getAll(emptyContext))
                .containsExactly(REQUEST_ID, AMZN_TRACE_ID);

        try (Scope scope = GlobalTracer.get().activateSpan(GlobalTracer.get().buildSpan("rootSpan").asChildOf(emptyContext).start())) {
            assertThat(BaggageField.getAll())
                    .containsExactly(REQUEST_ID, AMZN_TRACE_ID);
        }
    }

    @Test
    public void getAll_doesntExist() {
        assertThat(BaggageField.getAll(TraceContextOrSamplingFlags.NOT_SAMPLED)).isEmpty();
        assertThat(BaggageField.getAll(context)).isEmpty();
        assertThat(BaggageField.getAll()).isEmpty();

        try (Scope scope = GlobalTracer.get().activateSpan(GlobalTracer.get().buildSpan("rootSpan").start())) {
            assertThat(BaggageField.getAll()).isEmpty();
        }
    }

    @Test
    public void getByName_doesntExist() {
        assertThat(BaggageField.getByName(emptyContext, "robots")).isNull();
        assertThat(BaggageField.getByName("robots")).isNull();

        try (Scope scope = GlobalTracer.get().activateSpan(GlobalTracer.get().buildSpan("rootSpan").start())) {
            assertThat(BaggageField.getByName(REQUEST_ID.name())).isNull();
        }
    }

    @Test
    public void getByName() {
        assertThat(BaggageField.getByName(emptyContext, REQUEST_ID.name()))
                .isSameAs(REQUEST_ID);

        try (Scope scope = GlobalTracer.get().activateSpan(GlobalTracer.get().buildSpan("rootSpan").asChildOf(emptyContext).start())) {
            assertThat(BaggageField.getByName(REQUEST_ID.name()))
                    .isSameAs(REQUEST_ID);
        }
    }

    @Test
    public void getByName_extracted() {
        assertThat(BaggageField.getByName(emptyExtraction, REQUEST_ID.name()))
                .isSameAs(REQUEST_ID)
                .isSameAs(BaggageField.getByName(extraction, REQUEST_ID.name()));
    }

    @Test
    public void getByName_context_null() {
        // permits unguarded use of CurrentTraceContext.get()
        assertThat(BaggageField.getByName((HeraSpanContext) null, "foo"))
                .isNull();
    }

    @Test
    public void getByName_invalid() {
        assertThatThrownBy(() -> BaggageField.getByName(context, ""))
                .isInstanceOf(IllegalArgumentException.class);
        assertThatThrownBy(() -> BaggageField.getByName(context, "    "))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void trimsName() {
        assertThat(BaggageField.create(" x-foo  ").name())
                .isEqualTo("x-foo");
    }

    @Test
    public void create_invalid() {
        assertThatThrownBy(() -> BaggageField.create(null))
                .isInstanceOf(NullPointerException.class);
        assertThatThrownBy(() -> BaggageField.create(""))
                .isInstanceOf(IllegalArgumentException.class);
        assertThatThrownBy(() -> BaggageField.create("    "))
                .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void getValue_current_exists() {
        GlobalTracerTestUtil.resetGlobalTracer();
        HeraTracer t = new HeraTracer.Builder("root")
                .registerCodec(Format.Builtin.TEXT_MAP, baggageCodec)
                .build();
        GlobalTracer.registerIfAbsent(t);
        try (Scope scope = GlobalTracer.get().activateSpan(GlobalTracer.get().buildSpan("rootSpan").asChildOf(requestIdContext).start())) {
            assertThat(REQUEST_ID.getValue())
                    .isEqualTo(requestId);
        }
    }

    @Test
    public void getValue_current_doesntExist() {
        GlobalTracerTestUtil.resetGlobalTracer();
        HeraTracer t = new HeraTracer.Builder("root")
                .registerCodec(Format.Builtin.TEXT_MAP, baggageCodec)
                .build();
        GlobalTracer.registerIfAbsent(t);
        try (Scope scope = GlobalTracer.get().activateSpan(GlobalTracer.get().buildSpan("rootSpan").asChildOf(requestIdContext).start())) {
            assertThat(AMZN_TRACE_ID.getValue())
                    .isNull();
        }
    }

    @Test
    public void getValue_current_nothingCurrent() {
        assertThat(AMZN_TRACE_ID.getValue())
                .isNull();
    }

    @Test
    public void getValue_context_exists() {
        assertThat(REQUEST_ID.getValue(requestIdContext))
                .isEqualTo(requestId);
    }

    @Test
    public void getValue_context_doesntExist() {
        assertThat(AMZN_TRACE_ID.getValue(requestIdContext))
                .isNull();
        assertThat(AMZN_TRACE_ID.getValue(emptyContext))
                .isNull();
        assertThat(AMZN_TRACE_ID.getValue(context))
                .isNull();
    }

    @Test
    public void getValue_context_null() {
        // permits unguarded use of CurrentTraceContext.get()
        assertThat(REQUEST_ID.getValue((HeraSpanContext) null))
                .isNull();
    }

    @Test
    public void getValue_extracted_exists() {
        assertThat(REQUEST_ID.getValue(requestIdExtraction))
                .isEqualTo(requestId);
    }

    @Test
    public void getValue_extracted_doesntExist() {
        assertThat(AMZN_TRACE_ID.getValue(requestIdExtraction))
                .isNull();
        assertThat(AMZN_TRACE_ID.getValue(emptyExtraction))
                .isNull();
        assertThat(AMZN_TRACE_ID.getValue(TraceContextOrSamplingFlags.DEFERRED))
                .isNull();
    }

    @Test
    public void getValue_extracted_invalid() {
        assertThatThrownBy(() -> REQUEST_ID.getValue((TraceContextOrSamplingFlags) null))
                .isInstanceOf(NullPointerException.class);
    }

    @Test
    public void updateValue_current_exists() {
        GlobalTracerTestUtil.resetGlobalTracer();
        HeraTracer t = new HeraTracer.Builder("root")
                .registerCodec(Format.Builtin.TEXT_MAP, baggageCodec)
                .build();
        GlobalTracer.registerIfAbsent(t);
        try (Scope scope = GlobalTracer.get().activateSpan(GlobalTracer.get().buildSpan("rootSpan").asChildOf(requestIdContext).start())) {
            REQUEST_ID.updateValue("12345");
            assertThat(REQUEST_ID.getValue())
                    .isEqualTo("12345");
        }
    }

    @Test
    public void updateValue_current_doesntExist() {
        GlobalTracerTestUtil.resetGlobalTracer();
        HeraTracer t = new HeraTracer.Builder("root")
                .registerCodec(Format.Builtin.TEXT_MAP, baggageCodec)
                .build();
        GlobalTracer.registerIfAbsent(t);
        try (Scope scope = GlobalTracer.get().activateSpan(GlobalTracer.get().buildSpan("rootSpan").asChildOf(requestIdContext).start())) {
            AMZN_TRACE_ID.updateValue("12345");
            assertThat(AMZN_TRACE_ID.getValue())
                    .isEqualTo("12345");
        }
    }

    @Test
    public void updateValue_current_nothingCurrent() {
        AMZN_TRACE_ID.updateValue("12345");
        assertThat(AMZN_TRACE_ID.getValue())
                .isNull();
    }

    @Test
    public void updateValue_context_exists() {
        REQUEST_ID.updateValue(requestIdContext, "12345");
        assertThat(REQUEST_ID.getValue(requestIdContext))
                .isEqualTo("12345");
    }

    @Test
    public void updateValue_context_doesntExist() {
        AMZN_TRACE_ID.updateValue(requestIdContext, "12345");
        assertThat(AMZN_TRACE_ID.getValue(requestIdContext))
                .isEqualTo("12345");

        AMZN_TRACE_ID.updateValue(emptyContext, "12345");
        assertThat(AMZN_TRACE_ID.getValue(emptyContext))
                .isEqualTo("12345");

        AMZN_TRACE_ID.updateValue(context, "12345");
        assertThat(AMZN_TRACE_ID.getValue(context))
                .isNull();
    }

    @Test
    public void updateValue_context_null() {
        // permits unguarded use of CurrentTraceContext.get()
        REQUEST_ID.updateValue((HeraSpanContext) null, null);
    }

    @Test
    public void updateValue_extracted_exists() {
        REQUEST_ID.updateValue(requestIdExtraction, "12345");
        assertThat(REQUEST_ID.getValue(requestIdExtraction))
                .isEqualTo("12345");
    }

    @Test
    public void updateValue_extracted_doesntExist() {
        AMZN_TRACE_ID.updateValue(requestIdExtraction, "12345");
        assertThat(AMZN_TRACE_ID.getValue(requestIdExtraction))
                .isEqualTo("12345");

        AMZN_TRACE_ID.updateValue(emptyExtraction, "12345");
        assertThat(AMZN_TRACE_ID.getValue(emptyExtraction))
                .isEqualTo("12345");

        AMZN_TRACE_ID.updateValue(TraceContextOrSamplingFlags.DEFERRED, "12345");
    }

    @Test
    public void updateValue_extracted_invalid() {
        assertThatThrownBy(() -> REQUEST_ID.updateValue((TraceContextOrSamplingFlags) null, null))
                .isInstanceOf(NullPointerException.class);
    }

    @Test
    public void toString_onlyHasName() {
        assertThat(BaggageField.create("Foo"))
                .hasToString("BaggageField{Foo}"); // case preserved as that's the field name
    }

    /**
     * Ensures only lower-case name comparison is used in equals and hashCode. This allows {@link
     * BaggagePropagation} to deduplicate and {@link BaggageFields} to use these as keys.
     */
    @Test
    public void equalsAndHashCode() {
        // same field are equivalent
        BaggageField field = BaggageField.create("foo");
        assertThat(field).isEqualTo(field);
        assertThat(field).hasSameHashCodeAs(field);

        // different case format is equivalent
        BaggageField sameName = BaggageField.create("fOo");
        assertThat(field).isEqualTo(sameName);
        assertThat(sameName).isEqualTo(field);
        assertThat(field).hasSameHashCodeAs(sameName);

        // different values are not equivalent
        BaggageField differentValue = BaggageField.create("different");
        assertThat(field).isNotEqualTo(differentValue);
        assertThat(differentValue).isNotEqualTo(field);
        assertThat(field.hashCode()).isNotEqualTo(differentValue.hashCode());
    }
}
