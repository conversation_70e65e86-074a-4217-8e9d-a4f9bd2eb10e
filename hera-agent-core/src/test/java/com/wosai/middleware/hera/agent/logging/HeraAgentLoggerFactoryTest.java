package com.wosai.middleware.hera.agent.logging;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.exc.InvalidDefinitionException;
import org.assertj.core.api.Assertions;
import org.junit.Test;

public class HeraAgentLoggerFactoryTest {
    @Test(expected = InvalidDefinitionException.class)
    public void expectExceptionOnEmptyBean_givenDefaultObjectMapper() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.writeValueAsString(new EmptyBean());
    }

    @Test
    public void expectNoExceptionOnEmptyBean_givenObjectMapper_whenFailOnEmptyBeanDisabled() {
        Assertions.assertThatCode(() -> {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
            objectMapper.writeValueAsString(new EmptyBean());
        }).doesNotThrowAnyException();
    }

    public static class EmptyBean {
        // Intend to leave blank
    }
}
