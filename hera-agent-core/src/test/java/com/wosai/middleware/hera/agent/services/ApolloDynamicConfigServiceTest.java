package com.wosai.middleware.hera.agent.services;

import com.google.common.collect.ImmutableMap;
import com.wosai.middleware.hera.agent.conf.apollo.ApolloClient;
import com.wosai.middleware.hera.agent.conf.dynamic.watcher.SingleConfigWatcherAdapter;
import com.wosai.middleware.hera.infra.HeraAgentServiceRule;
import com.wosai.middleware.hera.infra.HeraJUnit4Runner;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;
import org.apache.skywalking.apm.agent.core.conf.Config;
import org.apache.skywalking.apm.agent.core.conf.dynamic.AgentConfigChangeWatcher;
import org.apache.skywalking.apm.agent.core.conf.dynamic.ConfigurationDiscoveryService;
import org.apache.skywalking.apm.agent.core.conf.dynamic.watcher.SamplingRateWatcher;
import org.apache.skywalking.apm.agent.test.helper.FieldGetter;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RunWith(HeraJUnit4Runner.class)
public class ApolloDynamicConfigServiceTest {

    @Rule
    public HeraAgentServiceRule agentServiceRule = new HeraAgentServiceRule();

    private ApolloDynamicConfigService service;

    private ApolloClient apolloClient;

    private List<?> watcherListInApolloClient;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        Config.StatusCheck.MAX_RECURSIVE_DEPTH = 1;
        service = (ApolloDynamicConfigService) ServiceManager.INSTANCE.findService(ConfigurationDiscoveryService.class);
        apolloClient = service.apolloClient;
        watcherListInApolloClient = FieldGetter.getValue(apolloClient, "watcherList");
    }

    @Test
    public void testRegister() {

        int size = watcherListInApolloClient.size();

        String propertyKey = "plugin.jdbc.trace_sql_parameters";
        AgentConfigChangeWatcher watcher = getWatcher(propertyKey);

        service.registerAgentConfigChangeWatcher(watcher);

        Assert.assertEquals(size + 1, watcherListInApolloClient.size());

        List<SingleConfigWatcherAdapter> singleConfigWatcherAdapters = (List<SingleConfigWatcherAdapter>) watcherListInApolloClient.stream().filter(item -> item instanceof SingleConfigWatcherAdapter).collect(Collectors.toList());
        Assert.assertNotEquals(0, singleConfigWatcherAdapters.size());
        Assert.assertTrue(singleConfigWatcherAdapters.stream().anyMatch(item -> item.getKeySetOfInterest().contains(propertyKey)));
    }

    @Test
    public void testRegisterAgentConfigChangeWatcher() {
        int size = watcherListInApolloClient.size();

        // subclass of AgentConfigChangeWatcher
        SamplingRateWatcher samplingRateWatcher = new SamplingRateWatcher("test-propertyKey", null);

        service.registerAgentConfigChangeWatcher(samplingRateWatcher);
        Assert.assertEquals(watcherListInApolloClient.size(), size + 1);
        List<?> singleConfigWatcherAdapters = watcherListInApolloClient.stream().filter(item -> item instanceof SingleConfigWatcherAdapter).collect(Collectors.toList());
        Assert.assertNotEquals(singleConfigWatcherAdapters.size(), 0);
        Assert.assertTrue(((List<SingleConfigWatcherAdapter>) singleConfigWatcherAdapters).stream().anyMatch(item -> item.getKeySetOfInterest().contains("test-propertyKey")));
    }

    @Test
    public void testRegisterAbstractAgentConfigChangeWatcher() {
        int size = watcherListInApolloClient.size();

        // subclass of AbstractAgentConfigChangeWatcher
        SingleConfigWatcherAdapter singleConfigWatcher = SingleConfigWatcherAdapter.wrap(getWatcher("test-propertyKey"));
        service.registerAgentConfigChangeWatcher(singleConfigWatcher);
        Assert.assertEquals(watcherListInApolloClient.size(), size + 1);
        List<?> singleConfigWatcherAdapters = watcherListInApolloClient.stream().filter(item -> item instanceof SingleConfigWatcherAdapter).collect(Collectors.toList());
        Assert.assertNotEquals(singleConfigWatcherAdapters.size(), 0);
        Assert.assertTrue(((List<SingleConfigWatcherAdapter>) singleConfigWatcherAdapters).stream().anyMatch(item -> item.getKeySetOfInterest().contains("test-propertyKey")));
    }

    @Test
    public void testConfigUpdate() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        Assert.assertEquals(new Integer(1), Config.StatusCheck.MAX_RECURSIVE_DEPTH);

        Method onNewValueMapReceived = apolloClient.getClass().getDeclaredMethod("onNewValueMapReceived", Map.class);
        onNewValueMapReceived.setAccessible(true);
        onNewValueMapReceived.invoke(apolloClient, ImmutableMap.of("statuscheck.max_recursive_depth", "2"));

        Assert.assertEquals("2", apolloClient.getConfigMap().get("statuscheck.max_recursive_depth"));
        Assert.assertEquals(new Integer(2), Config.StatusCheck.MAX_RECURSIVE_DEPTH);
    }

    @NotNull
    private AgentConfigChangeWatcher getWatcher(String propertyKey) {
        return new AgentConfigChangeWatcher(propertyKey) {
            @Override
            public void notify(ConfigChangeEvent configChangeEvent) {
                //do nothing
            }

            @Override
            public String value() {
                return "nothing";
            }
        };
    }

}
