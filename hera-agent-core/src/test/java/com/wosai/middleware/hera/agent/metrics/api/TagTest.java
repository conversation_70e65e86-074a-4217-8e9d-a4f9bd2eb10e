package com.wosai.middleware.hera.agent.metrics.api;

import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import org.assertj.core.util.Lists;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class TagTest {
    @Before
    public void setup() {
        MetricsHandler.defaultWithInMemoryRegistry();
    }

    @After
    public void teardown() {
        AbstractBuilder.resetMeterRegistry();
    }

    @Test
    public void testTag() {
        Tag tag = Tag.of("cache", "abc");
        assertThat(tag).isNotNull();
    }

    @Test
    public void testBuildAndEqual() {
        Tag tag = Tag.of("cache", "abc");
        assertThat(tag).isNotNull();
        io.micrometer.core.instrument.Tag nativeTag = tag.build();
        assertThat(tag.equals(nativeTag)).isTrue();
    }

    @Test
    public void testCollectionContainsAll_Positive() {
        List<io.micrometer.core.instrument.Tag> nativeTags = Lists.list(
                io.micrometer.core.instrument.Tag.of("cache", "a"),
                io.micrometer.core.instrument.Tag.of("http", "/path"),
                io.micrometer.core.instrument.Tag.of("mq.broker", "user.all")
        );

        List<Tag> tags = Lists.list(
                Tag.of("cache", "a"),
                Tag.of("http", "/path"),
                Tag.of("mq.broker", "user.all")
        );

        assertThat(nativeTags.containsAll(tags)).isTrue();
    }

    @Test
    public void testCollectionContainsAll_Negative() {
        List<io.micrometer.core.instrument.Tag> nativeTags = Lists.list(
                io.micrometer.core.instrument.Tag.of("cache", "a"),
                io.micrometer.core.instrument.Tag.of("http", "/path"),
                io.micrometer.core.instrument.Tag.of("mq.broker", "user.all")
        );

        List<Tag> tags = Lists.list(
                Tag.of("cache", "a"),
                Tag.of("http", "/path"),
                Tag.of("mq.broker", "user.all.2")
        );

        assertThat(nativeTags.containsAll(tags)).isFalse();
    }

    @Test
    public void testCollectionContainsAll_Positive_DifferentOrder() {
        List<io.micrometer.core.instrument.Tag> nativeTags = Lists.list(
                io.micrometer.core.instrument.Tag.of("cache", "a"),
                io.micrometer.core.instrument.Tag.of("http", "/path"),
                io.micrometer.core.instrument.Tag.of("mq.broker", "user.all")
        );

        List<Tag> tags = Lists.list(
                Tag.of("cache", "a"),
                Tag.of("mq.broker", "user.all"),
                Tag.of("http", "/path")
        );

        assertThat(nativeTags.containsAll(tags)).isTrue();
    }
}
