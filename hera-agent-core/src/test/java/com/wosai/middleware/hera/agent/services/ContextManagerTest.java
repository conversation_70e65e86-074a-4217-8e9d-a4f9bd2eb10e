package com.wosai.middleware.hera.agent.services;

import com.wosai.middleware.hera.infra.HeraAgentServiceRule;
import com.wosai.middleware.hera.infra.HeraJUnit4Runner;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.HeraTracer;
import com.wosai.middleware.hera.tracing.baggage.BaggageField;
import com.wosai.middleware.hera.tracing.baggage.BaggagePropagation;
import com.wosai.middleware.hera.tracing.baggage.BaggagePropagationConfig;
import com.wosai.middleware.hera.tracing.network.InMemoryReporter;
import com.wosai.middleware.hera.tracing.propagation.B3TextMapCodec;
import com.wosai.middleware.hera.tracing.propagation.Format;
import com.wosai.middleware.hera.tracing.spi.Codec;
import io.jaegertracing.internal.samplers.ConstSampler;
import io.opentracing.propagation.TextMap;
import io.opentracing.tag.Tags;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;

@RunWith(HeraJUnit4Runner.class)
public class ContextManagerTest {
    private HeraTracer tracer;
    private InMemoryReporter reporter;

    @Rule
    public HeraAgentServiceRule agentServiceRule = new HeraAgentServiceRule();

    @Before
    public void setup() {
        final BaggageField xFakeFlag = BaggageField.create("fake");
        final Codec<TextMap> baggageCodec = BaggagePropagation.newBuilder(new B3TextMapCodec.Builder().build())
                .add(BaggagePropagationConfig.SingleBaggageField.remote(xFakeFlag))
                .build();
        this.reporter = new InMemoryReporter();
        tracer = new HeraTracer.Builder("JaegerTester").withSampler(new ConstSampler(true))
                .withReporter(this.reporter)
                .registerCodec(Format.Builtin.TEXT_MAP, baggageCodec)
                .build();
        ContextManager.installTracer(tracer);
    }

    // EntrySpan

    @Test
    public void testAsyncEntrySpanWithContextManager_asyncFinishAfterSyncFinish() throws InterruptedException {
        AbstractHeraSpan span = ContextManager.createEntrySpan("asyncSpan");
        span.setBaggageItem("fake", "1");
        assertThat(tracer.scopeManager().activeSpan()).isEqualTo(span);
        // prepare async
        ContextManager.prepareAsyncSpan(span);
        assertThat(reporter.getSpans()).hasSize(0);
        assertThat(ContextManager.getBaggageItem("fake", "0")).isEqualTo("1");
        assertThat(ContextManager.getBaggageItem("X-ENV-FLAG", "false")).isEqualTo("false");
        ContextManager.stopSpan();
        assertThat(reporter.getSpans()).hasSize(0);
        assertThat(tracer.scopeManager().activeSpan()).isNull();
        // after calling stopSpan() on this thread, we should not be able to getBaggageItem on this thread.
        assertThat(ContextManager.getBaggageItem("fake", "0")).isEqualTo("0");
        final AbstractHeraSpan finalSpan = span;
        // asyncFinish in another thread
        Thread t = new Thread(() -> {
            assertThat(finalSpan.getBaggageItem("fake")).isEqualTo("1");
            finalSpan.asyncFinish();
        });
        t.start();
        t.join();
        assertThat(reporter.getSpans()).hasSize(1);
        assertThat(ContextManager.getBaggageItem("fake", "0")).isEqualTo("0");
    }

    @Test
    public void testAsyncEntrySpan_asyncFinishAfterPartialSyncFinish() throws Exception {
        // Tomcat/Jetty
        AbstractHeraSpan tomcatSpan = ContextManager.createEntrySpan("GET:/metrics?q=111111");
        // Spring MVC - probably WebFlux
        AbstractHeraSpan springMVCSpan = ContextManager.createEntrySpan("GET:/metrics");
        springMVCSpan.setBaggageItem("fake", "1");
        // prepare for async
        springMVCSpan.prepareForAsync();
        // finish Spring MVC span
        ContextManager.stopSpan();
        Thread t = new Thread(() -> {
            Tags.HTTP_STATUS.set(springMVCSpan, 200);
            springMVCSpan.asyncFinish();
        });
        t.start();
        t.join();
        assertThat(reporter.getSpans()).hasSize(0);
        assertThat(ContextManager.getBaggageItem("fake", "0")).isEqualTo("1");
        ContextManager.stopSpan();
        assertThat(reporter.getSpans()).hasSize(1);
        assertThat(reporter.getSpans().get(0).getName()).isEqualTo("GET:/metrics");
        assertThat(reporter.getSpans().get(0).getTags().get("http.status_code")).isEqualTo(200);
        assertThat(ContextManager.getBaggageItem("fake", "0")).isEqualTo("0");
    }

    @Test
    public void testAsyncEntrySpan_asyncFinishAfterCompleteSyncFinish() throws Exception {
        // Tomcat/Jetty
        AbstractHeraSpan tomcatSpan = ContextManager.createEntrySpan("GET:/metrics?q=111111");
        // Spring MVC - probably WebFlux
        AbstractHeraSpan springMVCSpan = ContextManager.createEntrySpan("GET:/metrics");
        springMVCSpan.setBaggageItem("fake", "1");
        // prepare for async
        springMVCSpan.prepareForAsync();
        // finish Spring MVC span
        ContextManager.stopSpan();
        ContextManager.stopSpan();
        assertThat(reporter.getSpans()).hasSize(0);
        Thread t = new Thread(() -> {
            Tags.HTTP_STATUS.set(springMVCSpan, 200);
            springMVCSpan.asyncFinish();
        });
        t.start();
        t.join();
        assertThat(reporter.getSpans()).hasSize(1);
        assertThat(reporter.getSpans().get(0).getName()).isEqualTo("GET:/metrics");
        assertThat(reporter.getSpans().get(0).getTags().get("http.status_code")).isEqualTo(200);
        assertThat(ContextManager.getBaggageItem("fake", "0")).isEqualTo("0");
    }

    // ExitSpan

    @Test
    public void testAsyncExitSpanWithContextManager_asyncFinishAfterSyncFinish() throws InterruptedException {
        // Lettuce
        AbstractHeraSpan span = ContextManager.createExitSpan("exits", "127.0.0.1:6379");
        span.setBaggageItem("fake", "1");
        assertThat(tracer.scopeManager().activeSpan()).isEqualTo(span);
        // prepare async
        ContextManager.prepareAsyncSpan(span);
        assertThat(reporter.getSpans()).hasSize(0);
        assertThat(ContextManager.getBaggageItem("fake", "0")).isEqualTo("1");
        ContextManager.stopSpan();
        assertThat(reporter.getSpans()).hasSize(0);
        assertThat(tracer.scopeManager().activeSpan()).isNull();
        assertThat(ContextManager.getBaggageItem("fake", "0")).isEqualTo("0");
        final AbstractHeraSpan finalSpan = span;
        // asyncFinish in another thread
        Thread t = new Thread(finalSpan::asyncFinish);
        t.start();
        t.join();
        assertThat(reporter.getSpans()).hasSize(1);
        assertThat(ContextManager.getBaggageItem("fake", "0")).isEqualTo("0");
    }

    @Test
    public void testAsyncSpanWithContextManager_givenSpanIsNotActivated() {
        assertThatCode(() -> {
            AbstractHeraSpan span = tracer.buildSpan("asyncSpan").withTag(Tags.SPAN_KIND, Tags.SPAN_KIND_SERVER).start();
            ContextManager.prepareAsyncSpan(span);
        }).hasMessage("Span is not the active in current context.");
    }

    @Test
    public void testSyncSpan() {
        AbstractHeraSpan span = ContextManager.createEntrySpan("testGetBaggageItem");
        span.setBaggageItem("fake", "1");
        assertThat(ContextManager.getBaggageItem("fake", "0")).isEqualTo("1");
        assertThat(ContextManager.getBaggageItem("X-ENV-FLAG", "false")).isEqualTo("false");
        ContextManager.stopSpan();
        assertThat(ContextManager.getBaggageItem("fake", "0")).isEqualTo("0");
    }
}
