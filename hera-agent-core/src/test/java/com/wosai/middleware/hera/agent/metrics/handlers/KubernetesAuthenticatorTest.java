package com.wosai.middleware.hera.agent.metrics.handlers;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.wosai.middleware.hera.agent.conf.bootstrap.KubernetesContext;
import io.jaegertracing.internal.clock.Clock;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.time.Duration;
import java.time.Instant;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.exactly;
import static com.github.tomakehurst.wiremock.client.WireMock.matchingJsonPath;
import static com.github.tomakehurst.wiremock.client.WireMock.okJson;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.reset;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options;
import static com.github.tomakehurst.wiremock.stubbing.Scenario.STARTED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.when;

public class KubernetesAuthenticatorTest {
    @Rule
    public MockitoRule rule = MockitoJUnit.rule();
    @Rule
    public WireMockRule wireMockRule = new WireMockRule(options().dynamicPort().containerThreads(7));
    private KubernetesAuthenticator authenticator;
    @Mock
    private KubernetesContext.ServiceAccountTokenProvider provider;
    @Mock
    private Clock clock;

    @Before
    public void setup() {
        authenticator = new KubernetesAuthenticator(wireMockRule.baseUrl(), Duration.ofMinutes(1), Duration.ofMinutes(1), null, clock);
    }

    private static void setUpTokenReviewOnceScenario(final String requestToken) {
        stubFor(post(urlPathEqualTo(KubernetesAuthenticator.TOKEN_REVIEW_API))
                .withHeader("Authorization", equalTo(KubernetesAuthenticator.TOKEN_PREFIX + requestToken))
                .withRequestBody(matchingJsonPath("$.spec.token", equalTo(requestToken)))
                .willReturn(okJson("{\n" +
                        "    \"kind\": \"TokenReview\",\n" +
                        "    \"apiVersion\": \"authentication.k8s.io/v1\",\n" +
                        "    \"metadata\": {\n" +
                        "        \"creationTimestamp\": null,\n" +
                        "        \"managedFields\": [\n" +
                        "            {\n" +
                        "                \"manager\": \"PostmanRuntime\",\n" +
                        "                \"operation\": \"Update\",\n" +
                        "                \"apiVersion\": \"authentication.k8s.io/v1\",\n" +
                        "                \"time\": \"2022-09-05T10:50:57Z\",\n" +
                        "                \"fieldsType\": \"FieldsV1\",\n" +
                        "                \"fieldsV1\": {\n" +
                        "                    \"f:spec\": {\n" +
                        "                        \"f:token\": {}\n" +
                        "                    }\n" +
                        "                }\n" +
                        "            }\n" +
                        "        ]\n" +
                        "    },\n" +
                        "    \"spec\": {\n" +
                        "        \"token\": \"" + requestToken + "\"\n" +
                        "    },\n" +
                        "    \"status\": {\n" +
                        "        \"authenticated\": true,\n" +
                        "        \"user\": {\n" +
                        "            \"username\": \"system:serviceaccount:monitoring-system:vmagent\",\n" +
                        "            \"uid\": \"3aab298f-54c8-4513-90f3-d06e86e4d0de\",\n" +
                        "            \"groups\": [\n" +
                        "                \"system:serviceaccounts\",\n" +
                        "                \"system:serviceaccounts:monitoring-system\",\n" +
                        "                \"system:authenticated\"\n" +
                        "            ]\n" +
                        "        },\n" +
                        "        \"audiences\": [\n" +
                        "            \"https://kubernetes.default.svc\"\n" +
                        "        ]\n" +
                        "    }\n" +
                        "}"))
        );
    }

    private static void setUpTokenReviewRetryScenario(final String requestToken) {
        stubFor(post(urlPathEqualTo(KubernetesAuthenticator.TOKEN_REVIEW_API))
                .withHeader("Authorization", equalTo(KubernetesAuthenticator.TOKEN_PREFIX + requestToken))
                .withRequestBody(matchingJsonPath("$.spec.token", equalTo(requestToken)))
                .inScenario("Retry Scenario")
                .whenScenarioStateIs(STARTED)
                .willReturn(aResponse().withStatus(401))
                .willSetStateTo("Cause Success"));

        stubFor(post(urlPathEqualTo(KubernetesAuthenticator.TOKEN_REVIEW_API))
                .withHeader("Authorization", equalTo(KubernetesAuthenticator.TOKEN_PREFIX + requestToken))
                .withRequestBody(matchingJsonPath("$.spec.token", equalTo(requestToken)))
                .inScenario("Retry Scenario")
                .whenScenarioStateIs("Cause Success")
                .willReturn(okJson("{\n" +
                        "    \"kind\": \"TokenReview\",\n" +
                        "    \"apiVersion\": \"authentication.k8s.io/v1\",\n" +
                        "    \"metadata\": {\n" +
                        "        \"creationTimestamp\": null,\n" +
                        "        \"managedFields\": [\n" +
                        "            {\n" +
                        "                \"manager\": \"PostmanRuntime\",\n" +
                        "                \"operation\": \"Update\",\n" +
                        "                \"apiVersion\": \"authentication.k8s.io/v1\",\n" +
                        "                \"time\": \"2022-09-05T10:50:57Z\",\n" +
                        "                \"fieldsType\": \"FieldsV1\",\n" +
                        "                \"fieldsV1\": {\n" +
                        "                    \"f:spec\": {\n" +
                        "                        \"f:token\": {}\n" +
                        "                    }\n" +
                        "                }\n" +
                        "            }\n" +
                        "        ]\n" +
                        "    },\n" +
                        "    \"spec\": {\n" +
                        "      \"token\": \"" + requestToken + "\"\n" +
                        "    },\n" +
                        "    \"status\": {\n" +
                        "        \"authenticated\": true,\n" +
                        "        \"user\": {\n" +
                        "            \"username\": \"system:serviceaccount:monitoring-system:vmagent\",\n" +
                        "            \"uid\": \"3aab298f-54c8-4513-90f3-d06e86e4d0de\",\n" +
                        "            \"groups\": [\n" +
                        "                \"system:serviceaccounts\",\n" +
                        "                \"system:serviceaccounts:monitoring-system\",\n" +
                        "                \"system:authenticated\"\n" +
                        "            ]\n" +
                        "        },\n" +
                        "        \"audiences\": [\n" +
                        "            \"https://kubernetes.default.svc\"\n" +
                        "        ]\n" +
                        "    }\n" +
                        "}"))
        );
    }

    private static void setUpTokenReviewRetryFailureScenario(final String requestToken, final String saToken1, final String saToken2) {
        stubFor(post(urlPathEqualTo(KubernetesAuthenticator.TOKEN_REVIEW_API))
                .withHeader("Authorization", equalTo(KubernetesAuthenticator.TOKEN_PREFIX + saToken1))
                .withRequestBody(matchingJsonPath("$.spec.token", equalTo(requestToken)))
                .inScenario("Retry Scenario")
                .whenScenarioStateIs(STARTED)
                .willReturn(aResponse().withStatus(401))
                .willSetStateTo("Cause Failure Again"));

        stubFor(post(urlPathEqualTo(KubernetesAuthenticator.TOKEN_REVIEW_API))
                .withHeader("Authorization", equalTo(KubernetesAuthenticator.TOKEN_PREFIX + saToken2))
                .withRequestBody(matchingJsonPath("$.spec.token", equalTo(requestToken)))
                .inScenario("Cause Failure Again")
                .whenScenarioStateIs(STARTED)
                .willReturn(aResponse().withStatus(401)));
    }

    @Test
    public void testServiceAccount() {
        final String vmAgentToken = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        setUpTokenReviewOnceScenario(vmAgentToken);
        boolean authed = authenticator.checkToken(KubernetesAuthenticator.TOKEN_PREFIX + vmAgentToken);
        verify(exactly(1), postRequestedFor(urlPathEqualTo(KubernetesAuthenticator.TOKEN_REVIEW_API)));
        Mockito.verify(provider, never()).token();
        assertThat(authenticator.legalTokenRef.get()).isNotNull();
        assertThat(authed).isTrue();
    }

    @Test
    public void testShortLivedVMAgentToken() {
        final long issuedAt = **********L;
        final String vmAgentToken = "eyJhbGciOiJSUzI1NiIsImtpZCI6ImxlbV9FeElQekwtMEZKSGlkMlA4cnBzeW9YNXdodWtmd1FxbS1mOEl2NFEifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.No5eW28KQMiIS9c4KjnV0acaWVn3FMBTL7Xz4E3hyuLG8sa9bLMfmpSZJyzMu7vBBh_sDpBy2h-MEs1cEiV9Fx9_lafCcA2O_mB2i6bm5hqMFWSwlb01LnWbvYwUsw1_-8wYCk-dChh3L3WTV7Qna66jogTJRUC5uwX8WG5Jhmm0VtokKej3_EKBuSylHt00q4i1mAwnJQCIDyiZ0Bo6UpJ9UQzOiaNqbK88sby1rot70hujwROdeg5dJjuzYWCkzsRiA4wYNl61l8Qdf3w3ufKzWkpF-9rDUWtgFDLtE2B_E9XPJH-k7tuFw3reKzBHl0huQHY0_Ul-zzg6mkn1EA";
        setUpTokenReviewOnceScenario(vmAgentToken);
        boolean authed = authenticator.checkToken(KubernetesAuthenticator.TOKEN_PREFIX + vmAgentToken);
        verify(exactly(1), postRequestedFor(urlPathEqualTo(KubernetesAuthenticator.TOKEN_REVIEW_API)));
        Mockito.verify(provider, never()).token();
        reset();
        assertThat(authenticator.legalTokenRef.get()).isNotNull();
        assertThat(authed).isTrue();

        // test cache expiry
        // advance 1800s ~ 30min
        when(clock.currentTimeMicros()).thenReturn((issuedAt + 1800) * 1000_000);
        assertThat(authenticator.getValidTokenFromCache()).isNotEmpty();

        // advance 3600s ~ 60min
        when(clock.currentTimeMicros()).thenReturn((issuedAt + 3601) * 1000_000);
        assertThat(authenticator.getValidTokenFromCache()).isNull();
        assertThat(authenticator.legalTokenRef.get()).isNull();
    }

    static String createLegacyToken() {
        return JWT.create().withIssuer("kubernetes/serviceaccount")
                .withClaim("kubernetes.io/serviceaccount/namespace", "sqb")
                .sign(Algorithm.none());
    }

    static String createBoundToken(Instant issuedAt, Instant expiresAt) {
        return JWT.create().withIssuer("kubernetes/serviceaccount")
                .withIssuedAt(issuedAt)
                .withExpiresAt(expiresAt)
                .withClaim("kubernetes.io/serviceaccount/namespace", "sqb")
                .sign(Algorithm.none());
    }
}
