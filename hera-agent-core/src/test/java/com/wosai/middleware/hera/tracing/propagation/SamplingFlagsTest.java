package com.wosai.middleware.hera.tracing.propagation;

import com.wosai.middleware.hera.tracing.InternalPropagation;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class SamplingFlagsTest {
    @Test
    public void testDownCastDeferredAndSampled() {
        assertThat(SamplingFlags.toSamplingFlags(InternalPropagation.FLAG_DEFERRED | InternalPropagation.FLAG_SAMPLED)).isEqualTo(SamplingFlags.SAMPLED);
    }

    @Test
    public void testDownCastDebugAndDeferred() {
        assertThat(SamplingFlags.toSamplingFlags(InternalPropagation.FLAG_DEFERRED | InternalPropagation.FLAG_DEBUG)).isEqualTo(SamplingFlags.DEFERRED);
    }

    @Test
    public void testDownCastDebugAndNotSampled() {
        assertThat(SamplingFlags.toSamplingFlags(InternalPropagation.FLAG_DEBUG)).isEqualTo(SamplingFlags.NOT_SAMPLED);
    }
}
