package com.wosai.middleware.hera.agent.sentinel;

import com.alibaba.csp.sentinel.metric.extension.MetricExtensionProvider;
import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.AbstractBuilder;
import com.wosai.middleware.hera.agent.services.SentinelContextManager;
import com.wosai.middleware.hera.infra.HeraJUnit4Runner;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.catchException;

@RunWith(HeraJUnit4Runner.class)
public class SentinelPrometheusMetricsTest {

    @Before
    public void setup() throws Exception {
        MetricsHandler.defaultWithInMemoryRegistry();
        SentinelPrometheusExtension.initExtension();
        SentinelContextManager.loadRules(Collections.singletonList(FlowRule.initFlowRule("hello", null, 0)));
    }

    @After
    public void after() throws NoSuchFieldException, IllegalAccessException {
        // reset metricExtensions
        Field metricExtensions = MetricExtensionProvider.class.getDeclaredField("metricExtensions");
        metricExtensions.setAccessible(true);
        metricExtensions.set(MetricExtensionProvider.class, new ArrayList<>());
        AbstractBuilder.resetMeterRegistry();
    }

    @Test
    public void test_withSentinelBlockMetric() {
        Exception exception = catchException(() ->
                SentinelContextManager.entry("hello", 2, EntryType.IN, null));
        assertThat(exception).isNotNull().isInstanceOf(BlockException.class);
        assertThat(MetricsHandler.getMeters()).hasSize(1);
        assertThat(MetricsHandler.find("sentinel.block.requests.total").counter().count()).isEqualTo(1.0);
    }

    @Test
    public void test_withSentinelPassMetric() {
        Exception exception = catchException(() ->
                SentinelContextManager.entry("helloFallse", 2, EntryType.IN, null));
        assertThat(exception).isNull();
        assertThat(MetricsHandler.getMeters()).hasSize(2);
        assertThat(MetricsHandler.find("sentinel.pass.requests.total").counter().count()).isEqualTo(1.0);
        assertThat(MetricsHandler.find("sentinel.current.threads").gauge().value()).isEqualTo(1.0);
    }
}
