package com.wosai.middleware.hera.agent.profiling;

import com.wosai.middleware.hera.agent.profiling.trace.ProfileStatusContext;
import com.wosai.middleware.hera.agent.profiling.trace.ProfileTask;
import com.wosai.middleware.hera.agent.profiling.trace.ProfileTaskChannelService;
import com.wosai.middleware.hera.agent.profiling.trace.ProfileTaskExecutionService;
import com.wosai.middleware.hera.agent.profiling.trace.TracingThreadSnapshot;
import com.wosai.middleware.hera.agent.remote.HeraGRPCChannelManager;
import com.wosai.middleware.hera.infra.HeraJUnit4Runner;
import com.wosai.middleware.hera.infra.HeraServiceManager;
import com.wosai.middleware.hera.tracing.AbstractHeraSpan;
import com.wosai.middleware.hera.tracing.EntrySpan;
import com.wosai.middleware.hera.tracing.internal.handler.MutableSpan;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.lang.reflect.InvocationTargetException;
import java.time.Duration;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.Mockito.atLeast;

@RunWith(HeraJUnit4Runner.class)
public class SnapshotGenerateTest {
    @Rule
    public MockitoRule rule = MockitoJUnit.rule();
    @Rule
    public HeraServiceManager heraServiceManager = new HeraServiceManager();
    @Captor
    private ArgumentCaptor<TracingThreadSnapshot> captor;
    @Spy
    private ProfileTaskExecutionService taskExecutionService = new ProfileTaskExecutionService();
    @Spy
    private ProfileTaskChannelService profileTaskChannelService = new ProfileTaskChannelService();

    @Before
    public void setup() throws NoSuchFieldException, IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        heraServiceManager.loadService(ProfileTaskExecutionService.class, taskExecutionService);
        heraServiceManager.loadService(ProfileTaskChannelService.class, profileTaskChannelService);
        heraServiceManager.loadService(HeraGRPCChannelManager.class, new HeraGRPCChannelManager());
        heraServiceManager.loadAll();
    }

    @Test
    public void snapshotGenerateTest() {
        ProfileTask task = new ProfileTask();
        task.setTaskId("taskId");
        task.setSpanOPName("op");
        task.setSampleTime(60);
        task.setMinDurationThreshold(0);
        task.setMaxSamplingCount(1);
        // directly send a task to be started
        taskExecutionService.startTask(task);

        MutableSpan state = new MutableSpan(0L);
        HeraSpanContext emptySpanContext = HeraSpanContext.createEmptySpanContext(0, Collections.emptyList(), "");
        AbstractHeraSpan span = new EntrySpan(null, emptySpanContext, state);
        span.context().setProfileStatus(ProfileStatusContext.createWithPending(span.getStartTimestampMillis()));

        taskExecutionService.addProfiling(span, "spanId", "op");

        await().atMost(Duration.ofSeconds(30)).untilAsserted(() -> {
            Mockito.verify(profileTaskChannelService, atLeast(1))
                    .addProfilingSnapshot(captor.capture());
            TracingThreadSnapshot snapshot = captor.getValue();
            assertThat(snapshot).isNotNull();
            assertThat(snapshot.getTaskId()).isEqualTo("taskId");
            assertThat(snapshot.getSpanId()).isEqualTo("spanId");
            assertThat(snapshot.getStackList()).isNotNull();
        });
    }
}
