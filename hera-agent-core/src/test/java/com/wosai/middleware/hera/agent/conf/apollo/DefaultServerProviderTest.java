package com.wosai.middleware.hera.agent.conf.apollo;

import com.google.common.io.CharSource;
import org.junit.Test;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

import static org.assertj.core.api.Assertions.assertThat;

public class DefaultServerProviderTest {
    @Test
    public void readConfigService() throws IOException {
        ServerProvider serverProvider = new DefaultServerProvider();
        InputStream is = CharSource.wrap("apollo.config-service=https://yahoo.jp").asByteSource(StandardCharsets.UTF_8).openStream();
        serverProvider.initialize(is);
        assertThat(serverProvider.getProperty(ApolloClient.ApolloClientSystemConsts.APOLLO_CONFIG_SERVICE, null))
                .isNotEmpty().isEqualTo("https://yahoo.jp");
    }

    @Test
    public void testNotWindows() {
        assertThat(DefaultServerProvider.isOSWindows()).isFalse();
    }
}
