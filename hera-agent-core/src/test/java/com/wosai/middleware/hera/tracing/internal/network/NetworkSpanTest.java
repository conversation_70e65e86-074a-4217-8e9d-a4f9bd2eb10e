package com.wosai.middleware.hera.tracing.internal.network;

import com.wosai.middleware.hera.tracing.network.Endpoint;
import com.wosai.middleware.hera.tracing.network.NetworkSpan;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.entry;

public class NetworkSpanTest {

    @Test
    public void buildNormalSpan() {
        NetworkSpan span = NetworkSpan.builder().name("testSpan").build();
        assertThat(span).isNotNull();
    }

    @Test
    public void tagsNonNull() {
        NetworkSpan span = NetworkSpan.builder().name("testSpan").build();
        assertThat(span.getTags()).isNotNull().isEmpty();
    }

    @Test
    public void emptyTagsWhileNoRemoteEndpoint() {
        NetworkSpan span = NetworkSpan.builder().name("testSpan").build();
        assertThat(span.convertRemoteEndpointAsSpanTags()).isNotNull().isEmpty();
    }

    @Test
    public void tagsWithRemoteIpV4Endpoint() {
        NetworkSpan span = NetworkSpan.builder().name("testSpan").remoteEndpoint(Endpoint.newBuilder().ip("127.0.0.1").port(80).build()).build();
        assertThat(span.convertRemoteEndpointAsSpanTags())
                .isNotEmpty()
                .contains(entry("peer.ipv4", "127.0.0.1"), entry("peer.port", 80));
    }

    @Test
    public void tagsWithRemoteIpV6Endpoint() {
        NetworkSpan span = NetworkSpan.builder().name("testSpan").remoteEndpoint(Endpoint.newBuilder().ip("2001:db8::8a2e:370:7334").port(80).build()).build();
        assertThat(span.convertRemoteEndpointAsSpanTags())
                .isNotEmpty()
                .contains(entry("peer.ipv6", "2001:db8::8a2e:370:7334"), entry("peer.port", 80));
    }
}
