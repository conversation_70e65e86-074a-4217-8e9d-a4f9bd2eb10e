package com.wosai.middleware.hera.agent.metrics.api;

import org.assertj.core.util.Lists;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class TagsTest {
    @Test
    public void testDelegationEqual() {
        Tags tags = Tags.of("k1", "v1");
        assertThat(tags).isNotNull();
        assertThat(tags.delegation).isNotNull();
        assertThat(tags.delegation).isEqualTo(io.micrometer.core.instrument.Tags.of("k1", "v1"));
    }

    @Test
    public void testDelegationEqual_and() {
        Tags tags = Tags.of("k1", "v1").and("k2", "v2");
        assertThat(tags).isNotNull();
        assertThat(tags.delegation).isNotNull();
        assertThat(tags.delegation).isEqualTo(io.micrometer.core.instrument.Tags.of("k1", "v1", "k2", "v2"));
    }

    @Test
    public void testEqual_and_empty() {
        Tags tags = Tags.of("k1", "v1");
        Tags otherTags = tags.and(new String[0]);
        assertThat(tags).isNotNull();
        assertThat(otherTags).isNotNull();
        assertThat(tags).isEqualTo(otherTags);
    }

    @Test
    public void testEqual_and_multiple_strings() {
        Tags tags = Tags.of("k1", "v1").and("k2", "v2", "k3", "v3");
        assertThat(tags).isNotNull();
        assertThat(tags).isEqualTo(Tags.of("k1", "v1")
                .and(Lists.list(Tag.of("k2", "v2"), Tag.of("k3", "v3"))));
    }
}
