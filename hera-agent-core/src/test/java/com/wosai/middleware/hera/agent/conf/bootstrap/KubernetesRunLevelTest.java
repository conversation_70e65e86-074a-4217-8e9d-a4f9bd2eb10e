package com.wosai.middleware.hera.agent.conf.bootstrap;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.google.common.jimfs.Configuration;
import com.google.common.jimfs.Jimfs;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.nio.file.FileSystem;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.data.MapEntry.entry;

public class KubernetesRunLevelTest {
    private KubernetesRunLevel k8sRunLevel;
    private FileSystem fs;

    @Before
    public void setup() {
        fs = Jimfs.newFileSystem(Configuration.unix());
        k8sRunLevel = new KubernetesRunLevel(fs);
    }

    @Test
    public void nonNull() {
        assertThat(k8sRunLevel).isNotNull();
    }

    @Test
    public void test_whenNoFileCreated() {
        assertThat(k8sRunLevel.shouldRun()).isFalse();
    }

    @Test
    public void test_whenOnlySATokenExists() throws IOException {
        Files.createDirectories(fs.getPath("/var/run/secrets/kubernetes.io/serviceaccount"));
        Path p = Files.createFile(fs.getPath(KubernetesRunLevel.SERVICE_ACCOUNT_TOKEN));
        Files.write(p, "eyJ".getBytes());
        assertThat(k8sRunLevel.shouldRun()).isFalse();
    }

    @Test
    public void test_whenOnlyNamespaceExists() throws IOException {
        Files.createDirectories(fs.getPath("/var/run/secrets/kubernetes.io/serviceaccount"));
        Path p = Files.createFile(fs.getPath(KubernetesRunLevel.KUBERNETES_NAMESPACE));
        Files.write(p, "sqb".getBytes());
        assertThat(k8sRunLevel.shouldRun()).isFalse();
    }

    @Test
    public void test_legacyServiceAccount() throws IOException {
        Files.createDirectories(fs.getPath("/var/run/secrets/kubernetes.io/serviceaccount"));
        Path ns = Files.createFile(fs.getPath(KubernetesRunLevel.KUBERNETES_NAMESPACE));
        Files.write(ns, "kube-system".getBytes());
        Path saToken = Files.createFile(fs.getPath(KubernetesRunLevel.SERVICE_ACCOUNT_TOKEN));
        final String token = "eyJhbGciOiJSUzI1NiIsImtpZCI6ImxlbV9FeElQekwtMEZKSGlkMlA4cnBzeW9YNXdodWtmd1FxbS1mOEl2NFEifQ.eyJpc3MiOiJrdWJlcm5ldGVzL3NlcnZpY2VhY2NvdW50Iiwia3ViZXJuZXRlcy5pby9zZXJ2aWNlYWNjb3VudC9uYW1lc3BhY2UiOiJkZWZhdWx0Iiwia3ViZXJuZXRlcy5pby9zZXJ2aWNlYWNjb3VudC9zZWNyZXQubmFtZSI6ImRlZmF1bHQtdG9rZW4tdGY4cjciLCJrdWJlcm5ldGVzLmlvL3NlcnZpY2VhY2NvdW50L3NlcnZpY2UtYWNjb3VudC5uYW1lIjoiZGVmYXVsdCIsImt1YmVybmV0ZXMuaW8vc2VydmljZWFjY291bnQvc2VydmljZS1hY2NvdW50LnVpZCI6IjMzNGIxYTAyLTU0YzYtNDUxZC1iZDY2LTQ2NzY3MWI3OTY5OCIsInN1YiI6InN5c3RlbTpzZXJ2aWNlYWNjb3VudDpkZWZhdWx0OmRlZmF1bHQifQ.LLf[ignored]";
        Files.write(saToken, token.getBytes());
        assertThat(k8sRunLevel.shouldRun()).isTrue();
        KubernetesContext ctx = k8sRunLevel.context();
        assertThat(ctx.getServiceAccountTokenProvider()).isInstanceOf(KubernetesContext.LegacyTokenProvider.class);
        assertThat(ctx.getServiceAccountTokenProvider().token()).isEqualTo(token);
        assertThat(ctx.getNamespace()).isEqualTo("kube-system");
    }

    @Test
    public void test_boundServiceAccount() throws IOException {
        Files.createDirectories(fs.getPath("/var/run/secrets/kubernetes.io/serviceaccount"));
        Path ns = Files.createFile(fs.getPath(KubernetesRunLevel.KUBERNETES_NAMESPACE));
        Files.write(ns, "kube-system".getBytes());
        Path saToken = Files.createFile(fs.getPath(KubernetesRunLevel.SERVICE_ACCOUNT_TOKEN));
        final Instant now = Instant.now();
        // expired
        final String expiredToken = createBoundToken(now.minus(60, ChronoUnit.MINUTES), now);
        Files.write(saToken, expiredToken.getBytes());
        assertThat(k8sRunLevel.shouldRun()).isTrue();
        // read the expired token
        KubernetesContext ctx = k8sRunLevel.context();
        assertThat(ctx.getServiceAccountTokenProvider()).isInstanceOf(KubernetesContext.BoundTokenProvider.class);
        assertThat(ctx.getServiceAccountTokenProvider().token()).isEqualTo(expiredToken);
        assertThat(ctx.getServiceAccountTokenProvider()).extracting("leaseDur").isEqualTo(Duration.ofHours(1));
        assertThat(ctx.getNamespace()).isEqualTo("kube-system");
        // write a new one
        final String newToken = createBoundToken(now.minus(30, ChronoUnit.MINUTES), now.plus(30, ChronoUnit.MINUTES));
        Files.write(saToken, newToken.getBytes());
        // reloaded
        assertThat(ctx.getServiceAccountTokenProvider().token()).isEqualTo(newToken);
    }

    @Test
    public void test_boundServiceAccount_reloadNotTrigger() throws IOException {
        Files.createDirectories(fs.getPath("/var/run/secrets/kubernetes.io/serviceaccount"));
        Path ns = Files.createFile(fs.getPath(KubernetesRunLevel.KUBERNETES_NAMESPACE));
        Files.write(ns, "kube-system".getBytes());
        Path saToken = Files.createFile(fs.getPath(KubernetesRunLevel.SERVICE_ACCOUNT_TOKEN));
        final Instant now = Instant.now();
        final String oldToken = createBoundToken(now.minus(45, ChronoUnit.MINUTES), now.plus(15, ChronoUnit.MINUTES));
        Files.write(saToken, oldToken.getBytes());
        assertThat(k8sRunLevel.shouldRun()).isTrue();
        // read the expired token
        KubernetesContext ctx = k8sRunLevel.context();
        assertThat(ctx.getServiceAccountTokenProvider()).isInstanceOf(KubernetesContext.BoundTokenProvider.class);
        assertThat(ctx.getServiceAccountTokenProvider().token()).isEqualTo(oldToken);
        assertThat(ctx.getServiceAccountTokenProvider()).extracting("leaseDur").isEqualTo(Duration.ofHours(1));
        assertThat(ctx.getNamespace()).isEqualTo("kube-system");
        // write a new one
        final String newToken = createBoundToken(now.minus(30, ChronoUnit.MINUTES), now.plus(30, ChronoUnit.MINUTES));
        Files.write(saToken, newToken.getBytes());
        assertThat(newToken).isNotEqualTo(oldToken);
        // reloaded
        assertThat(ctx.getServiceAccountTokenProvider().token()).isEqualTo(oldToken);
    }

    @Test
    public void testDownwardAPI_whenBothFilesExist_secondElem() throws IOException {
        Files.createDirectories(fs.getPath("/var/run/secrets/kubernetes.io/serviceaccount"));
        Path ns = Files.createFile(fs.getPath(KubernetesRunLevel.KUBERNETES_NAMESPACE));
        Files.write(ns, "kube-system".getBytes());
        Path saToken = Files.createFile(fs.getPath(KubernetesRunLevel.SERVICE_ACCOUNT_TOKEN));
        Files.write(saToken, "eyJ".getBytes());
        Files.createDirectories(fs.getPath("/etc/podinfo"));
        Path labels = Files.createFile(fs.getPath("/etc/podinfo/labels"));
        Files.write(labels, "app=\"jsonrpc4j-wosai\"\ngroup=\"ft\"".getBytes());
        assertThat(k8sRunLevel.shouldRun()).isTrue();
        assertThat(k8sRunLevel.readDownwardAPI("/opt/podinfo/labels;/etc/podinfo/labels"))
                .isNotEmpty()
                .containsOnly(entry("app", "jsonrpc4j-wosai"), entry("group", "ft"));
    }

    @Test
    public void testDownwardAPI_whenBothFilesExist_firstElem() throws IOException {
        Files.createDirectories(fs.getPath("/var/run/secrets/kubernetes.io/serviceaccount"));
        Path ns = Files.createFile(fs.getPath(KubernetesRunLevel.KUBERNETES_NAMESPACE));
        Files.write(ns, "kube-system".getBytes());
        Path saToken = Files.createFile(fs.getPath(KubernetesRunLevel.SERVICE_ACCOUNT_TOKEN));
        Files.write(saToken, "eyJ".getBytes());
        Files.createDirectories(fs.getPath("/etc/podinfo"));
        Path labels = Files.createFile(fs.getPath("/etc/podinfo/labels"));
        Files.write(labels, "app=\"jsonrpc4j-wosai\"\ngroup=\"ft\"".getBytes());
        assertThat(k8sRunLevel.shouldRun()).isTrue();
        assertThat(k8sRunLevel.readDownwardAPI("/etc/podinfo/labels;/opt/podinfo/labels;"))
                .isNotEmpty()
                .containsOnly(entry("app", "jsonrpc4j-wosai"), entry("group", "ft"));
    }

    static String createBoundToken(Instant issuedAt, Instant expiresAt) {
        return JWT.create().withIssuer("kubernetes/serviceaccount")
                .withIssuedAt(issuedAt)
                .withExpiresAt(expiresAt)
                .withClaim("kubernetes.io/serviceaccount/namespace", "sqb")
                .sign(Algorithm.none());
    }
}
