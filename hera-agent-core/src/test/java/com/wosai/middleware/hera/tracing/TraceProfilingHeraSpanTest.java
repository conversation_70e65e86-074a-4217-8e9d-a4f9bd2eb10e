package com.wosai.middleware.hera.tracing;

import com.wosai.middleware.hera.agent.profiling.trace.ProfileStatusContext;
import com.wosai.middleware.hera.infra.HeraAgentServiceRule;
import com.wosai.middleware.hera.tracing.propagation.HeraSpanContext;
import io.jaegertracing.internal.samplers.ConstSampler;
import io.opentracing.Scope;
import io.opentracing.Span;
import io.opentracing.Tracer;
import org.apache.skywalking.apm.agent.core.profile.ProfileStatus;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class TraceProfilingHeraSpanTest {
    private Tracer tracer;

    @Rule
    public HeraAgentServiceRule agentServiceRule = new HeraAgentServiceRule();

    @Before
    public void setup() {
        tracer = new HeraTracer.Builder("JaegerTester")
                .withSampler(new ConstSampler(true))
                .build();
    }

    @Test
    public void testTraceProfile_withInTheSameThread() {
        Tracer.SpanBuilder parentSpanBuilder = tracer.buildSpan("parentSpan");
        assertThat(((HeraTracer.SpanBuilder) parentSpanBuilder).profileStatusContext).isNull();

        Span parent = parentSpanBuilder.start();
        Scope scope = tracer.activateSpan(parent);
        // we have a default NONE for the first span in the current thread
        assertThat(((HeraSpanContext) parent.context()).getProfileStatus().get()).isEqualTo(ProfileStatus.NONE);

        // update parent status context
        final ProfileStatusContext ctx = ProfileStatusContext.createWithPending(1);
        ((HeraSpanContext) parent.context()).setProfileStatus(ctx);

        Tracer.SpanBuilder childSpanBuilder = tracer.buildSpan("childSpan");
        if (parent.context() != null) {
            childSpanBuilder.asChildOf(parent.context());
        }
        Span childSpan = childSpanBuilder.start();

        assertThat(((HeraTracer.SpanBuilder) childSpanBuilder).profileStatusContext).isNotNull();
        assertThat(((HeraSpanContext) childSpan.context()).getProfileStatus()).isEqualTo(ctx);
        childSpan.finish();
        scope.close();
        parent.finish();
    }

    @Test
    public void testTraceProfile_withInTheSameThread_withOutExplicitRef() {
        Tracer.SpanBuilder parentSpanBuilder = tracer.buildSpan("parentSpan");
        assertThat(((HeraTracer.SpanBuilder) parentSpanBuilder).profileStatusContext).isNull();

        Span parent = parentSpanBuilder.start();
        Scope scope = tracer.activateSpan(parent);
        // we have a default NONE for the first span in the current thread
        assertThat(((HeraSpanContext) parent.context()).getProfileStatus().get()).isEqualTo(ProfileStatus.NONE);

        // update parent status context
        final ProfileStatusContext ctx = ProfileStatusContext.createWithPending(1);
        ((HeraSpanContext) parent.context()).setProfileStatus(ctx);

        Tracer.SpanBuilder childSpanBuilder = tracer.buildSpan("childSpan");
        Span childSpan = childSpanBuilder.start();

        assertThat(((HeraTracer.SpanBuilder) childSpanBuilder).profileStatusContext).isNotNull();
        assertThat(((HeraSpanContext) childSpan.context()).getProfileStatus()).isEqualTo(ctx);
        childSpan.finish();
        scope.close();
        parent.finish();
    }

    @Test
    public void testTraceProfile_withSpanInANewThread() throws Exception {
        Tracer.SpanBuilder parentSpanBuilder = tracer.buildSpan("parentSpan");
        assertThat(((HeraTracer.SpanBuilder) parentSpanBuilder).profileStatusContext).isNull();

        Span parent = parentSpanBuilder.start();
        Scope scope = tracer.activateSpan(parent);
        // we have a default NONE for the first span in the current thread
        assertThat(((HeraSpanContext) parent.context()).getProfileStatus().get()).isEqualTo(ProfileStatus.NONE);

        // update parent status context
        final ProfileStatusContext ctx = ProfileStatusContext.createWithPending(1);
        ((HeraSpanContext) parent.context()).setProfileStatus(ctx);

        Throwable[] t = new Throwable[1];
        Thread thread = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Tracer.SpanBuilder childSpanBuilder = tracer.buildSpan("childSpan");
                    childSpanBuilder.asChildOf(parent.context());
                    final Span childSpan = childSpanBuilder.start();

                    assertThat(((HeraTracer.SpanBuilder) childSpanBuilder).profileStatusContext).isNotNull();
                    assertThat(((HeraSpanContext) childSpan.context()).getProfileStatus()).isNotNull();
                    assertThat(((HeraSpanContext) childSpan.context()).getProfileStatus().isBeingWatched()).isTrue();
                    childSpan.finish();
                } catch (AssertionError error) {
                    t[0] = error;
                }
            }
        });
        thread.start();
        thread.join();
        assertThat(t[0]).isNull();

        scope.close();
        parent.finish();
    }
}
