package com.wosai.middleware.hera.tracing.propagation;

import org.junit.Test;

import java.util.function.Supplier;

import static com.wosai.middleware.hera.tracing.propagation.SamplingFlags.DEFERRED;
import static com.wosai.middleware.hera.tracing.propagation.SamplingFlags.NOT_SAMPLED;
import static com.wosai.middleware.hera.tracing.propagation.SamplingFlags.SAMPLED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

public class TraceContextOrSamplingFlagsTest {
    HeraSpanContext context = HeraSpanContext.newBuilder().traceIdLow(333L).spanId(1L).sampled(true).build();

    TraceContextOrSamplingFlags extracted;

    @Test
    public void create_context() {
        extracted = TraceContextOrSamplingFlags.create(context);
        assertThat(extracted.context()).isSameAs(context);
        assertThat(extracted.samplingFlags()).isNull();
    }

    @Test
    public void create_samplingFlags() {
        extracted = TraceContextOrSamplingFlags.create(SAMPLED);
        assertThat(extracted.context()).isNull();
        assertThat(extracted.samplingFlags()).isSameAs(SAMPLED);
    }

    @Test
    public void sampled_get_context() {
        assertThat(TraceContextOrSamplingFlags.create(context).sampled()).isTrue();

        extracted = TraceContextOrSamplingFlags.create(context.toBuilder().sampled(null).build());
        assertThat(extracted.sampled()).isNull();
    }

    @Test
    public void sampled_get_samplingFlags() {
        assertThat(TraceContextOrSamplingFlags.create(SAMPLED).sampled()).isTrue();

        extracted = TraceContextOrSamplingFlags.create(DEFERRED);
        assertThat(extracted.sampled()).isNull();
    }

    @Test
    public void sampled_set_context() {
        extracted = TraceContextOrSamplingFlags.create(context);
        assertThat(extracted.sampled(true)).isSameAs(extracted);
        assertThat(extracted.sampled(false).sampled()).isFalse();
        assertThat(extracted.sampled(false).context().sampled()).isFalse();

        extracted = TraceContextOrSamplingFlags.create(context.toBuilder().sampled(null).build());
        assertThat(extracted.sampled(true).sampled()).isTrue();
        assertThat(extracted.sampled(false).sampled()).isFalse();
        assertThat(extracted.sampled(true).context().sampled()).isTrue();
        assertThat(extracted.sampled(false).context().sampled()).isFalse();
    }

    @Test
    public void sampled_set_samplingFlags() {
        extracted = TraceContextOrSamplingFlags.create(SAMPLED);
        assertThat(extracted.sampled(true)).isSameAs(extracted);
        assertThat(extracted.sampled(false).sampled()).isFalse();

        extracted = TraceContextOrSamplingFlags.create(NOT_SAMPLED);
        assertThat(extracted.sampled(true).sampled()).isTrue();
        assertThat(extracted.sampled(false).sampled()).isFalse();
    }

    @Test
    public void sampled_set_keepsExtra_context() {
        extracted = TraceContextOrSamplingFlags.newBuilder(context).addExtra(1L).build();
        assertThat(extracted.sampled(false).context().extra()).contains(1L);
    }

    @Test
    public void sampled_set_keepsExtra_samplingFlags() {
        extracted = TraceContextOrSamplingFlags.newBuilder(SAMPLED).addExtra(1L).build();
        assertThat(extracted.sampled(false).extra()).contains(1L);
    }

    @Test
    public void newBuilder_context() {
        extracted = TraceContextOrSamplingFlags.newBuilder(context).build();
        assertThat(extracted.context()).isSameAs(context);
        assertThat(extracted.samplingFlags()).isNull();
    }

    @Test
    public void newBuilder_samplingFlags() {
        extracted = TraceContextOrSamplingFlags.newBuilder(SAMPLED).build();
        assertThat(extracted.context()).isNull();
        assertThat(extracted.samplingFlags()).isSameAs(SAMPLED);
    }

    @Test
    public void builder_addExtra_context() {
        extracted = TraceContextOrSamplingFlags.newBuilder(context).addExtra(1L).build();
        assertThat(extracted.context().extra()).containsExactly(1L);
        assertThat(extracted.extra()).isEmpty();
    }

    @Test
    public void builder_addExtra_samplingFlags() {
        extracted = TraceContextOrSamplingFlags.newBuilder(SAMPLED).addExtra(1L).build();
        assertThat(extracted.extra()).containsExactly(1L);
    }

    @Test
    public void builder_addExtra_toExisting_context() {
        extracted = TraceContextOrSamplingFlags.newBuilder(context).addExtra(1L).build();

        extracted = extracted.toBuilder().addExtra(2L).build();
        assertThat(extracted.context().extra()).containsExactly(1L, 2L);
        assertThat(extracted.extra()).isEmpty();

        assertThatThrownBy(() -> extracted.context().extra().add(3L))
                .isInstanceOf(UnsupportedOperationException.class);
        assertThatThrownBy(() -> extracted.extra().add(3L))
                .isInstanceOf(UnsupportedOperationException.class);
    }

    @Test
    public void builder_addExtra_toExisting_samplingFlags() {
        extracted = TraceContextOrSamplingFlags.newBuilder(SAMPLED).addExtra(1L).build();

        extracted = extracted.toBuilder().addExtra(2L).build();
        assertThat(extracted.extra()).containsExactly(1L, 2L);

        assertThatThrownBy(() -> extracted.extra().add(3L))
                .isInstanceOf(UnsupportedOperationException.class);
    }

    @Test
    public void builder_addExtra_redundantIgnored_context() {
        extracted = TraceContextOrSamplingFlags.newBuilder(context).addExtra(1L).build();

        extracted = extracted.toBuilder().addExtra(1L).build();
        assertThat(extracted.context().extra()).containsExactly(1L);
        assertThat(extracted.extra()).isEmpty();
    }

    @Test
    public void builder_addExtra_redundantIgnored_samplingFlags() {
        extracted = TraceContextOrSamplingFlags.newBuilder(SAMPLED).addExtra(1L).build();

        extracted = extracted.toBuilder().addExtra(1L).build();
        assertThat(extracted.extra()).containsExactly(1L);
    }

    @Test
    public void equalsAndHashCode_context() {
        equalsAndHashCode(
                () -> TraceContextOrSamplingFlags.create(context),
                () -> TraceContextOrSamplingFlags.create(context.toBuilder().traceIdLow(111L).build()),
                () -> TraceContextOrSamplingFlags.create(SAMPLED)
        );
    }

    @Test
    public void equalsAndHashCode_samplingFlags() {
        equalsAndHashCode(
                () -> TraceContextOrSamplingFlags.create(SAMPLED),
                () -> TraceContextOrSamplingFlags.create(NOT_SAMPLED),
                () -> TraceContextOrSamplingFlags.create(context)
        );
    }

    void equalsAndHashCode(
            Supplier<TraceContextOrSamplingFlags> factory,
            Supplier<TraceContextOrSamplingFlags> differentValueFactory,
            Supplier<TraceContextOrSamplingFlags> differentTypeFactory
    ) {
        // same extracted are equivalent
        extracted = factory.get();
        assertThat(extracted).isEqualTo(extracted);
        assertThat(extracted).hasSameHashCodeAs(extracted);

        // different extracted is equivalent
        TraceContextOrSamplingFlags sameState = factory.get();
        assertThat(extracted).isEqualTo(sameState);
        assertThat(extracted).hasSameHashCodeAs(sameState);

        // different values are not equivalent
        TraceContextOrSamplingFlags differentValue = differentValueFactory.get();
        assertThat(extracted).isNotEqualTo(differentValue);
        assertThat(differentValue).isNotEqualTo(extracted);
        assertThat(extracted.hashCode()).isNotEqualTo(differentValue);

        // different extra are not equivalent
        TraceContextOrSamplingFlags withExtra = extracted.toBuilder().addExtra(1L).build();
        assertThat(extracted).isNotEqualTo(withExtra);
        assertThat(withExtra).isNotEqualTo(extracted);
        assertThat(extracted.hashCode()).isNotEqualTo(withExtra);

        // different type are not equivalent
        TraceContextOrSamplingFlags differentType = differentTypeFactory.get();
        assertThat(extracted).isNotEqualTo(differentType);
        assertThat(differentType).isNotEqualTo(extracted);
        assertThat(extracted.hashCode()).isNotEqualTo(differentType);
    }

    @Test
    public void toString_context() {
        toString(
                () -> TraceContextOrSamplingFlags.create(context),
                "Extracted{heraSpanContext=000000000000014d:0000000000000001:0:1, samplingFlags=SAMPLED_REMOTE}",
                "Extracted{heraSpanContext=000000000000014d:0000000000000001:0:1, samplingFlags=SAMPLED_REMOTE, extra=[1, 2]}"
        );
    }

    @Test
    public void toString_samplingFlags() {
        toString(
                () -> TraceContextOrSamplingFlags.create(SAMPLED),
                "Extracted{samplingFlags=SAMPLED_REMOTE}",
                "Extracted{samplingFlags=SAMPLED_REMOTE, extra=[1, 2]}"
        );
    }

    void toString(
            Supplier<TraceContextOrSamplingFlags> factory, String toString, String toStringWithExtra) {
        extracted = factory.get();
        assertThat(extracted).hasToString(toString);

        extracted = factory.get().toBuilder().addExtra(1L).addExtra(2L).build();
        assertThat(extracted).hasToString(toStringWithExtra);
    }
}
