package com.wosai.middleware.hera.agent.remote;

import com.shouqianba.middleware.hera.network.common.v1.Commands;
import com.shouqianba.middleware.hera.network.management.v1.InstancePingRequest;
import com.shouqianba.middleware.hera.network.management.v1.InstancePropertiesRequest;
import com.shouqianba.middleware.hera.network.management.v1.RxManagementServiceGrpc;
import com.wosai.middleware.hera.infra.HeraJUnit4Runner;
import io.reactivex.Single;
import io.reactivex.disposables.Disposable;
import org.apache.skywalking.apm.agent.core.boot.ServiceManager;
import org.apache.skywalking.apm.agent.core.conf.Config;
import org.apache.skywalking.apm.agent.core.logging.api.ILog;
import org.apache.skywalking.apm.agent.core.logging.api.LogManager;
import org.apache.skywalking.apm.agent.core.remote.GRPCChannelStatus;
import org.apache.skywalking.apm.agent.core.remote.ServiceManagementClient;
import org.awaitility.Awaitility;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@RunWith(HeraJUnit4Runner.class)
public class HeraServiceManagementClientTest extends AbstractGrpcTest<RxManagementServiceGrpc.ManagementServiceImplBase> {

    private static final ILog LOGGER = LogManager.getLogger(HeraServiceManagementClientTest.class);
    public volatile Map<String, String> properties = new ConcurrentHashMap<>();
    public AtomicInteger pingRequestCounter = new AtomicInteger();
    public AtomicInteger propertiesRequestCounter = new AtomicInteger();
    private Disposable disposable;
    private final RxManagementServiceGrpc.ManagementServiceImplBase testService = new RxManagementServiceGrpc.ManagementServiceImplBase() {

        @Override
        public Single<Commands> reportInstanceProperties(Single<InstancePropertiesRequest> request) {
            disposable = request.subscribe((instancePropertiesRequest, throwable) -> {
                LOGGER.info(instancePropertiesRequest.getService() + " report properties " + propertiesRequestCounter.getAndIncrement());
                instancePropertiesRequest.getPropertiesList().forEach(item -> properties.put(item.getKey(), item.getValue()));
            });
            return Single.just(Commands.getDefaultInstance());
        }

        @Override
        public Single<Commands> keepAlive(Single<InstancePingRequest> request) {
            disposable = request.subscribe((instancePingRequest, throwable) ->
                    LOGGER.info(instancePingRequest.getService() + " keep alive " + pingRequestCounter.getAndIncrement()));
            return Single.just(Commands.getDefaultInstance());
        }
    };

    @BeforeClass
    public static void beforeClass() {
        Config.Collector.GRPC_UPSTREAM_TIMEOUT = 1;
        Config.Collector.HEARTBEAT_PERIOD = 1;
        Config.Collector.PROPERTIES_REPORT_PERIOD_FACTOR = 2;
        Config.Agent.SERVICE_NAME = "hera.test";
    }

    @Before
    public void before() throws IOException, NoSuchFieldException, IllegalAccessException {
        this.register(testService);
        HeraServiceManagementClient client = (HeraServiceManagementClient) ServiceManager.INSTANCE.findService(ServiceManagementClient.class);
        client.rxManagementServiceStub = RxManagementServiceGrpc.newRxStub(this.getChannel());
        client.status = GRPCChannelStatus.CONNECTED;

        Awaitility.setDefaultTimeout(20, TimeUnit.SECONDS);
    }

    @Test
    public void testManagementMetadataSend() {
        Awaitility.await().until(() -> propertiesRequestCounter.get() > 1);
        Assert.assertTrue(properties.containsKey("JVM Version"));
    }

    @After
    public void after() throws InterruptedException {
        if (disposable.isDisposed()) {
            disposable.dispose();
        }
        Config.Collector.GRPC_UPSTREAM_TIMEOUT = 30;
        Config.Collector.HEARTBEAT_PERIOD = 30;
        Config.Collector.PROPERTIES_REPORT_PERIOD_FACTOR = 10;
        Config.Agent.SERVICE_NAME = "";
    }
}
