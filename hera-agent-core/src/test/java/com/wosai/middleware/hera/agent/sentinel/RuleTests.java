package com.wosai.middleware.hera.agent.sentinel;

import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class RuleTests {
    @Test
    public void testFlowRule() {
        com.alibaba.csp.sentinel.slots.block.flow.FlowRule sentinelRule = new com.alibaba.csp.sentinel.slots.block.flow.FlowRule();
        FlowRule rule = new FlowRule(sentinelRule);
        assertThat(rule).isNotNull().isEqualTo(sentinelRule);
        assertThat(rule.toString()).isEqualTo(sentinelRule.toString());
        assertThat(rule.hashCode()).isEqualTo(sentinelRule.hashCode());
    }

    @Test
    public void testParamFlowRule() {
        com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowRule sentinelRule = new com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowRule();
        ParamFlowRule rule = new ParamFlowRule(sentinelRule);
        assertThat(rule).isNotNull().isEqualTo(sentinelRule);
        assertThat(rule.toString()).isEqualTo(sentinelRule.toString());
        assertThat(rule.hashCode()).isEqualTo(sentinelRule.hashCode());
    }

    @Test
    public void testDegradeFlowRule() {
        com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule sentinelRule = new com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule();
        DegradeRule rule = new DegradeRule(sentinelRule);
        assertThat(rule).isNotNull().isEqualTo(sentinelRule);
        assertThat(rule.toString()).isEqualTo(sentinelRule.toString());
        assertThat(rule.hashCode()).isEqualTo(sentinelRule.hashCode());
    }
}
