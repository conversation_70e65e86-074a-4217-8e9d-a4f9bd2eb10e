package com.wosai.middleware.hera.agent.metrics.handlers;

import com.wosai.middleware.hera.agent.metrics.HandlerContext;
import com.wosai.middleware.hera.agent.metrics.MetricsHandler;
import com.wosai.middleware.hera.agent.metrics.api.AbstractBuilder;
import com.wosai.middleware.hera.tracing.internal.handler.MutableSpan;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Meter;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

public class ErrorMetricSpanHandlerTest {
    @Rule
    public MockitoRule rule = MockitoJUnit.rule();

    @Mock
    HandlerContext context;

    @Mock
    MutableSpan mutableSpan;

    private ErrorMetricSpanHandler errorMetricSpanHandler;

    @Before
    public void setup() {
        when(context.getSpan()).thenReturn(mutableSpan);
        errorMetricSpanHandler = ErrorMetricSpanHandler.builder().build();
        MetricsHandler.defaultWithInMemoryRegistry();
    }

    @After
    public void teardown() {
        AbstractBuilder.resetMeterRegistry();
    }

    @Test
    public void testHttpClient() {
        when(context.get("tag.component")).thenReturn("HttpClient");
        when(mutableSpan.tag("http.url")).thenReturn("https://www.baidu.com/");
        errorMetricSpanHandler.handle(context);
        assertThat(MetricsHandler.getMeters()).hasSize(1);
        Meter meter = MetricsHandler.getMeterRegistry().getMeters().get(0);
        assertThat(meter).isInstanceOf(Counter.class);
        Counter counter = (Counter) meter;
        assertThat(counter.getId().getTag("operation")).isEqualTo("https://www.baidu.com/");
        assertThat(counter.getId().getTag("exception")).isEqualTo("None");
        assertThat(counter.count()).isEqualTo(1L);
    }

    @Test
    public void testHttpClient_givenNoHttpUrl() {
        when(context.get("tag.component")).thenReturn("HttpClient");
        when(mutableSpan.tag("http.url")).thenReturn(null);
        errorMetricSpanHandler.handle(context);
        assertThat(MetricsHandler.getMeters()).hasSize(1);
        Meter meter = MetricsHandler.getMeterRegistry().getMeters().get(0);
        assertThat(meter).isInstanceOf(Counter.class);
        Counter counter = (Counter) meter;
        assertThat(counter.getId().getTag("operation")).isEqualTo("UNKNOWN");
        assertThat(counter.getId().getTag("exception")).isEqualTo("None");
        assertThat(counter.count()).isEqualTo(1L);
    }
}
