package com.wosai.middleware.hera.agent.profiling;

import com.wosai.middleware.hera.agent.profiling.async.AsyncProfileTask;
import com.wosai.middleware.hera.agent.profiling.async.AsyncProfileTaskExecutionService;
import jdk.jfr.consumer.RecordedEvent;
import jdk.jfr.consumer.RecordingFile;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.Mockito.times;

public class JfrGenerateTest {
    @Rule
    public MockitoRule rule = MockitoJUnit.rule();
    @Captor
    private ArgumentCaptor<File> fileCaptor;
    @Captor
    private ArgumentCaptor<AsyncProfileTask> taskCaptor;

    @Spy
    private AsyncProfileTaskExecutionService taskExecutionService;

    @Test
    public void jfrGenerateTest() {
        AsyncProfileTask task = new AsyncProfileTask();
        task.setTaskId("uid");
        task.setFormat("jfr");
        task.setOperation("event=cpu,lock=10ms");
        task.setSampleTime(1);
        task.setCreateTime(System.currentTimeMillis());
        taskExecutionService.startTask(task);

        await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
            Mockito.verify(taskExecutionService, times(1))
                    .uploadAsyncProfilerResultFile(fileCaptor.capture(), taskCaptor.capture());
            File file = fileCaptor.getValue();
            Path path = Paths.get(file.getPath());
            List<RecordedEvent> recordedEvents = RecordingFile.readAllEvents(path);
            assertThat(recordedEvents).isNotNull();
        });
    }
}
