######################### DEFINITIONS ############################
MVN ?= mvn
JF ?= jf
CI ?= false
SKIP_CI ?= false
MVN_FLAGS ?= --batch-mode --errors --fail-fast --show-version -DinstallAtEnd=true -DdeployAtEnd=true $(EXTRA_MVN_FLAGS)
JF_FLAGS ?= --build-name=${CI_PROJECT_NAME} --build-number=${CI_PIPELINE_ID} ${MAVEN_CLI_OPTS}
HUB ?= jfrog.wosai-inc.com/docker-virtual-dev/hera
HERA_ROOT := $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST))))
CONTEXT ?= ${HERA_ROOT}/dist
DOCKER_MIRROR ?= jfrog.wosai-inc.com/docker-virtual-prod/ft-mirror/
DOCKER_BUILD_TOP := ${CONTEXT}/docker_build

######################### VERSION PARSING ############################
POM_VERSION := $(shell xmllint --xpath "/*[local-name()='project']/*[local-name()='version']/text()" pom.xml)

# if build for non-SNAPSHOT version, skip tests
ifeq (,$(findstring -SNAPSHOT, $(POM_VERSION)))
	EXTRA_MVN_FLAGS := -DskipTests
endif

# https://dev.to/eugenebabichenko/generating-pretty-version-strings-including-nightly-with-git-and-makefiles-48p3
TAG_COMMIT := $(shell git rev-list --abbrev-commit --tags --max-count=1)
# `2>/dev/null` suppress errors and `|| true` suppress the error codes.
TAG := $(shell git describe --abbrev=0 --tags ${TAG_COMMIT} 2>/dev/null || true)
# here we strip the version prefix
VERSION := $(TAG:v%=%)
DATE := $(shell git log -1 --format=%cd --date=format:"%Y%m%d")
# get the latest commit hash in the short form
COMMIT := $(shell git rev-parse --short HEAD)
ifneq ($(COMMIT), $(TAG_COMMIT))
    VERSION := $(VERSION)-next-$(COMMIT)-$(DATE)
ifeq (,$(findstring -SNAPSHOT, $(POM_VERSION)))
	SKIP_CI = true
endif
endif
# check if the version string is empty
ifeq ($(VERSION),)
    VERSION := $(COMMIT)-$(DATE)
endif
# git status --porcelain outputs a machine-readable text and the output is empty
# if the working tree is clean
ifneq ($(shell git status --porcelain),)
    VERSION := $(VERSION)-dirty
endif

######################### METADATA ############################

# Write IMAGE VERSION to deploy.env
.PHONY: write-deploy-env
write-deploy-env:
	@echo "IMAGE_VERSION=$(VERSION)" > deploy.env
	@echo "HUB=$(HUB)" >> deploy.env

######################### MAVEN ############################

%.install: TARGET = install
%.deploy: TARGET = deploy

maven.%:
ifeq ($(SKIP_CI),true)
	@echo "Skip CI since tag does not exist for this release version"
	exit 127
else
	@ $(MVN) $(MVN_FLAGS) clean verify $(TARGET)
endif

jf.maven.%:
ifeq ($(SKIP_CI),true)
	@echo "Skip CI since tag does not exist for this release version"
	exit 127
else
	@ $(JF) $(MVN) $(MVN_FLAGS) clean verify $(TARGET) $(JF_FLAGS)
endif

######################### DOCKER ############################

.PHONY: docker.all docker.push

DOCKER_TARGETS:=docker.jdk8 docker.jdk17 docker.jdk21 docker.jetty9 docker.jetty9-jdk17 docker.jetty9-jdk21
BUILD_ARGS := $(BUILD_ARGS) --build-arg BUILD_DATE=`date -u +"%Y-%m-%dT%H:%M:%SZ"` --build-arg VCS_REF=$(COMMIT) --build-arg VERSION=$(POM_VERSION)

%.jdk8: NAME = jdk8
%.jdk8: VARIANT = ubuntu
%.jdk8: BASE_IMAGE = eclipse-temurin:8u442-b06-jdk-jammy
%.jdk17: NAME = jdk17
%.jdk17: VARIANT = ubuntu
%.jdk17: BASE_IMAGE = eclipse-temurin:17.0.14_7-jdk-jammy
%.jdk17: TAG_SUFFIX = -jdk17
%.jdk21: NAME = jdk21
%.jdk21: VARIANT = ubuntu
%.jdk21: BASE_IMAGE = eclipse-temurin:21-jdk-jammy
%.jdk21: TAG_SUFFIX = -jdk21
%.jetty9: NAME = jetty9
%.jetty9: VARIANT = jetty9
%.jetty9: BASE_IMAGE = jetty:9.4-jdk8-eclipse-temurin
%.jetty9: TAG_PREFIX = jetty9-
%.jetty9-jdk17: NAME = jetty9-jdk17
%.jetty9-jdk17: VARIANT = jetty9
%.jetty9-jdk17: BASE_IMAGE = jetty:9.4-jdk17-eclipse-temurin
%.jetty9-jdk17: TAG_PREFIX = jetty9-
%.jetty9-jdk17: TAG_SUFFIX = -jdk17
%.jetty9-jdk21: NAME = jetty9-jdk21
%.jetty9-jdk21: VARIANT = jetty9
%.jetty9-jdk21: BASE_IMAGE = jetty:9.4-jdk21-eclipse-temurin
%.jetty9-jdk21: TAG_PREFIX = jetty9-
%.jetty9-jdk21: TAG_SUFFIX = -jdk21

docker.%: PLATFORMS =
docker.%: LOAD_OR_PUSH = --load
push.%: PLATFORMS = --platform linux/amd64,linux/arm64
push.%: LOAD_OR_PUSH = --push

docker.% push.docker.%: $(HERA_ROOT)/target ci
	$(DOCKER_RULE)

docker.all: $(DOCKER_TARGETS)
docker.push: $(DOCKER_TARGETS:%=push.%)

# $^ the name of the dependencies for the target
# Rule Steps #
##############
# 1. Create an intermediate container to be used as the builder since the normal Docker daemon does not support multiple manifests,
# 2. Run docker buildx build and push or load the images according to the commands,
# 3. Delete the builder container

define DOCKER_RULE
	mkdir -p $(DOCKER_BUILD_TOP)/$(NAME)
	cp -r $^ $(DOCKER_BUILD_TOP)/$(NAME)
	docker buildx create --use --driver docker-container --name hera_buildkit_$(NAME) > /dev/null 2>&1 || true
	docker buildx build $(PLATFORMS) $(LOAD_OR_PUSH) \
	    --metadata-file buildx-metadata-$(NAME) \
		--no-cache $(BUILD_ARGS) --build-arg BASE_IMAGE=$(DOCKER_MIRROR)$(BASE_IMAGE) \
		-t $(HUB):$(TAG_PREFIX)$(VERSION)$(TAG_SUFFIX) \
		-f $(HERA_ROOT)/docker/Dockerfile.$(VARIANT) \
		$(DOCKER_BUILD_TOP)/$(NAME)
endef
