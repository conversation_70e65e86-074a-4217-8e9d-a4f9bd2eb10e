BASE_IMG ?= eclipse-temurin:8-jdk
HUB ?= jfrog.wosai-inc.com/docker-virtual-dev/middleware/
IMAGE_TAG = $(shell git rev-parse HEAD)

.PHONY: docker-build
docker-build: docker-build-provider docker-build-consumer

.PHONY: maven-build
maven-build:
	mvn clean package

.PHONY: docker-build-provider
docker-build-provider: maven-build
	docker build --build-arg TARGET=./dubbo-jsonrpc-demo-provider/target/dubbo-jsonrpc-demo-provider.jar --build-arg BASE_IMG=$(BASE_IMG) -t $(HUB)dubbo-jsonrpc-demo-provider:$(IMAGE_TAG) .

.PHONY: docker-build-consumer
docker-build-consumer: maven-build
	docker build --build-arg TARGET=./dubbo-jsonrpc-demo-consumer/target/dubbo-jsonrpc-demo-consumer.jar --build-arg BASE_IMG=$(BASE_IMG) -t $(HUB)dubbo-jsonrpc-demo-consumer:$(IMAGE_TAG) .
