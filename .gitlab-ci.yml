variables:
  MAVEN_OPTS: "-Dhttps.protocols=TLSv1.2 -Dmaven.repo.local=/opt/cache/$CI_PROJECT_NAME/.m2/repository -Xmx2048m -Xms1024m"
  JFROG_CLI_TEMP_DIR: "/opt/cache"
  GIT_FETCH_EXTRA_FLAGS: --tags
  DOCKER_BUILDER_SHA256: 1cd2e00f872000282745f06d3c046065fc78fcb903469ca760dc5797cb2814db
  MAVEN_BUILDER_SHA256: 1f6b4902c508d5dcf98e076bdd219c5d8a8cc569c5d1623f988e2154adfc1a51

stages:
  - build
  - build-docker
  - push-buildinfo
  - test

.build:
  stage: build
  image: jfrog.wosai-inc.com/docker-virtual-prod/middleware/maven-builder:3.9.9-eclipse-temurin-17-focal@sha256:${MAVEN_BUILDER_SHA256}
  before_script:
    - jfrog c add jfrog-default --artifactory-url=$ARTIFACTORY_URL --user=$JFROG_USER --password=$JFROG_PASS || true
    - jfrog c show
    - jfrog mvnc --repo-deploy-snapshots=${ARTIFACTORY_REPO} --repo-deploy-releases=${ARTIFACTORY_REPO} --repo-resolve-releases=${ARTIFACTORY_REPO} --repo-resolve-snapshots=${ARTIFACTORY_REPO} --server-id-deploy=jfrog-default --server-id-resolve=jfrog-default
  script:
    - JF=jfrog make jf.maven.install
    # Collect environment variables
    - jfrog rt bce ${CI_PROJECT_NAME} ${CI_PIPELINE_ID}
    # Collect Git Info
    - jfrog rt bag ${CI_PROJECT_NAME} ${CI_PIPELINE_ID}
    - cat hera-docs/target/site/jacoco-aggregate/index.html
  artifacts:
    when: always
    paths:
      - target/hera-agent
    reports:
      junit: "**/target/surefire-reports/TEST-*.xml"
  tags:
    - ft-k8s

build-branches:
  extends: .build
  variables:
    ARTIFACTORY_REPO: maven-virtual-dev
    JFROG_USER: $ARTIFACTORY_USER
    JFROG_PASS: $ARTIFACTORY_PASS
  only:
    - branches

build-tags:
  extends: .build
  variables:
    ARTIFACTORY_REPO: maven-virtual-staging
    JFROG_USER: $ARTIFACTORY_USER_STAGING
    JFROG_PASS: $ARTIFACTORY_PASS_STAGING
  only:
    - tags

.build-docker:
  stage: build-docker
  image: jfrog.wosai-inc.com/docker-virtual-prod/middleware/docker-builder:20.10.24@sha256:${DOCKER_BUILDER_SHA256}
  before_script:
    - docker run --privileged --rm tonistiigi/binfmt --install all
    - jfrog c add jfrog-default --artifactory-url=$ARTIFACTORY_URL --user=$JFROG_USER --password=$JFROG_PASS || true
    - docker login jfrog.wosai-inc.com --username $JFROG_USER --password $JFROG_PASS
    - jfrog c show
  script:
    - HUB=${HUB} make push.docker.${TARGET}
    # Attach build info
    - jfrog rt bdc ${DOCKER_REPO} --image-file buildx-metadata-${TARGET} --build-name ${CI_PROJECT_NAME} --build-number ${CI_PIPELINE_ID}
  after_script:
    - docker buildx rm hera_buildkit_${TARGET}
  parallel:
    matrix:
      - TARGET: ["jdk8", "jdk17", "jdk21", "jetty9", "jetty9-jdk17", "jetty9-jdk21"]
  tags:
    - ft-k8s

build-docker-branches:
  extends: .build-docker
  variables:
    DOCKER_REPO: docker-virtual-dev
    HUB: jfrog.wosai-inc.com/${DOCKER_REPO}/hera
    JFROG_USER: $ARTIFACTORY_USER
    JFROG_PASS: $ARTIFACTORY_PASS
  except:
    - master
    - tags

build-docker-master:
  extends: .build-docker
  variables:
    DOCKER_REPO: docker-virtual-dev
    HUB: jfrog.wosai-inc.com/${DOCKER_REPO}/hera-master
    JFROG_USER: $ARTIFACTORY_USER
    JFROG_PASS: $ARTIFACTORY_PASS
  only:
    - master

build-docker-tags:
  extends: .build-docker
  variables:
    DOCKER_REPO: docker-virtual-staging
    HUB: jfrog.wosai-inc.com/${DOCKER_REPO}/hera
    JFROG_USER: $ARTIFACTORY_USER_STAGING
    JFROG_PASS: $ARTIFACTORY_PASS_STAGING
  only:
    - tags

.push-buildinfo:
  stage: push-buildinfo
  image: jfrog.wosai-inc.com/docker-virtual-prod/middleware/docker-builder:20.10.24@sha256:${DOCKER_BUILDER_SHA256}
  before_script:
    - jfrog c add jfrog-default --artifactory-url=$ARTIFACTORY_URL --user=$JFROG_USER --password=$JFROG_PASS || true
    - jfrog c show
  script:
    - jfrog rt bp ${CI_PROJECT_NAME} ${CI_PIPELINE_ID}
    - make write-deploy-env
  artifacts:
    reports:
      dotenv: deploy.env
  tags:
    - ft-k8s

push-buildinfo-branches:
  extends: .push-buildinfo
  variables:
    DOCKER_REPO: docker-virtual-dev
    HUB: jfrog.wosai-inc.com/${DOCKER_REPO}/hera
    JFROG_USER: $ARTIFACTORY_USER
    JFROG_PASS: $ARTIFACTORY_PASS
  except:
    - master
    - tags

push-buildinfo-master:
  extends: .push-buildinfo
  variables:
    DOCKER_REPO: docker-virtual-dev
    HUB: jfrog.wosai-inc.com/${DOCKER_REPO}/hera-master
    JFROG_USER: $ARTIFACTORY_USER
    JFROG_PASS: $ARTIFACTORY_PASS
  only:
    - master

push-buildinfo-tags:
  extends: .push-buildinfo
  variables:
    DOCKER_REPO: docker-virtual-staging
    HUB: jfrog.wosai-inc.com/${DOCKER_REPO}/hera
    JFROG_USER: $ARTIFACTORY_USER_STAGING
    JFROG_PASS: $ARTIFACTORY_PASS_STAGING
  only:
    - tags

test:
  stage: test
  inherit:
    variables: false
  variables:
    HERA_IMAGE_VERSION: $IMAGE_VERSION
    HUB: $HUB
  trigger:
    project: middleware/hera-test-suites
    branch: master 
    strategy: depend
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_BRANCH
