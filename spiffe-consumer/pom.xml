<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.shouqianba.middleware</groupId>
		<artifactId>dubbo-jsonrpc-demo</artifactId>
		<version>1.1.4-SNAPSHOT</version>
	</parent>
	<groupId>com.example</groupId>
	<artifactId>spiffe-consumer</artifactId>
	<version>1.1.4-SNAPSHOT</version>
	<name>consumer</name>
	<description>Demo project for Spring Boot</description>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.springframework.boot</groupId>-->
<!--			<artifactId>spring-boot-starter-thymeleaf</artifactId>　　　　-->
<!--		</dependency>-->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
		<dependency>
			<groupId>io.spiffe</groupId>
			<artifactId>java-spiffe-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>io.spiffe</groupId>-->
<!--			<artifactId>grpc-netty-macos-aarch64</artifactId>-->
<!--			<version>0.8.4</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>io.spiffe</groupId>
			<artifactId>grpc-netty-linux</artifactId>
			<version>0.8.4</version>
		</dependency>
		<dependency>
			<groupId>com.wosai.middleware</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>0.15.0</version>
		</dependency>
		<dependency>
			<groupId>com.wosai.middleware</groupId>
			<artifactId>vault-sdk</artifactId>
			<version>0.15.0</version>
		</dependency>
	</dependencies>

	<build>
		<finalName>spiffe-consumer</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
							<goal>build-info</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
