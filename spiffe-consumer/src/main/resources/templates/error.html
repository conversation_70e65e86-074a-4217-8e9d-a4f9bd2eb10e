<!DOCTYPE html>
<html lang="en"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <link rel="stylesheet" type="text/css" media="all" href="../../css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" />
    <script type="text/javascript" th:src="@{/js/jquery.min.js}" src="js/jquery.min.js"></script>
    <script type="text/javascript" th:src="@{/js/bootstrap.min.js}" src="js/bootstrap.min.js"></script>
</head>
<body>
<div class="container alert">
    <div class="alert-danger">
        <h1><strong>Oops... something went wrong.</strong></h1>
    </div>
    <h2>Error: </h2>
    <h3 th:text="${error}"></h3>
</div>
</body>
</html>
