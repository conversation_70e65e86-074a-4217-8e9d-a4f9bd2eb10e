package com.springcloud.demo.consumer.controller;

import com.springcloud.demo.api.ISayService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;


@RestController
public class HelloController {

    @DubboReference(version = "1.0.1")
    private ISayService iSayService;

    @GetMapping("/say/{something}")
    public String say(@PathVariable("something") String something) {
        return iSayService.saySomething(something);
    }
}
