server:
  port: 8080

spring:
  application:
    name: dubbo-ycj-consumer
  cloud:
    nacos:
      discovery:
        server-addr: ***************:30848

dubbo:
  scan:
    base-packages: com.springcloud.demo.consumer
  application:
    name: dubbo-ycj-consumer
  protocol:
    port: -1
  registry:
    address: nacos://***************:30848?username=nacos&password=nacos
  metadata-report:
    address: nacos://***************:30848?username=nacos&password=nacos
  consumer:
    check: false


