syntax = "proto3";

package hera.management.v1;

import "hera/common/v1/common.proto";

option java_multiple_files = true;
option java_package = "com.shouqianba.middleware.hera.network.management.v1";

// Define the service reporting the extra information of the instance.
service ManagementService {
  // Report custom properties of a service instance.
  rpc reportInstanceProperties (InstancePropertiesRequest) returns (common.v1.Commands) {
  }

  // Keep the instance alive in the backend analysis.
  // Only recommend to do separate keepAlive report when no trace and metrics needs to be reported.
  // Otherwise, it is duplicated.
  rpc keepAlive (InstancePingRequest) returns (common.v1.Commands) {

  }
}

message InstancePropertiesRequest {
  string service = 1;
  string serviceInstance = 2;
  repeated common.v1.KeyValuePair properties = 3;
}

message InstancePingRequest {
  string service = 1;
  string serviceInstance = 2;
}