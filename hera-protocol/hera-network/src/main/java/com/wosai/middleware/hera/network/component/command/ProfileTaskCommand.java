package com.wosai.middleware.hera.network.component.command;

import com.shouqianba.middleware.hera.network.common.v1.Command;
import com.shouqianba.middleware.hera.network.common.v1.KeyValuePair;
import lombok.Getter;

import java.util.List;

@Getter
public class ProfileTaskCommand extends BaseTaskCommand implements GenericDeserializable<ProfileTaskCommand> {
    public static final GenericDeserializable<ProfileTaskCommand> DESERIALIZER =
            new ProfileTaskCommand(CommandExecutionContext.noop(), "", "", "", 0, 0, 0, 0, 0, 0);
    public static final String NAME = "ProfileTaskQuery";

    // profile task data
    private final String endpointName;
    private final int minDurationThreshold;
    private final int dumpPeriod;
    private final int maxSamplingCount;
    private final long startTime;

    public ProfileTaskCommand(CommandExecutionContext context, String serialNumber, String taskId, String endpointName, int sampleTime,
                              int minDurationThreshold, int dumpPeriod, int maxSamplingCount, long startTime, long createTime) {
        super(context, NAME, serialNumber, taskId, createTime, sampleTime);
        this.endpointName = endpointName;
        this.minDurationThreshold = minDurationThreshold;
        this.dumpPeriod = dumpPeriod;
        this.maxSamplingCount = maxSamplingCount;
        this.startTime = startTime;
    }

    @Override
    public ProfileTaskCommand deserialize(CommandExecutionContext context, Command command) {
        final List<KeyValuePair> argsList = command.getArgsList();
        String serialNumber = null;
        String taskId = null;
        String endpointName = null;
        int sampleTime = 0;
        int minDurationThreshold = 0;
        int dumpPeriod = 0;
        int maxSamplingCount = 0;
        long startTime = 0;
        long createTime = 0;

        for (final KeyValuePair pair : argsList) {
            if (BaseCommand.SERIAL_NUMBER.equals(pair.getKey())) {
                serialNumber = pair.getValue();
            } else if (BaseCommand.ENDPOINT_NAME.equals(pair.getKey())) {
                endpointName = pair.getValue();
            } else if (BaseCommand.TASK_ID.equals(pair.getKey())) {
                taskId = pair.getValue();
            } else if (BaseCommand.SAMPLE_TIME.equals(pair.getKey())) {
                sampleTime = Integer.parseInt(pair.getValue());
            } else if (BaseCommand.MIN_DURATION_THRESHOLD.equals(pair.getKey())) {
                minDurationThreshold = Integer.parseInt(pair.getValue());
            } else if (BaseCommand.DUMP_PERIOD.equals(pair.getKey())) {
                dumpPeriod = Integer.parseInt(pair.getValue());
            } else if (BaseCommand.MAX_SAMPLING_COUNT.equals(pair.getKey())) {
                maxSamplingCount = Integer.parseInt(pair.getValue());
            } else if (BaseCommand.START_TIME.equals(pair.getKey())) {
                startTime = Long.parseLong(pair.getValue());
            } else if (BaseCommand.CREATE_TIME.equals(pair.getKey())) {
                createTime = Long.parseLong(pair.getValue());
            }
        }

        return new ProfileTaskCommand(context, serialNumber, taskId, endpointName, sampleTime, minDurationThreshold, dumpPeriod, maxSamplingCount, startTime, createTime);
    }
}