package com.wosai.middleware.hera.network.component.command;

import io.netty.util.AttributeKey;
import lombok.Getter;

import javax.annotation.Nullable;

public abstract class BaseCommand implements CommandExecutionContextAccessor {
    @Getter
    private final String command;
    @Getter
    private final String serialNumber;
    private final CommandExecutionContext delegation;

    // common
    protected static final String SERIAL_NUMBER = "SerialNumber";

    // profile common
    protected static final String TASK_ID = "TaskId";
    public static final String CREATE_TIME = "CreateTime";
    protected static final String SAMPLE_TIME = "SampleTime";

    // trace
    protected static final String ENDPOINT_NAME = "EndpointName";
    protected static final String MIN_DURATION_THRESHOLD = "MinDurationThreshold";
    protected static final String DUMP_PERIOD = "DumpPeriod";
    protected static final String MAX_SAMPLING_COUNT = "MaxSamplingCount";
    protected static final String START_TIME = "StartTime";

    // async
    protected static final String OPERATION = "Operation";
    protected static final String FORMAT = "Format";

    // remote command
    protected static final String REMOTE_COMMAND = "RemoteCommand";

    // configuration discovery
    public static final String UUID_CONST_NAME = "UUID";

    BaseCommand(CommandExecutionContext context, String command, String serialNumber) {
        this.delegation = context;
        this.command = command;
        this.serialNumber = serialNumber;
    }

    @Nullable
    @Override
    public <V> V setAttr(AttributeKey<V> key, @Nullable V value) {
        return delegation.setAttr(key, value);
    }

    @Nullable
    @Override
    public <V> V attr(AttributeKey<V> key) {
        return delegation.attr(key);
    }
}
