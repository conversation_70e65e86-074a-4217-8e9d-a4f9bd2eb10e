package com.wosai.middleware.hera.network.component.command;

import io.netty.util.Attribute;
import io.netty.util.AttributeKey;
import io.netty.util.AttributeMap;
import io.netty.util.DefaultAttributeMap;

import javax.annotation.Nullable;

import static java.util.Objects.requireNonNull;

public class CommandExecutionContext implements CommandExecutionContextAccessor {
    private static final CommandExecutionContext EMPTY = new CommandExecutionContext(new AttributeMap() {
        @Override
        public <T> Attribute<T> attr(AttributeKey<T> key) {
            return null;
        }

        @Override
        public <T> boolean hasAttr(AttributeKey<T> key) {
            return false;
        }
    });
    private final AttributeMap attributeMap;

    private CommandExecutionContext() {
        this(new DefaultAttributeMap());
    }

    private CommandExecutionContext(AttributeMap attributeMap) {
        this.attributeMap = attributeMap;
    }

    @Nullable
    public <V> V setAttr(AttributeKey<V> key, @Nullable V value) {
        requireNonNull(key, "key");
        Attribute<V> attr = this.attributeMap.attr(key);
        if (attr == null) {
            return null;
        }
        return attr.getAndSet(value);
    }

    @Nullable
    public <V> V attr(AttributeKey<V> key) {
        requireNonNull(key, "key");
        Attribute<V> attr = this.attributeMap.attr(key);
        if (attr == null) {
            return null;
        }
        return attr.get();
    }

    public static CommandExecutionContext create() {
        return new CommandExecutionContext();
    }

    public static CommandExecutionContext noop() {
        return EMPTY;
    }
}
