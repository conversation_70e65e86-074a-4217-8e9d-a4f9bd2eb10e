FROM jfrog.wosai-inc.com/docker-virtual-staging/hera:1.7.2

# 时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
  && echo Asia/Shanghai > /etc/timezone \
  && dpkg-reconfigure -f noninteractive tzdata

WORKDIR /app/bin

COPY target/dubbo-spring-cloud-demo-provider.jar ./app.jar

# 日志目录
VOLUME /app/log

EXPOSE 8080

CMD ["java","-jar", "/app/bin/app.jar"]
# CMD ["java", "-javaagent:/app/bin/zeus-agent.jar=spring.application.name=jsonrpc4j-wosai-service" ,"-jar", "/app/bin/app.jar"]