<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.shouqianba.middleware</groupId>
	<artifactId>dubbo-jsonrpc-demo</artifactId>
	<version>1.1.4-SNAPSHOT</version>
	<packaging>pom</packaging>

	<name>dubbo-jsonrpc-demo</name>
	<modules>
		<module>dubbo-jsonrpc-demo-api</module>
		<module>dubbo-jsonrpc-demo-provider</module>
		<module>dubbo-jsonrpc-demo-consumer</module>
		<module>dubbo-jsonrpc-demo-service</module>
		<module>dubbo-samples-xds-provider</module>
		<module>dubbo-samples-xds-consumer</module>
		<module>spiffe-consumer</module>
		<module>spiffe-provider</module>
        <module>dubbo-spring-cloud-demo-api</module>
        <module>dubbo-spring-cloud-demo-provider</module>
		<module>dubbo-spring-cloud-demo-consumer</module>
        <module>dubbo-jsonrpc-mcp</module>
        <module>do-apisix-mcp-server</module>
    </modules>

	<properties>
		<dubbo.version>3.3.1</dubbo.version>
		<dubbo.wosai.version>1.0.5-SNAPSHOT</dubbo.wosai.version>
		<nacos.version>3.0.1</nacos.version>
		<spring-boot.version>2.7.8</spring-boot.version>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<dubbo.rpc.http.version>1.0.2-SNAPSHOT</dubbo.rpc.http.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- Spring Boot -->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-web</artifactId>
				<version>${spring-boot.version}</version>
			</dependency>

			<!-- Dubbo -->
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-bom</artifactId>
				<version>${dubbo.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!-- Nacos -->
			<dependency>
				<groupId>com.alibaba.nacos</groupId>
				<artifactId>nacos-client</artifactId>
				<version>${nacos.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.dubbo.extensions</groupId>
				<artifactId>dubbo-remoting-http</artifactId>
				<version>${dubbo.version}</version>
			</dependency>
<!--			<dependency>-->
<!--				<groupId>org.apache.dubbo.extensions</groupId>-->
<!--				<artifactId>dubbo-xds</artifactId>-->
<!--				<version>${dubbo.version}</version>-->
<!--			</dependency>-->
			<dependency>
				<groupId>com.wosai.middleware</groupId>
				<artifactId>dubbo-rpc-http</artifactId>
				<version>${dubbo.wosai.version}</version>
			</dependency>
			<dependency>
				<groupId>com.wosai.middleware</groupId>
				<artifactId>dubbo-xds</artifactId>
				<version>${dubbo.wosai.version}</version>
			</dependency>
			<dependency>
				<groupId>com.wosai.middleware</groupId>
				<artifactId>dubbo-grpc-xds</artifactId>
				<version>${dubbo.wosai.version}</version>
			</dependency>
			<dependency>
				<groupId>io.spiffe</groupId>
				<artifactId>java-spiffe-core</artifactId>
				<version>0.8.4</version>
			</dependency>

<!--			<dependency>-->
<!--				<groupId>io.spiffe</groupId>-->
<!--				<artifactId>grpc-netty-macos</artifactId>-->
<!--				<version>0.8.4</version>-->
<!--			</dependency>-->

			<dependency>
				<groupId>jakarta.persistence</groupId>
				<artifactId>jakarta.persistence-api</artifactId>
				<version>2.2.3</version>
			</dependency>


		</dependencies>
	</dependencyManagement>


	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-maven-plugin</artifactId>
					<version>2.3.12.RELEASE</version>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

	<distributionManagement>
		<repository>
			<id>central</id>
			<name>maven-virtual-dev</name>
			<url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
		</repository>
		<snapshotRepository>
			<id>snapshots</id>
			<name>maven-virtual-dev</name>
			<url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
		</snapshotRepository>
	</distributionManagement>
</project>
