package com.shouqianba.middleware.jsonrpc.service;

import com.wosai.web.rpc.EnableJsonRpc;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

@EnableJsonRpc
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableDubbo(scanBasePackages = "com.shouqianba.middleware")
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
