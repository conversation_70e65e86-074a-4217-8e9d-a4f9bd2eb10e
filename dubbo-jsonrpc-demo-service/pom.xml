<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.shouqianba.middleware</groupId>
        <artifactId>dubbo-jsonrpc-demo</artifactId>
        <version>1.1.4-SNAPSHOT</version>
    </parent>

    <groupId>com.shouqianba.middleware</groupId>
    <artifactId>dubbo-jsonrpc-demo-service</artifactId>
    <packaging>jar</packaging>
    <name>dubbo-jsonrpc-demo-service</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <!--api-->
        <dependency>
            <groupId>com.shouqianba.middleware</groupId>
            <artifactId>dubbo-jsonrpc-demo-api</artifactId>
            <version>1.1.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-web-rpc</artifactId>
            <version>2.0.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.0.2.Final</version>
        </dependency>
        <!-- dubbo -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>

        <!-- spring boot starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>dubbo-rpc-http</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo.extensions</groupId>
            <artifactId>dubbo-remoting-http</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>dubbo-jsonrpc-demo-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
